{"name": "legapauperadriatica.com", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "create-admin": "ts-node scripts/create-admin-user.ts"}, "dependencies": {"@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.1", "@tanstack/react-query": "^5.68.0", "@types/react-window": "^1.8.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.479.0", "next": "^15.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.6.0", "react-window": "^1.8.11", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.4.5", "eslint": "^9", "eslint-config-next": "15.2.2", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5"}}