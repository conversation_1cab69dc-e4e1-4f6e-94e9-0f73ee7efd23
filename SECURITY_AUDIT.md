# Security Audit — Le<PERSON>uper <PERSON> (Next.js + Supabase)

Date: 2025-08-27
Auditor: Agent <PERSON> (senior security auditor AI)
Scope: Next.js 15 + TS + Tailwind app with Supabase backend, production context


## Summary of Findings

- Critical privilege escalation: tournaments policy allows non-admins to write tournaments via a permissive validation policy.
- PII exposure: players is publicly readable (includes email), contrary to policy that emails should be visible only to self and admins.
- Integrity gap: tournament_registrations updates/deletes are time-gated only in client code, not at the DB layer.
- Public registrations: by design, tournament_registrations are globally readable (including decklist URL/content); accepted by product owner.
- SECURITY DEFINER RPC hardening: several functions lack a constrained search_path; flagged by advisors.
- Optional hardening: security headers/CSP; CSRF origin check for signout; confirmation of magic-link redirect path.

Verdict: ❌ Unsafe (critical privilege escalation on tournaments + PII exposure).


## Threat Model (high-level)

- Principals: anonymous, authenticated users, admins.
- Trust boundaries: <PERSON>rows<PERSON> (untrusted), Next server runtime (trusted), Supabase PostgREST (enforces RLS), Postgres (RLS + RPC), Storage (not used for uploads here).
- Data classes: PII (players.email, first/last names), tournaments/registrations/results metadata, role assignments (user_roles), audit/security logs.
- Threats: privilege escalation via mis-scoped RLS; IDOR via broad SELECT; bypass of client-only business rules; search_path injection risk on SECURITY DEFINER; XSS/headers; CSRF on cookie-auth endpoints; privacy concerns around decklists.


## Detailed Findings (by severity)

### 🔴 Critical

1) Non-admin tournament writes enabled by permissive policy
- Where: public.tournaments RLS
- Evidence:
  - A PERMISSIVE "*" policy named “Validate tournament data on insert/update” uses validate_tournament_data(...) as USING/WITH CHECK, which effectively ORs with admin policies and allows non-admin writes when validation returns true.
- Impact: Any authenticated user can create/modify/delete tournaments, bypassing admin-only writes.
- Fix:
  - Remove this permissive validator policy.
  - Keep admin-only RLS for INSERT/UPDATE/DELETE.
  - Enforce content checks via a CHECK constraint (preferred) or a RESTRICTIVE validator policy that doesn’t broaden access.


### 🟠 High

2) PII exposure: players publicly readable (emails)
- Policy decision: emails should be visible only to self and admins.
- Current state: “Public read access” (USING true) on players exposes emails publicly.
- Fix:
  - Remove public read policy from players.
  - Add SELECT policies for self and admins.
  - If public listing is needed, expose a players_public view (id, name only) and grant read to anon/auth.

3) Registrations time-gating enforced only client-side
- Where: public.tournament_registrations RLS
- Current: users can UPDATE/DELETE own registration anytime; client enforces time checks, but DB doesn’t.
- Impact: Malicious clients can update/delete after event start.
- Fix:
  - Add RESTRICTIVE UPDATE/DELETE policies for non-admin users: allow only if owner AND now() < event start.
  - Allow admins regardless of time.
  - Optionally require using the existing SECURITY DEFINER RPC (update_user_registration) for updates.


### 🟡 Medium

4) user_roles publicly readable to authenticated users (not limited)
- Current policy: USING true for SELECT; permits enumeration of admin assignments.
- Fix: Restrict to authenticated (explicit) or admin-only as desired. Recommendation: authenticated-only is acceptable (visible to signed-in users only).

5) SECURITY DEFINER functions missing constrained search_path
- Advisors flagged: public.get_user_registrations, public.update_user_registration, public.validate_manabox_url.
- Impact: With SECURITY DEFINER, mutable search_path is a defense-in-depth risk.
- Fix: Add SET search_path TO 'public','auth' to these functions (like your other hardened functions).

6) Public read of tournament_registrations includes decklist URL/content
- You confirmed this is intended. Be aware this exposes decklist links and optional text to the public. If privacy concerns arise, consider a public view that omits these fields and restrict table read.


### 🟢 Low

7) HTTP headers & CSP
- Add: CSP (nonce-based), HSTS, XFO, XCTO, Referrer-Policy, Permissions-Policy, COOP/CORP/COEP.

8) CSRF on signout
- Cross-site signouts possible; low impact.
- Fix: add Origin/Host check.

9) Magic link redirect target
- Current approach relies on email template rewriting to /auth/confirm; if not guaranteed, consider setting emailRedirectTo to /auth/confirm?next=…


## Actionable Checklist (priority order)

1) Blocker
- Drop permissive tournaments validation policy; add CHECK constraint; keep admin-only RLS.

2) High
- Lock down players SELECT to self/admins; add players_public view for public names.
- Add RESTRICTIVE RLS on tournament_registrations for UPDATE/DELETE to enforce time gating (admin bypass included). Route updates via RPC.

3) Medium
- Restrict user_roles read policy to authenticated (explicit) or admin-only.
- Recreate SECURITY DEFINER functions with SET search_path to 'public','auth'.

4) Low (optional)
- Add security headers/CSP.
- Add Origin/Host check to signout.
- Optionally adjust magic link redirect path.


## Patching Plan (DB via Supabase MCP)

- Single migration performs:
  - Drop permissive tournaments validator policy.
  - Add CHECK constraint for title/description validation.
  - Tighten players SELECT policies; create players_public view; grant SELECT to anon/auth on the view.
  - Add RESTRICTIVE time-gating policies for tournament_registrations update/delete (admin OR owner-before-start).
  - Restrict user_roles read to authenticated (explicit).
  - Recreate SECURITY DEFINER functions with safe search_path.

- Note: After locking down players SELECT, any public code path reading players.* must be updated to use public.players_public or to only request safe columns; otherwise, public pages that join players might fail.


## Next.js Code Follow-ups (not included in this DB migration)

- If public views/pages depend on players.*, switch them to select from players_public or restrict columns to non-PII (id,name).
- Optionally add security headers/CSP in next.config.
- Optionally add Origin checks in /api/auth/signout.
- Optionally adjust login emailRedirectTo to /auth/confirm?next=… if needed.


## Production Readiness Verdict

❌ Unsafe until the DB migration is applied.

- The migration addresses: critical tournaments privilege escalation, PII exposure policy, and registration time-gating.
- Post-migration, the app may require small code tweaks for public players listing.


## Evidence (excerpts)

- tournaments policy included a PERMISSIVE "*" validator policy using validate_tournament_data (allowed non-admin writes).
- players had “Public read access” USING true (exposed email). You confirmed emails should be private to self/admins.
- tournament_registrations lacked DB time gating; client-only enforcement.
- Advisors flagged mutable search_path for three SECURITY DEFINER functions.


## Rollback Plan

- Each change is reversible:
  - Drop the CHECK constraint / reinstate policy if needed (not recommended).
  - Re-add public read policies if needed.
  - Drop RESTRICTIVE policies if needed.
  - Restore prior function definitions from version control.


---

If you approve, the migration can be applied immediately via MCP to your live Supabase project. A brief smoke test should follow: create/update/delete tournament (non-admin must be forbidden), public ranking pages (players_public), updating registration after event start (must fail), confirm admin workflows remain unaffected.


## Patches Applied

Date: 2025-08-27

- Database (applied via MCP):
  - Removed permissive tournaments validator policy; added CHECK constraint (title_description_valid).
  - Tightened players RLS (self/admin-only read); added players_public view and granted anon/auth SELECT.
  - Added RESTRICTIVE time-gating policies for tournament_registrations (owner-only before start; admin bypass).
  - user_roles SELECT restricted to authenticated.
  - SECURITY DEFINER functions hardened with SET search_path (get_user_registrations, update_user_registration, validate_manabox_url).

- Code updates (this commit):
  - Ranking: rewired playersService.getRanking to aggregate from tournament_results and resolve names via players_public (public-safe).
  - Ranking UI types: added tournamentsPlayed to Player type; UI now prefers tournamentsPlayed, falls back to tournament_results length when available.
  - Tournament results: stopped joining players table; now resolves player names via players_public and attaches a minimal public-safe player object.
  - Auth: magic-link redirect updated to use /auth/confirm?next=… ensuring the OTP always lands on the confirm route.
  - API hardening: /api/auth/signout now enforces Origin/Host checks (CSRF mitigation) and sets Cache-Control: no-store.
  - Platform headers: added next.config.mjs with CSP (nonce), HSTS, XFO, XCTO, Referrer-Policy, Permissions-Policy, COOP, CORP, COEP.

- Post-patch verification guidance added in this report under “How to verify (smoke tests)”.

