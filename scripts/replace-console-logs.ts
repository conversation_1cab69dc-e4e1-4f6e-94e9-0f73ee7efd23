#!/usr/bin/env ts-node

import fs from 'fs';
import path from 'path';

// Files to update (excluding logger.ts itself)
const filesToUpdate = [
  './src/app/classifica/page.tsx',
  './src/components/calendar/CalendarActions.tsx',
  './src/components/calendar/EventSelectionModal.tsx',
  './src/components/calendar/CalendarGrid.tsx',
  './src/components/calendar/CalendarLegend.tsx',
  './src/components/admin/ProfileForm.tsx',
  './src/hooks/useAuth.ts',
];

function replaceConsoleLogsInFile(filePath: string) {
  const fullPath = path.resolve(filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.error(`File not found: ${fullPath}`);
    return;
  }
  
  let content = fs.readFileSync(fullPath, 'utf-8');
  let hasChanges = false;
  
  // Check if logger is already imported
  const hasLoggerImport = content.includes("from '@/lib/utils/logger'");
  
  // Replace console statements
  const originalContent = content;
  
  // Replace console.log
  content = content.replace(/console\.log\((.*?)\);?$/gm, (match, args) => {
    hasChanges = true;
    // Try to extract a meaningful message
    if (args.includes(',')) {
      // Multiple arguments
      const parts = args.split(',');
      const message = parts[0].trim();
      const rest = parts.slice(1).join(',').trim();
      return `logger.debug(${message}, { component: '${getComponentName(filePath)}', data: ${rest} });`;
    } else {
      return `logger.debug(${args}, { component: '${getComponentName(filePath)}' });`;
    }
  });
  
  // Replace console.error
  content = content.replace(/console\.error\((.*?)\);?$/gm, (match, args) => {
    hasChanges = true;
    if (args.includes(',')) {
      const parts = args.split(',');
      const message = parts[0].trim();
      const error = parts[1] ? parts[1].trim() : 'undefined';
      return `logger.error(${message}, ${error}, { component: '${getComponentName(filePath)}' });`;
    } else {
      return `logger.error(${args}, undefined, { component: '${getComponentName(filePath)}' });`;
    }
  });
  
  // Replace console.warn
  content = content.replace(/console\.warn\((.*?)\);?$/gm, (match, args) => {
    hasChanges = true;
    return `logger.warn(${args}, { component: '${getComponentName(filePath)}' });`;
  });
  
  // Add import if needed and there were changes
  if (hasChanges && !hasLoggerImport) {
    // Find the last import statement
    const importRegex = /^import\s+.*?;$/gm;
    const imports = content.match(importRegex);
    
    if (imports && imports.length > 0) {
      const lastImport = imports[imports.length - 1];
      const lastImportIndex = content.lastIndexOf(lastImport);
      const insertPosition = lastImportIndex + lastImport.length;
      
      content = 
        content.slice(0, insertPosition) + 
        "\nimport { logger } from '@/lib/utils/logger';" +
        content.slice(insertPosition);
    } else {
      // No imports found, add at the beginning
      content = "import { logger } from '@/lib/utils/logger';\n\n" + content;
    }
  }
  
  if (hasChanges) {
    fs.writeFileSync(fullPath, content, 'utf-8');
    console.log(`✅ Updated: ${filePath}`);
  } else {
    console.log(`⏭️  No changes needed: ${filePath}`);
  }
}

function getComponentName(filePath: string): string {
  const basename = path.basename(filePath, path.extname(filePath));
  return basename.charAt(0).toUpperCase() + basename.slice(1);
}

// Process all files
console.log('🔄 Starting console.log replacement...\n');

filesToUpdate.forEach(file => {
  replaceConsoleLogsInFile(file);
});

console.log('\n✨ Console.log replacement complete!');
