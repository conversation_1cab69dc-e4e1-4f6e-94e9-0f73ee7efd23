vorrei implementare il sistema di iscrizione dei players agli eventi presenti nel calendario, questo è il flusso:
1. l'utente clicca su un evento presente sul calendario
2. si apre il modale di iscrizione chiamato "iscrizione - {nome evento}"
3. l'utente compila tutti i campi obbligatori (nome e cognome, indirizzo email, nome deck, archetipo e decklist) 
e clicca sul pulsante "conferma iscrizione"
4. se risulta già un utente registrato con l'email inserita si procede con l'iscrizione del player all'evento inserendo i dati nel database
, altrimenti viene creato automaticamente un account e viene inviata una mail di conferma creazione account e solo poi viene iscritto con
successo all'evento
5. una volta che l'iscrizione è stata effettuata con successo, l'utente riceverà una mail con la conferma dell'iscrizione e le istruzioni per
verificare in ogni momento lo status dei suoi eventi sul suo profilo
6. l'utente potrà accedere al suo account per verificare lo stato delle sue iscrizioni e poterci fare cose tipo disiscriversi o completarle
nel caso della decklist che dovrà tassativamente essere modificabile fino ad un minuto prima dell'orario ufficiale di inizio dell'evento
