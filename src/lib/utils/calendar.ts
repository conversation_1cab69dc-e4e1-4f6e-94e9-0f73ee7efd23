/**
 * Consolidated calendar utilities
 * All calendar-related utility functions in one place
 */

// Calendar constants
export const DAYS_OF_WEEK = ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"];

export const MONTHS = [
  "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Ma<PERSON>", "Giu<PERSON>",
  "Lugli<PERSON>", "Agosto", "Settembre", "Ottobre", "Novembre", "Dicembre"
];

/**
 * Get the number of days in a given month
 * @param year - The year
 * @param month - The month (0-indexed)
 * @returns Number of days in the month
 */
export function getDaysInMonth(year: number, month: number): number {
  return new Date(year, month + 1, 0).getDate();
}

/**
 * Get the first day of the month (0 = Sunday, 6 = Saturday)
 * @param year - The year
 * @param month - The month (0-indexed)
 * @returns Day of week (0-6)
 */
export function getFirstDayOfMonth(year: number, month: number): number {
  return new Date(year, month, 1).getDay();
}

/**
 * Parse a date string in DD/MM/YYYY format to a Date object
 * @param dateStr - Date string in DD/MM/YYYY format
 * @returns Date object
 */
export function parseDate(dateStr: string): Date {
  const [day, month, year] = dateStr.split('/').map(Number);
  return new Date(year, month - 1, day);
}

/**
 * Format a date to DD/MM/YYYY string
 * @param date - Date object
 * @returns Formatted date string
 */
export function formatDateString(date: Date): string {
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
}

/**
 * Check if two dates are the same day
 * @param date1 - First date
 * @param date2 - Second date
 * @returns True if same day
 */
export function isSameDay(date1: Date, date2: Date): boolean {
  return (
    date1.getDate() === date2.getDate() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getFullYear() === date2.getFullYear()
  );
}

/**
 * Get the current date without time
 * @returns Date object set to midnight
 */
export function getToday(): Date {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return today;
}

/**
 * Check if a date is in the past
 * @param date - Date to check
 * @returns True if date is in the past
 */
export function isPastDate(date: Date | string): boolean {
  const checkDate = typeof date === 'string' ? new Date(date) : date;
  const today = getToday();
  return checkDate < today;
}

/**
 * Get month name from month index
 * @param monthIndex - Month index (0-11)
 * @returns Month name in Italian
 */
export function getMonthName(monthIndex: number): string {
  return MONTHS[monthIndex] || '';
}

/**
 * Get day of week name from day index
 * @param dayIndex - Day index (0-6, 0 = Sunday)
 * @returns Day name in Italian
 */
export function getDayName(dayIndex: number): string {
  return DAYS_OF_WEEK[dayIndex] || '';
}
