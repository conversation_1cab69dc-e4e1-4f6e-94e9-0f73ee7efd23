export function getSiteUrl(): string {
  // Prefer explicit env var to avoid host mismatches (e.g., localhost vs LAN IP)
  const envUrl = process.env.NEXT_PUBLIC_SITE_URL;
  if (envUrl && typeof envUrl === 'string' && envUrl.length > 0) return envUrl.replace(/\/$/, '');
  if (typeof window !== 'undefined' && window.location?.origin) return window.location.origin;
  // Fallback dev origin
  return 'http://localhost:3000';
}
