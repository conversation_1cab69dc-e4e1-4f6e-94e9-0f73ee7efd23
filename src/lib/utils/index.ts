import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { StoreColorKey, StoreColorScheme } from "../types";
import type { Database } from "@/lib/supabase/types";

type Player = Database['public']['Tables']['players']['Row'];
type User = {
  email?: string | null;
};

// Utility per combinare classi Tailwind
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Funzioni di utilità per il calendario
export const DAYS_OF_WEEK = ["Dom", "Lun", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Ven", "Sab"];
export const MONTHS = [
  "Gennaio", "Febbraio", "Marzo", "Aprile", "Maggio", "Giugno",
  "Luglio", "Agosto", "Settembre", "Ottobre", "Novembre", "Dicembre"
];

// Funzione per ottenere il numero di giorni in un mese
export function getDaysInMonth(year: number, month: number): number {
  return new Date(year, month + 1, 0).getDate();
}

// Funzione per ottenere il primo giorno del mese
export function getFirstDayOfMonth(year: number, month: number): number {
  return new Date(year, month, 1).getDay();
}

// Funzione per convertire una data in formato stringa "DD/MM/YYYY" in un oggetto Date
export function parseDate(dateStr: string): Date {
  const [day, month, year] = dateStr.split('/').map(Number);
  return new Date(year, month - 1, day);
}

// Colori per i diversi negozi
export const STORE_COLORS: Record<StoreColorKey, StoreColorScheme> = {
  "trs": { 
    bg: "bg-blue-500", 
    bgHover: "hover:bg-blue-600", 
    bgSelected: "bg-blue-600", 
    text: "text-white", 
    border: "border-blue-400/30",
    light: "text-blue-400",
    progress: "bg-blue-500"
  },
  "fw": { 
    bg: "bg-fuchsia-500", 
    bgHover: "hover:bg-fuchsia-600", 
    bgSelected: "bg-fuchsia-600", 
    text: "text-white", 
    border: "border-fuchsia-400/30",
    light: "text-fuchsia-400",
    progress: "bg-fuchsia-500"
  },
  "td": { 
    bg: "bg-red-500", 
    bgHover: "hover:bg-red-600", 
    bgSelected: "bg-red-600", 
    text: "text-white", 
    border: "border-red-400/30",
    light: "text-red-400",
    progress: "bg-red-500"
  },
  "mc": { 
    bg: "bg-green-500", 
    bgHover: "hover:bg-green-600", 
    bgSelected: "bg-green-600", 
    text: "text-white", 
    border: "border-green-400/30",
    light: "text-green-400",
    progress: "bg-green-500"
  },
  "top8-finale": { 
    bg: "bg-amber-500", 
    bgHover: "hover:bg-amber-600", 
    bgSelected: "bg-amber-600", 
    text: "text-white", 
    border: "border-amber-400/30",
    light: "text-amber-400",
    progress: "bg-amber-500"
  }
};

// Funzione per ottenere il colore in base all'ID del torneo
export function getStoreColor(tournamentId: string): StoreColorScheme {
  // Estrai il prefisso dal tournament ID (es. "trs-1" -> "trs")
  const prefix = tournamentId.split('-')[0] as StoreColorKey;
  return STORE_COLORS[prefix] || STORE_COLORS["top8-finale"]; // Fallback al colore della finale
}

// Funzione per ottenere il nome da mostrare per un player/user
export function getDisplayName(player?: Player | null, user?: User | null): string {
  // Se abbiamo un player con nome e cognome, usa quelli
  if (player?.first_name && player?.last_name) {
    return `${player.first_name} ${player.last_name}`;
  }
  
  // Se il player ha solo il campo name, usa quello
  if (player?.name && player.name.trim()) {
    return player.name;
  }
  
  // Se abbiamo un player con email, usa la parte prima della @
  if (player?.email) {
    const emailLocalPart = player.email.split('@')[0];
    return emailLocalPart || 'Utente';
  }
  
  // Se abbiamo solo l'utente auth con email, usa la parte prima della @
  if (user?.email) {
    const emailLocalPart = user.email.split('@')[0];
    return emailLocalPart || 'Utente';
  }
  
  // Fallback
  return 'Utente';
}
