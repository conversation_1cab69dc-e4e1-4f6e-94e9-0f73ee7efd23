import { ManaboxValidationResult } from '@/types/registration';

/**
 * Validates if a URL is a valid Manabox deck URL
 * @param url - The URL to validate
 * @returns ManaboxValidationResult with validation status and error message
 */
export function validateManaboxUrl(url: string): ManaboxValidationResult {
  // If empty, it's valid (optional field)
  if (!url.trim()) {
    return { isValid: true };
  }

  try {
    const parsedUrl = new URL(url);
    
    // Check if it's a Manabox domain
    if (parsedUrl.hostname !== 'manabox.app' && parsedUrl.hostname !== 'www.manabox.app') {
      return {
        isValid: false,
        error: 'L\'URL deve essere di Manabox (manabox.app)'
      };
    }

    // Check if it's a deck URL pattern
    // Manabox deck URLs typically follow the pattern: https://manabox.app/decks/{deckId}
    const deckUrlPattern = /^\/decks\/[a-zA-Z0-9_-]+$/;
    
    if (!deckUrlPattern.test(parsedUrl.pathname)) {
      return {
        isValid: false,
        error: 'URL non valido. Assicurati di copiare l\'URL completo della decklist'
      };
    }

    return { isValid: true };

  } catch {
    return {
      isValid: false,
      error: 'URL non valido. Verifica che sia un link corretto'
    };
  }
}

/**
 * Formats a Manabox URL by ensuring it has the correct protocol
 * @param url - The URL to format
 * @returns Formatted URL or null if invalid
 */
export function formatManaboxUrl(url: string): string | null {
  if (!url.trim()) {
    return null;
  }

  try {
    // If it doesn't start with http/https, add https
    let formattedUrl = url.trim();
    if (!formattedUrl.startsWith('http://') && !formattedUrl.startsWith('https://')) {
      // If it starts with manabox.app, add https://
      if (formattedUrl.startsWith('manabox.app') || formattedUrl.startsWith('www.manabox.app')) {
        formattedUrl = `https://${formattedUrl}`;
      } else {
        // If it's just a path, assume it's a manabox URL
        formattedUrl = `https://manabox.app${formattedUrl.startsWith('/') ? '' : '/'}${formattedUrl}`;
      }
    }

    const validation = validateManaboxUrl(formattedUrl);
    return validation.isValid ? formattedUrl : null;

  } catch {
    return null;
  }
}
