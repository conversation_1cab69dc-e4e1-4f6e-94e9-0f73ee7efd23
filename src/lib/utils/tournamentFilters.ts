import { Tournament, TournamentFilters } from '@/types/calendar';

/**
 * Apply all filters to a list of tournaments
 */
export function applyTournamentFilters(
  tournaments: Tournament[],
  filters: TournamentFilters
): Tournament[] {
  let filteredTournaments = [...tournaments];

  // Apply date range filter
  if (filters.startDate || filters.endDate) {
    filteredTournaments = filteredTournaments.filter(tournament => {
      const tournamentDate = tournament.date;
      
      if (filters.startDate && tournamentDate < filters.startDate) {
        return false;
      }
      
      if (filters.endDate && tournamentDate > filters.endDate) {
        return false;
      }
      
      return true;
    });
  }

  // Apply store filter
  if (filters.storeId) {
    filteredTournaments = filteredTournaments.filter(
      tournament => tournament.store_id === filters.storeId
    );
  }

  // Apply sort order
  filteredTournaments.sort((a, b) => {
    const dateA = new Date(a.date).getTime();
    const dateB = new Date(b.date).getTime();
    
    if (filters.sortOrder === 'nearest-first') {
      return dateA - dateB;
    } else {
      return dateB - dateA;
    }
  });

  return filteredTournaments;
}

/**
 * Check if a tournament matches the current filters
 */
export function tournamentMatchesFilters(
  tournament: Tournament,
  filters: TournamentFilters
): boolean {
  // Check date range
  if (filters.startDate && tournament.date < filters.startDate) {
    return false;
  }
  
  if (filters.endDate && tournament.date > filters.endDate) {
    return false;
  }

  // Check store filter
  if (filters.storeId && tournament.store_id !== filters.storeId) {
    return false;
  }

  return true;
}

/**
 * Get the count of tournaments that match the filters
 */
export function getFilteredTournamentsCount(
  tournaments: Tournament[],
  filters: TournamentFilters
): number {
  return tournaments.filter(tournament => 
    tournamentMatchesFilters(tournament, filters)
  ).length;
}
