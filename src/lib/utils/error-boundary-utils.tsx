import { ComponentType } from 'react';
import { withErrorBoundary } from '@/components/shared/ErrorBoundary';
import { ErrorFallback } from '@/components/shared/ErrorFallback';

/**
 * Pre-configured error boundary HOCs for different component types
 */

// For page-level components
export function withPageErrorBoundary<P extends object>(
  Component: ComponentType<P>,
  componentName?: string
) {
  return withErrorBoundary(Component, {
    level: 'page',
    fallback: <ErrorFallback variant="fullscreen" />,
    context: { component: componentName || Component.displayName || Component.name }
  });
}

// For section-level components (main content areas)
export function withSectionErrorBoundary<P extends object>(
  Component: ComponentType<P>,
  componentName?: string
) {
  return withErrorBoundary(Component, {
    level: 'section',
    fallback: <ErrorFallback variant="detailed" />,
    context: { component: componentName || Component.displayName || Component.name }
  });
}

// For individual components (modals, forms, etc.)
export function withComponentErrorBoundary<P extends object>(
  Component: ComponentType<P>,
  componentName?: string
) {
  return withErrorBoundary(Component, {
    level: 'component',
    fallback: <ErrorFallback variant="minimal" />,
    context: { component: componentName || Component.displayName || Component.name }
  });
}

// For data fetching components with custom retry logic
export function withDataErrorBoundary<P extends object>(
  Component: ComponentType<P>,
  options?: {
    onRetry?: () => void;
    componentName?: string;
  }
) {
  const componentName = options?.componentName || Component.displayName || Component.name;
  
  return withErrorBoundary(Component, {
    level: 'component',
    fallback: (
      <ErrorFallback 
        variant="detailed" 
        onRetry={options?.onRetry}
      />
    ),
    context: { 
      component: componentName,
      type: 'data-fetching'
    }
  });
}

// For form components with validation error handling
export function withFormErrorBoundary<P extends object>(
  Component: ComponentType<P>,
  componentName?: string
) {
  return withErrorBoundary(Component, {
    level: 'component',
    fallback: (
      <ErrorFallback 
        variant="minimal" 
        message="Si è verificato un errore nel form. Riprova."
      />
    ),
    context: { 
      component: componentName || Component.displayName || Component.name,
      type: 'form'
    },
    resetKeys: ['formData', 'values'] // Reset on form data changes
  });
}

// For list/table components with pagination
export function withListErrorBoundary<P extends object>(
  Component: ComponentType<P>,
  componentName?: string
) {
  return withErrorBoundary(Component, {
    level: 'component',
    fallback: <ErrorFallback variant="detailed" />,
    context: { 
      component: componentName || Component.displayName || Component.name,
      type: 'list'
    },
    resetKeys: ['page', 'filters', 'sort'] // Reset on pagination/filter changes
  });
}

// For chart/visualization components
export function withVisualizationErrorBoundary<P extends object>(
  Component: ComponentType<P>,
  componentName?: string
) {
  return withErrorBoundary(Component, {
    level: 'component',
    fallback: (
      <div className="flex items-center justify-center h-64 bg-gray-800/50 rounded-lg">
        <p className="text-gray-400">
          Impossibile visualizzare il grafico
        </p>
      </div>
    ),
    context: { 
      component: componentName || Component.displayName || Component.name,
      type: 'visualization'
    }
  });
}

// For critical components that should show minimal UI on error
export function withMinimalErrorBoundary<P extends object>(
  Component: ComponentType<P>,
  fallbackMessage = "Componente temporaneamente non disponibile"
) {
  return withErrorBoundary(Component, {
    level: 'component',
    fallback: (
      <div className="p-4 text-center text-gray-500">
        {fallbackMessage}
      </div>
    ),
    context: { component: Component.displayName || Component.name }
  });
}
