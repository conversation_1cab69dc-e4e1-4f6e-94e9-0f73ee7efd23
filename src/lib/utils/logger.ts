/**
 * Logger utility for development-only logging
 * Prevents console statements from appearing in production builds
 */

const isDevelopment = process.env.NODE_ENV === 'development';

interface LogContext {
  component?: string;
  action?: string;
  [key: string]: unknown;
}

class Logger {
  private formatMessage(level: string, message: string, context?: LogContext): string {
    const timestamp = new Date().toISOString();
    const contextStr = context ? ` | ${JSON.stringify(context)}` : '';
    return `[${timestamp}] [${level}]${contextStr} ${message}`;
  }

  debug(message: string, context?: LogContext): void {
    if (isDevelopment) {
      console.log(this.formatMessage('DEBUG', message, context));
    }
  }

  info(message: string, context?: LogContext): void {
    if (isDevelopment) {
      console.info(this.formatMessage('INFO', message, context));
    }
  }

  warn(message: string, context?: LogContext): void {
    if (isDevelopment) {
      console.warn(this.formatMessage('WARN', message, context));
    }
  }

  error(message: string, error?: Error | unknown, context?: LogContext): void {
    if (isDevelopment) {
      console.error(this.formatMessage('ERROR', message, context));
      if (error) {
        console.error(error);
      }
    }
  }

  // Utility method for logging API responses
  api(method: string, endpoint: string, data?: unknown): void {
    if (isDevelopment) {
      console.log(`[API] ${method} ${endpoint}`, data);
    }
  }

  // Utility method for logging component lifecycle
  lifecycle(component: string, event: string, data?: unknown): void {
    if (isDevelopment) {
      console.log(`[${component}] ${event}`, data);
    }
  }
}

// Export singleton instance
export const logger = new Logger();

// Export for testing purposes
export { Logger };
