import { supabase } from '../supabase/client';
import type { Database } from '../supabase/types';
import {
  storeSchema,
  storeUpdateSchema,
  type Store,
  type StoreFormData,
  type StoreUpdateFormData,
  type StoreError,
  type StoreErrorType,
  type StoreOperationResult
} from '@/types/stores';
import { logger } from '@/lib/utils/logger';

type InsertStore = Database['public']['Tables']['stores']['Insert'];
type UpdateStore = Database['public']['Tables']['stores']['Update'];

/**
 * Enhanced stores service with security, validation, and error handling
 */
class StoresService {
  /**
   * Validates admin permissions before write operations
   */
  private async validateAdminPermissions(): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw this.createError('PERMISSION_DENIED', 'Authentication required');
      }

      // Check admin role via API endpoint
      const response = await fetch('/api/auth/is-admin');
      const { isAdmin } = await response.json();

      if (!isAdmin) {
        throw this.createError('PERMISSION_DENIED', 'Admin privileges required');
      }
    } catch (error) {
      if (error instanceof Error && error.message.includes('PERMISSION_DENIED')) {
        throw error;
      }
      logger.error('Failed to validate admin permissions', error, { service: 'stores' });
      throw this.createError('PERMISSION_DENIED', 'Unable to verify admin permissions');
    }
  }

  /**
   * Creates a standardized error object
   */
  private createError(type: StoreErrorType, message: string, field?: string, details?: Record<string, unknown>): StoreError {
    return { type, message, field, details };
  }

  /**
   * Handles Supabase errors and converts them to StoreError
   */
  private handleSupabaseError(error: unknown, operation: string): StoreError {
    logger.error(`Supabase error during ${operation}`, error, { service: 'stores' });

    // Handle specific Supabase error codes
    if (error && typeof error === 'object' && 'code' in error) {
      if (error.code === '23505') {
        return this.createError('DUPLICATE_NAME', 'A store with this name already exists');
      }

      if (error.code === 'PGRST116') {
        return this.createError('NOT_FOUND', 'Store not found');
      }
    }

    if (error && typeof error === 'object' && 'message' in error &&
        typeof error.message === 'string' && error.message.includes('permission')) {
      return this.createError('PERMISSION_DENIED', 'Insufficient permissions');
    }

    const message = error && typeof error === 'object' && 'message' in error &&
                   typeof error.message === 'string' ? error.message : 'An unexpected error occurred';
    return this.createError('UNKNOWN_ERROR', message);
  }

  /**
   * Validates and sanitizes store data
   */
  private validateStoreData(data: StoreFormData): StoreFormData {
    try {
      return storeSchema.parse(data);
    } catch (error: unknown) {
      logger.error('Store validation failed', error, { service: 'stores', data });
      const validationErrors = error && typeof error === 'object' && 'errors' in error ? error.errors : undefined;
      throw this.createError('VALIDATION_ERROR', 'Invalid store data', undefined, { validationErrors });
    }
  }

  /**
   * Validates and sanitizes store update data
   */
  private validateStoreUpdateData(data: StoreUpdateFormData): StoreUpdateFormData {
    try {
      return storeUpdateSchema.parse(data);
    } catch (error: unknown) {
      logger.error('Store update validation failed', error, { service: 'stores', data });
      const validationErrors = error && typeof error === 'object' && 'errors' in error ? error.errors : undefined;
      throw this.createError('VALIDATION_ERROR', 'Invalid store update data', undefined, { validationErrors });
    }
  }

  /**
   * Fetches all stores with error handling
   */
  async getAll(): Promise<StoreOperationResult<Store[]>> {
    try {
      const { data, error } = await supabase
        .from('stores')
        .select('*')
        .order('name');

      if (error) {
        const storeError = this.handleSupabaseError(error, 'getAll');
        return { success: false, error: storeError };
      }

      return { success: true, data: data || [] };
    } catch (error) {
      logger.error('Failed to fetch stores', error, { service: 'stores' });
      return {
        success: false,
        error: this.createError('UNKNOWN_ERROR', 'Failed to fetch stores')
      };
    }
  }

  /**
   * Fetches a store by ID with error handling
   */
  async getById(id: string): Promise<StoreOperationResult<Store>> {
    try {
      // Validate ID format
      if (!id || typeof id !== 'string' || id.trim().length === 0) {
        return {
          success: false,
          error: this.createError('VALIDATION_ERROR', 'Invalid store ID')
        };
      }

      const { data, error } = await supabase
        .from('stores')
        .select('*')
        .eq('id', id.trim())
        .single();

      if (error) {
        const storeError = this.handleSupabaseError(error, 'getById');
        return { success: false, error: storeError };
      }

      return { success: true, data };
    } catch (error) {
      logger.error('Failed to fetch store by ID', error, { service: 'stores', storeId: id });
      return {
        success: false,
        error: this.createError('UNKNOWN_ERROR', 'Failed to fetch store')
      };
    }
  }

  /**
   * Creates a new store with validation and security checks
   */
  async create(storeData: StoreFormData): Promise<StoreOperationResult<Store>> {
    try {
      // Validate admin permissions
      await this.validateAdminPermissions();

      // Validate and sanitize input data
      const validatedData = this.validateStoreData(storeData);

      // Prepare insert data
      const insertData: InsertStore = {
        name: validatedData.name,
        address: validatedData.address,
        city: validatedData.city,
        color: validatedData.color || 'circolo'
      };

      // Insert into database
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const { data, error } = await (supabase as any)
        .from('stores')
        .insert(insertData)
        .select()
        .single();

      if (error) {
        const storeError = this.handleSupabaseError(error, 'create');
        return { success: false, error: storeError };
      }

      logger.info('Store created successfully', { service: 'stores', storeId: data.id, storeName: data.name });
      return { success: true, data };
    } catch (error) {
      if (error instanceof Error && error.message.includes('PERMISSION_DENIED')) {
        return { success: false, error: this.createError('PERMISSION_DENIED', error.message) };
      }
      if (error instanceof Error && error.message.includes('VALIDATION_ERROR')) {
        return { success: false, error: this.createError('VALIDATION_ERROR', error.message) };
      }

      logger.error('Failed to create store', error, { service: 'stores', storeData });
      return {
        success: false,
        error: this.createError('UNKNOWN_ERROR', 'Failed to create store')
      };
    }
  }

  /**
   * Updates an existing store with validation and security checks
   */
  async update(id: string, storeData: StoreUpdateFormData): Promise<StoreOperationResult<Store>> {
    try {
      // Validate admin permissions
      await this.validateAdminPermissions();

      // Validate ID format
      if (!id || typeof id !== 'string' || id.trim().length === 0) {
        return {
          success: false,
          error: this.createError('VALIDATION_ERROR', 'Invalid store ID')
        };
      }

      // Validate and sanitize input data
      const validatedData = this.validateStoreUpdateData(storeData);

      // Remove undefined values
      const updateData: UpdateStore = Object.fromEntries(
        Object.entries(validatedData).filter(([, value]) => value !== undefined)
      );

      // Check if store exists before updating
      const existingStore = await this.getById(id);
      if (!existingStore.success) {
        return existingStore;
      }

      // Update in database
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const { data, error } = await (supabase as any)
        .from('stores')
        .update(updateData)
        .eq('id', id.trim())
        .select()
        .single();

      if (error) {
        const storeError = this.handleSupabaseError(error, 'update');
        return { success: false, error: storeError };
      }

      logger.info('Store updated successfully', { service: 'stores', storeId: data.id, storeName: data.name });
      return { success: true, data };
    } catch (error) {
      if (error instanceof Error && error.message.includes('PERMISSION_DENIED')) {
        return { success: false, error: this.createError('PERMISSION_DENIED', error.message) };
      }
      if (error instanceof Error && error.message.includes('VALIDATION_ERROR')) {
        return { success: false, error: this.createError('VALIDATION_ERROR', error.message) };
      }

      logger.error('Failed to update store', error, { service: 'stores', storeId: id, storeData });
      return {
        success: false,
        error: this.createError('UNKNOWN_ERROR', 'Failed to update store')
      };
    }
  }

  /**
   * Deletes a store with validation and security checks
   */
  async delete(id: string): Promise<StoreOperationResult<void>> {
    try {
      // Validate admin permissions
      await this.validateAdminPermissions();

      // Validate ID format
      if (!id || typeof id !== 'string' || id.trim().length === 0) {
        return {
          success: false,
          error: this.createError('VALIDATION_ERROR', 'Invalid store ID')
        };
      }

      // Check if store exists before deleting
      const existingStore = await this.getById(id);
      if (!existingStore.success) {
        return { success: false, error: existingStore.error };
      }

      // Check if store is referenced by tournaments
      const { data: tournaments, error: tournamentsError } = await supabase
        .from('tournaments')
        .select('id')
        .eq('store_id', id.trim())
        .limit(1);

      if (tournamentsError) {
        logger.error('Failed to check tournament references', tournamentsError, { service: 'stores', storeId: id });
        return {
          success: false,
          error: this.createError('UNKNOWN_ERROR', 'Failed to verify store dependencies')
        };
      }

      if (tournaments && tournaments.length > 0) {
        return {
          success: false,
          error: this.createError('VALIDATION_ERROR', 'Cannot delete store: it is referenced by existing tournaments')
        };
      }

      // Delete from database
      const { error } = await supabase
        .from('stores')
        .delete()
        .eq('id', id.trim());

      if (error) {
        const storeError = this.handleSupabaseError(error, 'delete');
        return { success: false, error: storeError };
      }

      logger.info('Store deleted successfully', { service: 'stores', storeId: id });
      return { success: true };
    } catch (error) {
      if (error instanceof Error && error.message.includes('PERMISSION_DENIED')) {
        return { success: false, error: this.createError('PERMISSION_DENIED', error.message) };
      }
      if (error instanceof Error && error.message.includes('VALIDATION_ERROR')) {
        return { success: false, error: this.createError('VALIDATION_ERROR', error.message) };
      }

      logger.error('Failed to delete store', error, { service: 'stores', storeId: id });
      return {
        success: false,
        error: this.createError('UNKNOWN_ERROR', 'Failed to delete store')
      };
    }
  }

  /**
   * Checks if a store name is available (not already taken)
   */
  async isNameAvailable(name: string, excludeId?: string): Promise<StoreOperationResult<boolean>> {
    try {
      if (!name || typeof name !== 'string' || name.trim().length === 0) {
        return {
          success: false,
          error: this.createError('VALIDATION_ERROR', 'Invalid store name')
        };
      }

      let query = supabase
        .from('stores')
        .select('id')
        .ilike('name', name.trim());

      if (excludeId) {
        query = query.neq('id', excludeId);
      }

      const { data, error } = await query.limit(1);

      if (error) {
        const storeError = this.handleSupabaseError(error, 'isNameAvailable');
        return { success: false, error: storeError };
      }

      return { success: true, data: !data || data.length === 0 };
    } catch (error) {
      logger.error('Failed to check name availability', error, { service: 'stores', name });
      return {
        success: false,
        error: this.createError('UNKNOWN_ERROR', 'Failed to check name availability')
      };
    }
  }
}

// Export singleton instance
export const storesService = new StoresService();

// Export legacy interface for backward compatibility
export const legacyStoresService = {
  async getAll() {
    const result = await storesService.getAll();
    if (!result.success) throw new Error(result.error?.message || 'Failed to fetch stores');
    return result.data;
  },

  async getById(id: string) {
    const result = await storesService.getById(id);
    if (!result.success) throw new Error(result.error?.message || 'Failed to fetch store');
    return result.data;
  },

  async create(store: InsertStore) {
    const result = await storesService.create(store as StoreFormData);
    if (!result.success) throw new Error(result.error?.message || 'Failed to create store');
    return result.data;
  },

  async update(id: string, store: UpdateStore) {
    const result = await storesService.update(id, store as StoreUpdateFormData);
    if (!result.success) throw new Error(result.error?.message || 'Failed to update store');
    return result.data;
  },

  async delete(id: string) {
    const result = await storesService.delete(id);
    if (!result.success) throw new Error(result.error?.message || 'Failed to delete store');
  }
};