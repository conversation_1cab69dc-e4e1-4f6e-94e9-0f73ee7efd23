import { supabase } from '../supabase/client';
import type { Database } from '../supabase/types';

type Tournament = Database['public']['Tables']['tournaments']['Row'];
// type InsertTournament = Database['public']['Tables']['tournaments']['Insert'];
// type UpdateTournament = Database['public']['Tables']['tournaments']['Update'];

export const tournamentsService = {
  async getAll() {
    // Get active season ID
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data: activeSeason } = await (supabase as any)
      .from('seasons')
      .select('id')
      .eq('is_active', true)
      .single();
    
    if (!activeSeason) return [];
    
    // Get tournaments from active season
    const { data, error } = await supabase
      .from('tournaments')
      .select(`
        *,
        store:stores(*, color),
        tournament_registrations(count)
      `)
      .eq('season_id', activeSeason.id)
      .order('date', { ascending: true });
    
    if (error) throw error;
    return data;
  },

  async getAllPaginated(page: number = 0, pageSize: number = 12) {
    // Get active season ID
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data: activeSeason } = await (supabase as any)
      .from('seasons')
      .select('id')
      .eq('is_active', true)
      .single();
    
    if (!activeSeason) return { data: [], total: 0, hasMore: false };
    
    // Get total count first
    const { count, error: countError } = await supabase
      .from('tournaments')
      .select('*', { count: 'exact', head: true })
      .eq('season_id', activeSeason.id);
    
    if (countError) throw countError;
    
    // Get paginated tournaments from active season, ordered by date (newest first for cards view)
    const { data, error } = await supabase
      .from('tournaments')
      .select(`
        *,
        store:stores(*, color),
        tournament_registrations(count)
      `)
      .eq('season_id', activeSeason.id)
      .order('date', { ascending: false })
      .range(page * pageSize, (page + 1) * pageSize - 1);
    
    if (error) throw error;
    
    const total = count || 0;
    const hasMore = (page + 1) * pageSize < total;
    
    return { data: data || [], total, hasMore };
  },

  async getById(id: string) {
    const { data, error } = await supabase
      .from('tournaments')
      .select(`
        *,
        store:stores(*, color),
        tournament_registrations(count)
      `)
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  },

  async getByMonth(startDate: Date, endDate: Date) {
    // Get active season ID
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data: activeSeason } = await (supabase as any)
      .from('seasons')
      .select('id')
      .eq('is_active', true)
      .single();
    
    if (!activeSeason) return [];
    
    // Get tournaments from active season
    const { data, error } = await supabase
      .from('tournaments')
      .select(`
        *,
        store:stores(*, color),
        tournament_registrations(count)
      `)
      .eq('season_id', activeSeason.id)
      .gte('date', startDate.toISOString().split('T')[0])
      .lte('date', endDate.toISOString().split('T')[0])
      .order('date', { ascending: true });
    
    if (error) throw error;
    return data;
  },

  async getByDate(date: Date) {
    const dateStr = date.toISOString().split('T')[0];

    // Get active season ID
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data: activeSeason } = await (supabase as any)
      .from('seasons')
      .select('id')
      .eq('is_active', true)
      .single();
    
    if (!activeSeason) return [];

    // Get tournaments from active season
    const { data, error } = await supabase
      .from('tournaments')
      .select(`
        *,
        store:stores(*, color),
        tournament_registrations(
          count
        )
      `)
      .eq('season_id', activeSeason.id)
      .eq('date', dateStr)
      .order('time_start');
    
    if (error) throw error;
    return data;
  },

  async create(tournament: Omit<Tournament, 'id' | 'created_at' | 'updated_at'>) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data, error } = await (supabase as any)
      .from('tournaments')
      .insert(tournament)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async update(id: string, tournament: Partial<Tournament>) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data, error } = await (supabase as any)
      .from('tournaments')
      .update(tournament)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async delete(id: string) {
    const { error } = await supabase
      .from('tournaments')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  },

  async register(tournamentId: string, playerId: string, deckName?: string, archetypeId?: string) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data, error } = await (supabase as any)
      .from('tournament_registrations')
      .insert({
        tournament_id: tournamentId,
        player_id: playerId,
        deck_name: deckName,
        archetype_id: archetypeId,
      })
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async unregister(tournamentId: string, playerId: string) {
    const { error } = await supabase
      .from('tournament_registrations')
      .delete()
      .match({
        tournament_id: tournamentId,
        player_id: playerId,
      });
    
    if (error) throw error;
  }
}; 