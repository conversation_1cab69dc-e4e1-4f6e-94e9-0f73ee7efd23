import { supabase } from '../supabase/client';
import { Tables, TablesInsert, TablesUpdate } from '../supabase/types';

export type Archetype = Tables<'archetypes'>;

/**
 * Gets all active archetypes ordered by name
 */
export async function getArchetypes() {
  const { data, error } = await supabase
    .from('archetypes')
    .select('*')
    .eq('is_active', true)
    .order('name');

  if (error) {
    throw new Error(`Failed to fetch archetypes: ${error.message}`);
  }

  return data;
}

/**
 * Gets a specific archetype by ID
 */
export async function getArchetypeById(id: string) {
  const { data, error } = await supabase
    .from('archetypes')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    throw new Error(`Failed to fetch archetype: ${error.message}`);
  }

  return data;
}

/**
 * Creates a new archetype (admin only)
 */
export async function createArchetype(archetype: TablesInsert<'archetypes'>) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const { data, error } = await (supabase as any)
    .from('archetypes')
    .insert(archetype)
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create archetype: ${error.message}`);
  }

  return data;
}

/**
 * Updates an existing archetype (admin only)
 */
export async function updateArchetype(id: string, updates: TablesUpdate<'archetypes'>) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const { data, error } = await (supabase as any)
    .from('archetypes')
    .update(updates)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to update archetype: ${error.message}`);
  }

  return data;
}

/**
 * Deletes an archetype (admin only)
 */
export async function deleteArchetype(id: string) {
  const { error } = await supabase
    .from('archetypes')
    .delete()
    .eq('id', id);

  if (error) {
    throw new Error(`Failed to delete archetype: ${error.message}`);
  }

  return true;
}
