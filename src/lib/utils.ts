import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import type { Database } from "@/lib/supabase/types";

type Player = Database['public']['Tables']['players']['Row'];
type User = {
  email?: string | null;
};

// Utility per combinare classi Tailwind
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Funzione per ottenere il nome da mostrare per un player/user
export function getDisplayName(player?: Player | null, user?: User | null): string {
  // Se abbiamo un player con nome e cognome, usa quelli
  if (player?.first_name && player?.last_name) {
    return `${player.first_name} ${player.last_name}`;
  }
  
  // Se il player ha solo il campo name, usa quello
  if (player?.name && player.name.trim()) {
    return player.name;
  }
  
  // Se abbiamo un player con email, usa la parte prima della @
  if (player?.email) {
    const emailLocalPart = player.email.split('@')[0];
    return emailLocalPart || 'Utente';
  }
  
  // Se abbiamo solo l'utente auth con email, usa la parte prima della @
  if (user?.email) {
    const emailLocalPart = user.email.split('@')[0];
    return emailLocalPart || 'Utente';
  }
  
  // Fallback
  return 'Utente';
}

// Calendar utilities have been moved to src/lib/utils/calendar.ts
// Color management has been moved to src/lib/constants/colors.ts
