// Tipi comuni per l'applicazione

// Tipo per i tornei
export type Tournament = {
  id: string;
  title: string;
  date: Date;
  location: string;
  time: string;
  format: string;
  maxPlayers: number;
  currentPlayers: number;
  description: string;
  prizePool: string;
};

// Tipo per i colori dei negozi
export type StoreColorKey = "trs" | "fw" | "td" | "mc" | "top8-finale";

export type StoreColorScheme = {
  bg: string;
  bgHover: string;
  bgSelected: string;
  text: string;
  border: string;
  light: string;
  progress: string;
};

// Tipo per i giocatori nella classifica
export type Player = {
  id: string;
  name: string;
  tournaments: {
    name: string;
    date: string;
    location: string;
    points: number;
    position: number;
  }[];
}; 