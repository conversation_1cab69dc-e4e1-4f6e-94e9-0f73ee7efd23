import { StoreColorScheme } from '@/types/calendar';

/**
 * Unified color management system
 * Single source of truth for all color-related constants and functions
 */

// Tailwind color palette for stores
export const TAILWIND_COLORS = {
  blue: {
    base: 'blue-500',
    hover: 'blue-600',
    light: 'blue-400',
    border: 'blue-400/30'
  },
  fuchsia: {
    base: 'fuchsia-500',
    hover: 'fuchsia-600',
    light: 'fuchsia-400',
    border: 'fuchsia-400/30'
  },
  red: {
    base: 'red-500',
    hover: 'red-600',
    light: 'red-400',
    border: 'red-400/30'
  },
  green: {
    base: 'green-500',
    hover: 'green-600',
    light: 'green-400',
    border: 'green-400/30'
  },
  amber: {
    base: 'amber-500',
    hover: 'amber-600',
    light: 'amber-400',
    border: 'amber-400/30'
  },
  gray: {
    base: 'gray-500',
    hover: 'gray-600',
    light: 'gray-400',
    border: 'gray-400/30'
  },
  purple: {
    base: 'purple-500',
    hover: 'purple-600',
    light: 'purple-400',
    border: 'purple-400/30'
  },
  indigo: {
    base: 'indigo-500',
    hover: 'indigo-600',
    light: 'indigo-400',
    border: 'indigo-400/30'
  }
} as const;

// Legacy store color mappings (for backward compatibility)
export const LEGACY_STORE_COLORS = {
  "trs": "blue-500",      // Trick Room Store
  "fw": "fuchsia-500",    // Fantasy World
  "td": "red-500",        // Top Deck
  "mc": "green-500",      // Magic Corner
  "top8-finale": "amber-500"  // Top 8 Finals
} as const;

// Store name to color mappings
export const STORE_NAME_COLORS = {
  'trick room': 'blue-500',
  'circolo': 'blue-500',
  'borgo molino': 'blue-500',
  'fantasy world': 'fuchsia-500',
  'top deck': 'red-500',
  'magic corner': 'green-500',
  'top 8': 'amber-500',
  'finale': 'amber-500'
} as const;

/**
 * Generate a complete color scheme from a Tailwind color code
 * @param colorCode - Tailwind color code (e.g., "blue-500")
 * @returns Complete StoreColorScheme object
 */
export function generateColorScheme(colorCode: string): StoreColorScheme {
  // Extract base color name (e.g., "blue" from "blue-500")
  const colorBase = colorCode.split('-')[0];
  
  // Handle edge cases for Tailwind class generation
  // Note: These classes need to be included in Tailwind's content configuration
  return {
    bg: `bg-${colorCode}`,
    bgHover: `hover:bg-${colorBase}-600`,
    bgSelected: `bg-${colorBase}-600`,
    text: 'text-white',
    border: `border-${colorBase}-400/30`,
    light: `text-${colorBase}-400`,
    progress: `bg-${colorCode}`
  };
}

/**
 * Get color scheme from store data
 * @param store - Store object with optional color and name
 * @param tournament - Optional tournament object for context
 * @returns StoreColorScheme
 */
export function getStoreColorScheme(
  store: { color?: string | null; name?: string } | null,
  tournament?: { title?: string }
): StoreColorScheme {
  // Special case for tournaments without store
  if (!store) {
    if (tournament?.title) {
      const title = tournament.title.toLowerCase();
      if (title.includes('top 8') || title.includes('finale')) {
        return generateColorScheme('amber-500');
      }
    }
    return generateColorScheme('gray-500');
  }
  
  // Use database color if available
  if (store.color) {
    return generateColorScheme(store.color);
  }
  
  // Fallback to name-based mapping
  if (store.name) {
    const nameLower = store.name.toLowerCase();
    for (const [keyword, color] of Object.entries(STORE_NAME_COLORS)) {
      if (nameLower.includes(keyword)) {
        return generateColorScheme(color);
      }
    }
  }
  
  // Default fallback - cycle through colors based on store name
  const defaultColors = ['blue-500', 'fuchsia-500', 'amber-500', 'green-500'];
  const index = (store.name?.length || 0) % defaultColors.length;
  return generateColorScheme(defaultColors[index]);
}

/**
 * Get color scheme from legacy tournament ID
 * @param tournamentId - Legacy tournament ID (e.g., "trs-1")
 * @returns StoreColorScheme
 */
export function getColorFromLegacyId(tournamentId: string): StoreColorScheme {
  const prefix = tournamentId.split('-')[0];
  const colorCode = LEGACY_STORE_COLORS[prefix as keyof typeof LEGACY_STORE_COLORS] || 'amber-500';
  return generateColorScheme(colorCode);
}

/**
 * Get available color options for store selection
 * @returns Array of color options with label and value
 */
export function getAvailableColors(): Array<{ label: string; value: string; preview: string }> {
  return [
    { label: 'Blu', value: 'blue-500', preview: 'bg-blue-500' },
    { label: 'Fucsia', value: 'fuchsia-500', preview: 'bg-fuchsia-500' },
    { label: 'Rosso', value: 'red-500', preview: 'bg-red-500' },
    { label: 'Verde', value: 'green-500', preview: 'bg-green-500' },
    { label: 'Ambra', value: 'amber-500', preview: 'bg-amber-500' },
    { label: 'Grigio', value: 'gray-500', preview: 'bg-gray-500' },
    { label: 'Viola', value: 'purple-500', preview: 'bg-purple-500' },
    { label: 'Indaco', value: 'indigo-500', preview: 'bg-indigo-500' }
  ];
}

// Export a consolidated color utility object
export const Colors = {
  generate: generateColorScheme,
  getStoreScheme: getStoreColorScheme,
  getLegacyScheme: getColorFromLegacyId,
  getAvailable: getAvailableColors,
  tailwind: TAILWIND_COLORS,
  legacy: LEGACY_STORE_COLORS,
  storeNames: STORE_NAME_COLORS
} as const;
