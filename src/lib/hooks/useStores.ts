import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { storesService } from '../services/stores';
import type {
  Store,
  StoreFormData,
  StoreUpdateFormData
} from '@/types/stores';
import { logger } from '@/lib/utils/logger';
import { useCallback } from 'react';

/**
 * Enhanced store management hooks with proper error handling and optimistic updates
 */

/**
 * Hook to fetch all stores
 */
export function useStores() {
  return useQuery({
    queryKey: ['stores'],
    queryFn: async (): Promise<Store[]> => {
      const result = await storesService.getAll();
      if (!result.success) {
        logger.error('Failed to fetch stores', result.error, { hook: 'useStores' });
        throw new Error(result.error?.message || 'Failed to fetch stores');
      }
      return result.data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry on permission errors
      if (error?.message?.includes('permission')) return false;
      return failureCount < 3;
    }
  });
}

/**
 * Hook to fetch a single store by ID
 */
export function useStore(id: string) {
  return useQuery({
    queryKey: ['stores', id],
    queryFn: async (): Promise<Store> => {
      const result = await storesService.getById(id);
      if (!result.success) {
        logger.error('Failed to fetch store', result.error, { hook: 'useStore', storeId: id });
        throw new Error(result.error?.message || 'Failed to fetch store');
      }
      return result.data!;
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry on not found or permission errors
      if (error?.message?.includes('not found') || error?.message?.includes('permission')) return false;
      return failureCount < 3;
    }
  });
}

/**
 * Hook to create a new store
 */
export function useCreateStore() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (storeData: StoreFormData): Promise<Store> => {
      const result = await storesService.create(storeData);
      if (!result.success) {
        logger.error('Failed to create store', result.error, { hook: 'useCreateStore', storeData });
        throw new Error(result.error?.message || 'Failed to create store');
      }
      return result.data!;
    },
    onSuccess: (newStore) => {
      // Optimistically update the stores list
      queryClient.setQueryData(['stores'], (oldStores: Store[] | undefined) => {
        if (!oldStores) return [newStore];
        return [...oldStores, newStore].sort((a, b) => a.name.localeCompare(b.name));
      });

      // Invalidate to ensure consistency
      queryClient.invalidateQueries({ queryKey: ['stores'] });

      logger.info('Store created successfully', { hook: 'useCreateStore', storeId: newStore.id });
    },
    onError: (error) => {
      logger.error('Store creation failed', error, { hook: 'useCreateStore' });
    }
  });
}

/**
 * Hook to update an existing store
 */
export function useUpdateStore() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, storeData }: { id: string; storeData: StoreUpdateFormData }): Promise<Store> => {
      const result = await storesService.update(id, storeData);
      if (!result.success) {
        logger.error('Failed to update store', result.error, { hook: 'useUpdateStore', storeId: id, storeData });
        throw new Error(result.error?.message || 'Failed to update store');
      }
      return result.data!;
    },
    onSuccess: (updatedStore, { id }) => {
      // Optimistically update the stores list
      queryClient.setQueryData(['stores'], (oldStores: Store[] | undefined) => {
        if (!oldStores) return [updatedStore];
        return oldStores
          .map(store => store.id === id ? updatedStore : store)
          .sort((a, b) => a.name.localeCompare(b.name));
      });

      // Update the individual store cache
      queryClient.setQueryData(['stores', id], updatedStore);

      // Invalidate to ensure consistency
      queryClient.invalidateQueries({ queryKey: ['stores'] });
      queryClient.invalidateQueries({ queryKey: ['stores', id] });

      logger.info('Store updated successfully', { hook: 'useUpdateStore', storeId: id });
    },
    onError: (error, { id }) => {
      logger.error('Store update failed', error, { hook: 'useUpdateStore', storeId: id });
    }
  });
}

/**
 * Hook to delete a store
 */
export function useDeleteStore() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      const result = await storesService.delete(id);
      if (!result.success) {
        logger.error('Failed to delete store', result.error, { hook: 'useDeleteStore', storeId: id });
        throw new Error(result.error?.message || 'Failed to delete store');
      }
    },
    onSuccess: (_, id) => {
      // Optimistically remove from stores list
      queryClient.setQueryData(['stores'], (oldStores: Store[] | undefined) => {
        if (!oldStores) return [];
        return oldStores.filter(store => store.id !== id);
      });

      // Remove from individual store cache
      queryClient.removeQueries({ queryKey: ['stores', id] });

      // Invalidate to ensure consistency
      queryClient.invalidateQueries({ queryKey: ['stores'] });

      logger.info('Store deleted successfully', { hook: 'useDeleteStore', storeId: id });
    },
    onError: (error, id) => {
      logger.error('Store deletion failed', error, { hook: 'useDeleteStore', storeId: id });
    }
  });
}

/**
 * Hook to check if a store name is available
 */
export function useStoreNameAvailability() {
  return useMutation({
    mutationFn: async ({ name, excludeId }: { name: string; excludeId?: string }): Promise<boolean> => {
      const result = await storesService.isNameAvailable(name, excludeId);
      if (!result.success) {
        logger.error('Failed to check name availability', result.error, { hook: 'useStoreNameAvailability', name });
        throw new Error(result.error?.message || 'Failed to check name availability');
      }
      return result.data!;
    }
  });
}

/**
 * Hook for store management state and operations
 */
export function useStoreManagement() {
  const queryClient = useQueryClient();
  const storesQuery = useStores();
  const createStore = useCreateStore();
  const updateStore = useUpdateStore();
  const deleteStore = useDeleteStore();
  const checkNameAvailability = useStoreNameAvailability();

  const refreshStores = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ['stores'] });
  }, [queryClient]);

  const getStoreById = useCallback((id: string): Store | undefined => {
    const stores = storesQuery.data;
    return stores?.find(store => store.id === id);
  }, [storesQuery.data]);

  const isOperationInProgress = createStore.isPending || updateStore.isPending || deleteStore.isPending;

  return {
    // Data
    stores: storesQuery.data || [],
    isLoading: storesQuery.isLoading,
    error: storesQuery.error,

    // Operations
    createStore: createStore.mutateAsync,
    updateStore: updateStore.mutateAsync,
    deleteStore: deleteStore.mutateAsync,
    checkNameAvailability: checkNameAvailability.mutateAsync,
    refreshStores,
    getStoreById,

    // Status
    isCreating: createStore.isPending,
    isUpdating: updateStore.isPending,
    isDeleting: deleteStore.isPending,
    isOperationInProgress,

    // Errors
    createError: createStore.error,
    updateError: updateStore.error,
    deleteError: deleteStore.error,
    nameCheckError: checkNameAvailability.error
  };
}

