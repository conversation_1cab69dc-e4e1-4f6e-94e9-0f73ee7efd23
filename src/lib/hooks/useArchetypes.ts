import { useState, useEffect } from 'react';
import { getArchetypes, type Archetype } from '../services/archetypes';

/**
 * Hook to fetch all active archetypes
 */
export function useArchetypes() {
  const [data, setData] = useState<Archetype[] | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchArchetypes = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const archetypes = await getArchetypes();
        setData(archetypes);
      } catch (err) {
        console.error('Error fetching archetypes:', err);
        setError(err as Error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchArchetypes();
  }, []);

  return { data, isLoading, error };
}
