import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { playersService } from '../services/players';
import type { Database } from '../supabase/types';
import { PostgrestError } from '@supabase/supabase-js';
import type { Player as RankingPlayer } from '@/types/ranking';

type Player = Database['public']['Tables']['players']['Row'];
type InsertPlayer = Database['public']['Tables']['players']['Insert'];
type UpdatePlayer = Database['public']['Tables']['players']['Update'];
// type TournamentResult = Database['public']['Tables']['tournament_results']['Row'];

export function usePlayers() {
  return useQuery<Player[], PostgrestError>({
    queryKey: ['players'],
    queryFn: () => playersService.getAll()
  });
}

export function usePlayer(id: string) {
  return useQuery<Player, PostgrestError>({
    queryKey: ['players', id],
    queryFn: () => playersService.getById(id)
  });
}

export function usePlayersRanking() {
  return useQuery<RankingPlayer[], PostgrestError>({
    queryKey: ['players', 'ranking'],
    queryFn: () => playersService.getRanking()
  });
}

export function useCreatePlayer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (player: InsertPlayer) => playersService.create(player),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['players'] });
    }
  });
}

export function useUpdatePlayer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, player }: { id: string; player: UpdatePlayer }) => 
      playersService.update(id, player),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['players'] });
      queryClient.invalidateQueries({ queryKey: ['players', id] });
    }
  });
}

export function useDeletePlayer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => playersService.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['players'] });
    }
  });
}
