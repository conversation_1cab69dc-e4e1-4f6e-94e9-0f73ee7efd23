import { useEffect } from 'react';

/**
 * Hook to lock/unlock body scroll when modals are open
 * @param locked - Whether to lock the body scroll
 */
export function useLockBodyScroll(locked: boolean) {
  useEffect(() => {
    if (typeof document === 'undefined') return; // SSR safety
    
    if (locked) {
      // Store the original overflow value to restore later
      const originalOverflow = document.body.style.overflow;
      
      // Lock body scroll
      document.body.style.overflow = 'hidden';
      
      // Cleanup function to restore scroll
      return () => {
        document.body.style.overflow = originalOverflow;
      };
    } else {
      // Ensure scroll is unlocked when locked becomes false
      document.body.style.overflow = '';
    }
  }, [locked]);
}
