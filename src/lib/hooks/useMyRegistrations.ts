import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { RegistrationService } from '../services/registrationService';
import type { MyRegistrationWithTournament } from '@/types/registration';

export function useMyRegistrations() {
  return useQuery<MyRegistrationWithTournament[]>({
    queryKey: ['my-registrations'],
    queryFn: () => RegistrationService.getMyRegistrations(),
    staleTime: 1000 * 60 * 2,
    gcTime: 1000 * 60 * 10,
  });
}

export function useUpdateRegistration() {
  const qc = useQueryClient();
  return useMutation({
    mutationFn: RegistrationService.updateRegistration,
    onSuccess: () => {
      qc.invalidateQueries({ queryKey: ['my-registrations'] });
    }
  });
}

export function useDeleteRegistration() {
  const qc = useQueryClient();
  return useMutation({
    mutationFn: (registrationId: string) => RegistrationService.deleteRegistration(registrationId),
    onSuccess: (data) => {
      qc.invalidateQueries({ queryKey: ['my-registrations'] });
      
      // Use the tournament ID returned from the service to invalidate specific queries
      if (data?.tournamentId) {
        const tournamentId = data.tournamentId;
        
        // Invalidate specific tournament queries to update player counts
        qc.invalidateQueries({ queryKey: ['tournaments', tournamentId] });
        qc.invalidateQueries({ queryKey: ['tournament-registration-status', tournamentId] });
        qc.invalidateQueries({ queryKey: ['user-registration', tournamentId] });
        qc.invalidateQueries({ queryKey: ['tournament-registrations', tournamentId] });
        
        // Also invalidate general tournament queries for lists/calendar views
        qc.invalidateQueries({ queryKey: ['tournaments'] });
        qc.invalidateQueries({ queryKey: ['tournaments', 'month'] });
        qc.invalidateQueries({ queryKey: ['tournaments', 'date'] });
        qc.invalidateQueries({ queryKey: ['tournaments', 'paginated'] });
      }
    }
  });
}
