import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  getTournamentResults, 
  saveTournamentResults, 
  hasResults,
  TournamentResultWithPlayer 
} from '../services/tournamentResults';
import { logger } from '@/lib/utils/logger';

/**
 * Hook to fetch tournament results
 * @param tournamentId - The ID of the tournament
 * @returns Query result with tournament results data
 */
export function useTournamentResults(tournamentId: string | null) {
  return useQuery<TournamentResultWithPlayer[]>({
    queryKey: ['tournamentResults', tournamentId],
    queryFn: async () => {
      if (!tournamentId) {
        return [];
      }
      return getTournamentResults(tournamentId);
    },
    enabled: !!tournamentId,
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes (was cacheTime in v4)
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

/**
 * Hook to save tournament results
 * @returns Mutation for saving tournament results
 */
export function useSaveTournamentResults() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ 
      tournamentId, 
      results 
    }: { 
      tournamentId: string;
      results: Array<{
        player_id: string;
        position: number;
        points: number;
      }>;
    }) => {
      return saveTournamentResults(tournamentId, results);
    },
    onSuccess: (_, variables) => {
      // Invalidate and refetch tournament results
      queryClient.invalidateQueries({ 
        queryKey: ['tournamentResults', variables.tournamentId] 
      });
      
      // Also invalidate the tournament query to update any counts
      queryClient.invalidateQueries({ 
        queryKey: ['tournaments'] 
      });
      
      logger.info('Tournament results saved successfully', { 
        component: 'useSaveTournamentResults',
        tournamentId: variables.tournamentId 
      });
    },
    onError: (error, variables) => {
      logger.error('Failed to save tournament results', error, { 
        component: 'useSaveTournamentResults',
        tournamentId: variables.tournamentId 
      });
    },
  });
}

/**
 * Hook to check if a tournament has results
 * @param tournamentId - The ID of the tournament
 * @returns Query result indicating if tournament has results
 */
export function useTournamentHasResults(tournamentId: string | null) {
  return useQuery<boolean>({
    queryKey: ['tournamentHasResults', tournamentId],
    queryFn: async () => {
      if (!tournamentId) {
        return false;
      }
      return hasResults(tournamentId);
    },
    enabled: !!tournamentId,
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
  });
}
