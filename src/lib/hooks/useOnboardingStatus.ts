import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { playersService } from '@/lib/services/players';
import { logger } from '@/lib/utils/logger';
import type { Database } from '@/lib/supabase/types';

type Player = Database['public']['Tables']['players']['Row'];

export interface UseOnboardingStatusReturn {
  player: Player | undefined;
  needsOnboarding: boolean;
  loading: boolean;
  error: Error | null;
  completeOnboarding: (firstName: string, lastName: string) => Promise<void>;
  isCompleting: boolean;
  completionError: Error | null;
}

const ONBOARDING_QUERY_KEY = ['onboarding', 'current-player'] as const;

export function useOnboardingStatus(enabled: boolean = true): UseOnboardingStatusReturn {
  const queryClient = useQueryClient();

  // Query to get current player and determine onboarding status
  const {
    data: player,
    isLoading: loading,
    error: queryError,
  } = useQuery({
    queryKey: ONBOARDING_QUERY_KEY,
    queryFn: async (): Promise<Player> => {
      logger.debug('Fetching current player for onboarding status', { component: 'useOnboardingStatus' });
      
      try {
        const player = await playersService.getCurrent();
        
        logger.debug('Player fetched for onboarding status', {
          component: 'useOnboardingStatus',
          playerId: player.id,
          onboardingCompleted: player.onboarding_completed,
          hasFirstName: !!player.first_name,
          hasLastName: !!player.last_name,
        });
        
        return player;
      } catch (error) {
        logger.error('Failed to fetch current player for onboarding status', error, { 
          component: 'useOnboardingStatus' 
        });
        throw error;
      }
    },
    retry: (failureCount, error: unknown) => {
      // Don't retry if user is not authenticated
      const errorObj = error as { message?: string; code?: string };
      if (errorObj?.message?.includes('User not authenticated') || errorObj?.code === 'PGRST116') {
        return false;
      }
      return failureCount < 3;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (was cacheTime)
    enabled, // Control if query should run
  });

  // Mutation to complete onboarding
  const {
    mutateAsync: completeOnboardingMutation,
    isPending: isCompleting,
    error: mutationError,
  } = useMutation({
    mutationFn: async ({ firstName, lastName }: { firstName: string; lastName: string }) => {
      logger.debug('Starting onboarding completion mutation', {
        component: 'useOnboardingStatus',
        firstNameLength: firstName?.length,
        lastNameLength: lastName?.length,
      });

      const result = await playersService.completeOnboarding(firstName, lastName);
      
      logger.debug('Onboarding completion mutation successful', {
        component: 'useOnboardingStatus',
        playerId: result.id,
        newName: result.name,
      });

      return result;
    },
    onSuccess: (updatedPlayer) => {
      logger.debug('Onboarding completed successfully, updating cache', {
        component: 'useOnboardingStatus',
        playerId: updatedPlayer.id,
      });

      // Update the cache with the new player data
      queryClient.setQueryData(ONBOARDING_QUERY_KEY, updatedPlayer);
      
      // Invalidate related queries to ensure consistency
      queryClient.invalidateQueries({ queryKey: ['players'] });
      
      logger.info('Onboarding completed and cache updated', {
        component: 'useOnboardingStatus',
        playerId: updatedPlayer.id,
        fullName: updatedPlayer.name,
      });
    },
    onError: (error) => {
      logger.error('Onboarding completion failed', error, {
        component: 'useOnboardingStatus',
      });
    },
  });

  // Wrapper function for the mutation
  const completeOnboarding = async (firstName: string, lastName: string): Promise<void> => {
    await completeOnboardingMutation({ firstName, lastName });
  };

  // Derive onboarding status
  const needsOnboarding = player ? !player.onboarding_completed : false;

  logger.debug('Onboarding status computed', {
    component: 'useOnboardingStatus',
    needsOnboarding,
    loading,
    isCompleting,
    hasPlayer: !!player,
    playerId: player?.id,
  });

  return {
    player,
    needsOnboarding,
    loading,
    error: queryError as Error | null,
    completeOnboarding,
    isCompleting,
    completionError: mutationError as Error | null,
  };
}

/**
 * Hook to check if a user needs onboarding without triggering the player creation
 * Useful for conditional rendering without side effects
 */
export function useNeedsOnboarding(): boolean {
  const { data: player } = useQuery<Player>({
    queryKey: ONBOARDING_QUERY_KEY,
    enabled: false, // Don't auto-fetch, only use cached data
  });

  return player ? !player.onboarding_completed : false;
}
