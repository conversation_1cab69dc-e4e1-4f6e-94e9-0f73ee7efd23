import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { tournamentsService } from '../services/tournaments';
import type { Database } from '../supabase/types';
import { PostgrestError } from '@supabase/supabase-js';
import { startOfMonth, endOfMonth } from "date-fns";
import { Tournament } from '@/types/calendar';

type Tables = Database['public']['Tables'];

type TournamentCreate = Omit<Tables['tournaments']['Row'], 'id' | 'created_at' | 'updated_at'>;
type TournamentUpdate = Partial<Tables['tournaments']['Row']>;

export function useTournaments() {
  return useQuery<Tournament[], PostgrestError>({
    queryKey: ['tournaments'],
    queryFn: () => tournamentsService.getAll()
  });
}

export function useTournamentsByMonth(year: number, month: number) {
  const startDate = startOfMonth(new Date(year, month));
  const endDate = endOfMonth(new Date(year, month));

  return useQuery<Tournament[], PostgrestError>({
    queryKey: ["tournaments", "month", year, month],
    queryFn: () => tournamentsService.getByMonth(startDate, endDate)
  });
}

export function useTournament(id: string) {
  return useQuery<Tournament, PostgrestError>({
    queryKey: ['tournaments', id],
    queryFn: () => tournamentsService.getById(id),
    enabled: !!id
  });
}

export function useTournamentsByDate(date: Date) {
  return useQuery<Tournament[], PostgrestError>({
    queryKey: ['tournaments', 'date', date.toISOString()],
    queryFn: () => tournamentsService.getByDate(date)
  });
}

export function useCreateTournament() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (tournament: TournamentCreate) => tournamentsService.create(tournament),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tournaments'] });
    }
  });
}

export function useUpdateTournament() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, tournament }: { id: string; tournament: TournamentUpdate }) => 
      tournamentsService.update(id, tournament),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['tournaments'] });
      queryClient.invalidateQueries({ queryKey: ['tournaments', id] });
    }
  });
}

export function useDeleteTournament() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => tournamentsService.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tournaments'] });
    }
  });
}

export function useRegisterToTournament() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ tournamentId, playerId }: { tournamentId: string; playerId: string }) => 
      tournamentsService.register(tournamentId, playerId),
    onSuccess: (_, { tournamentId }) => {
      queryClient.invalidateQueries({ queryKey: ['tournaments', tournamentId] });
    }
  });
}

export function useUnregisterFromTournament() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ tournamentId, playerId }: { tournamentId: string; playerId: string }) => 
      tournamentsService.unregister(tournamentId, playerId),
    onSuccess: (_, { tournamentId }) => {
      queryClient.invalidateQueries({ queryKey: ['tournaments', tournamentId] });
    }
  });
}

export function useTournamentsPaginated(page: number, pageSize: number = 12) {
  return useQuery({
    queryKey: ['tournaments', 'paginated', page, pageSize],
    queryFn: () => tournamentsService.getAllPaginated(page, pageSize),
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
  });
}
