import { useMemo } from 'react';
import { StoreColorScheme } from '@/types/calendar';
import { Colors } from '@/lib/constants/colors';

/**
 * Custom hook for store color management
 * Provides memoized color schemes and utilities
 */

interface UseStoreColorsOptions {
  store?: { color?: string | null; name?: string } | null;
  tournament?: { title?: string };
  tournamentId?: string;
}

interface UseStoreColorsReturn {
  colorScheme: StoreColorScheme;
  availableColors: Array<{ label: string; value: string; preview: string }>;
  getSchemeForStore: (store: { color?: string | null; name?: string } | null) => StoreColorScheme;
}

/**
 * Hook to manage store colors
 * @param options - Options for color scheme generation
 * @returns Color scheme and utilities
 */
export function useStoreColors(options: UseStoreColorsOptions = {}): UseStoreColorsReturn {
  const { store, tournament, tournamentId } = options;

  // Memoize the color scheme based on inputs
  const colorScheme = useMemo(() => {
    // If we have store data, use that
    if (store !== undefined) {
      return Colors.getStoreScheme(store, tournament);
    }
    
    // If we have a legacy tournament ID, use that
    if (tournamentId) {
      return Colors.getLegacyScheme(tournamentId);
    }
    
    // Default fallback
    return Colors.generate('gray-500');
  }, [store, tournament, tournamentId]);

  // Memoize available colors (static, but included for completeness)
  const availableColors = useMemo(() => Colors.getAvailable(), []);

  // Utility function to get scheme for any store
  const getSchemeForStore = useMemo(
    () => (storeData: { color?: string | null; name?: string } | null) => {
      return Colors.getStoreScheme(storeData, tournament);
    },
    [tournament]
  );

  return {
    colorScheme,
    availableColors,
    getSchemeForStore
  };
}

/**
 * Hook to get color scheme for multiple stores
 * Useful for calendar views with multiple tournaments
 */
export function useMultipleStoreColors(
  stores: Array<{ color?: string | null; name?: string } | null>
): Map<string, StoreColorScheme> {
  return useMemo(() => {
    const colorMap = new Map<string, StoreColorScheme>();
    
    stores.forEach((store, index) => {
      const key = store?.name || `store-${index}`;
      colorMap.set(key, Colors.getStoreScheme(store));
    });
    
    return colorMap;
  }, [stores]);
}

/**
 * Hook to get contrasting text color for a background
 * Useful for dynamic color schemes
 * @param _backgroundColor - Background color (reserved for future use)
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function useContrastingTextColor(_backgroundColor?: string): string {
  return useMemo(() => {
    // For now, return white for all backgrounds
    // This could be enhanced with proper contrast calculation
    // The backgroundColor param will be used in future enhancements
    return 'text-white';
  }, []);
}
