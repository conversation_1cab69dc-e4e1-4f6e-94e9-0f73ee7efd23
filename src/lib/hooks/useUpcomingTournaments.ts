import { useQuery } from '@tanstack/react-query';
import { supabase } from '../supabase/client';
import type { Tournament } from '@/types/calendar';

export function useUpcomingTournaments(limit: number = 3) {
  return useQuery<Tournament[]>({
    queryKey: ['tournaments', 'upcoming', limit],
    queryFn: async () => {
      // Get active season ID
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const { data: activeSeason } = await (supabase as any)
        .from('seasons')
        .select('id')
        .eq('is_active', true)
        .single();
      
      if (!activeSeason) return [];
      
      // Get upcoming tournaments from active season
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const { data, error } = await (supabase as any)
        .from('tournaments')
        .select(`
          *,
          store:stores(*),
          tournament_registrations(count)
        `)
        .eq('season_id', activeSeason.id)
        .gte('date', new Date().toISOString().split('T')[0]) // Only future tournaments
        .order('date', { ascending: true })
        .limit(limit);
      
      if (error) throw error;
      return data || [];
    },
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
  });
}
