'use client';

import { createContext, useContext, ReactNode } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useOnboardingStatus } from '@/lib/hooks/useOnboardingStatus';
import { Session, User } from '@supabase/supabase-js';
import type { Database } from '@/lib/supabase/types';

type Player = Database['public']['Tables']['players']['Row'];

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signOut: () => Promise<void>;
  // Onboarding state
  player: Player | undefined;
  needsOnboarding: boolean;
  completeOnboarding: (firstName: string, lastName: string) => Promise<void>;
  isCompleting: boolean;
  onboardingError: Error | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const { user, session, loading: authLoading, signOut } = useAuth();
  
  // Only fetch onboarding status if user is authenticated
  const {
    player,
    needsOnboarding,
    loading: onboardingLoading,
    error: onboardingError,
    completeOnboarding,
    isCompleting,
  } = useOnboardingStatus(!!user);

  // Combined loading state: wait for both auth and onboarding (if user exists)
  const loading = authLoading || (user && onboardingLoading) || false;

  const value: AuthContextType = {
    user,
    session,
    loading,
    signOut,
    player,
    needsOnboarding: user ? needsOnboarding : false,
    completeOnboarding,
    isCompleting,
    onboardingError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuthContext() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuthContext deve essere utilizzato all\'interno di un AuthProvider');
  }
  return context;
}
