/**
 * API response type definitions
 * Centralized location for all API-related types
 */

import { Tournament, Player, Store, TournamentResult } from '@/types/database';

// ============================================
// Generic API Types
// ============================================

/**
 * Generic API response wrapper
 */
export interface ApiResponse<T = unknown> {
  data?: T;
  error?: ApiError;
  message?: string;
  status: number;
  success: boolean;
}

/**
 * API error structure
 */
export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  field?: string;
  timestamp?: string;
}

/**
 * Paginated API response
 */
export interface PaginatedResponse<T> {
  data: T[];
  pagination: PaginationMeta;
}

/**
 * Pagination metadata
 */
export interface PaginationMeta {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

/**
 * API request options
 */
export interface ApiRequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: unknown;
  params?: Record<string, string | number | boolean>;
  signal?: AbortSignal;
}

// ============================================
// Supabase Response Types
// ============================================

/**
 * Supabase query response
 */
export interface SupabaseResponse<T> {
  data: T | null;
  error: SupabaseError | null;
  count?: number;
  status: number;
  statusText: string;
}

/**
 * Supabase error structure
 */
export interface SupabaseError {
  message: string;
  details?: string;
  hint?: string;
  code?: string;
}

/**
 * Supabase auth response
 */
export interface AuthResponse {
  user: AuthUser | null;
  session: AuthSession | null;
  error: AuthError | null;
}

/**
 * Auth user structure
 */
export interface AuthUser {
  id: string;
  email: string;
  email_confirmed_at?: string;
  phone?: string;
  phone_confirmed_at?: string;
  last_sign_in_at?: string;
  created_at: string;
  updated_at: string;
  user_metadata?: Record<string, unknown>;
  app_metadata?: Record<string, unknown>;
}

/**
 * Auth session structure
 */
export interface AuthSession {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  expires_at?: number;
  token_type: string;
  user: AuthUser;
}

/**
 * Auth error structure
 */
export interface AuthError {
  message: string;
  status?: number;
  code?: string;
}

// ============================================
// Tournament API Types
// ============================================

/**
 * Tournament list response
 */
export interface TournamentListResponse {
  tournaments: Tournament[];
  total: number;
  filters?: TournamentFilters;
}

/**
 * Tournament filters
 */
export interface TournamentFilters {
  stores?: string[];
  formats?: string[];
  dateFrom?: string;
  dateTo?: string;
  status?: 'upcoming' | 'ongoing' | 'completed';
}

/**
 * Tournament details response
 */
export interface TournamentDetailsResponse {
  tournament: Tournament;
  participants: TournamentParticipant[];
  results?: TournamentResult[];
  store: Store;
}

/**
 * Tournament participant
 */
export interface TournamentParticipant {
  id: string;
  playerId: string;
  playerName: string;
  deckName?: string;
  registeredAt: string;
  checkedIn: boolean;
  dropped: boolean;
}

/**
 * Tournament registration response
 */
export interface TournamentRegistrationResponse {
  success: boolean;
  registrationId: string;
  message: string;
  tournament: Tournament;
  waitlisted?: boolean;
}

/**
 * Tournament stats
 */
export interface TournamentStats {
  totalTournaments: number;
  upcomingTournaments: number;
  totalParticipants: number;
  averageParticipants: number;
  popularFormats: Array<{ format: string; count: number }>;
  topStores: Array<{ store: string; count: number }>;
}

// ============================================
// Player API Types
// ============================================

/**
 * Player list response
 */
export interface PlayerListResponse {
  players: Player[];
  total: number;
  season?: string;
}

/**
 * Player details response
 */
export interface PlayerDetailsResponse {
  player: Player;
  stats: PlayerStats;
  history: PlayerHistory[];
  achievements: PlayerAchievement[];
}

/**
 * Player stats
 */
export interface PlayerStats {
  totalGames: number;
  wins: number;
  losses: number;
  draws: number;
  winRate: number;
  totalPoints: number;
  ranking: number;
  favoriteFormat?: string;
  favoriteStore?: string;
}

/**
 * Player history entry
 */
export interface PlayerHistory {
  tournamentId: string;
  tournamentName: string;
  date: string;
  position: number;
  participants: number;
  points: number;
  deckName?: string;
}

/**
 * Player achievement
 */
export interface PlayerAchievement {
  id: string;
  name: string;
  description: string;
  icon?: string;
  unlockedAt: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

/**
 * Player ranking response
 */
export interface PlayerRankingResponse {
  rankings: PlayerRanking[];
  season: string;
  lastUpdated: string;
}

/**
 * Player ranking entry
 */
export interface PlayerRanking {
  rank: number;
  playerId: string;
  playerName: string;
  points: number;
  gamesPlayed: number;
  winRate: number;
  change?: number; // Position change from last period
}

// ============================================
// Store API Types
// ============================================

/**
 * Store list response
 */
export interface StoreListResponse {
  stores: Store[];
  total: number;
}

/**
 * Store details response
 */
export interface StoreDetailsResponse {
  store: Store;
  tournaments: Tournament[];
  stats: StoreStats;
}

/**
 * Store stats
 */
export interface StoreStats {
  totalTournaments: number;
  upcomingTournaments: number;
  totalParticipants: number;
  averageParticipants: number;
  popularFormats: string[];
  rating?: number;
}

// ============================================
// Search API Types
// ============================================

/**
 * Search request
 */
export interface SearchRequest {
  query: string;
  type?: 'all' | 'tournaments' | 'players' | 'stores';
  limit?: number;
  offset?: number;
  filters?: Record<string, unknown>;
}

/**
 * Search response
 */
export interface SearchResponse {
  results: SearchResult[];
  total: number;
  query: string;
  took: number; // Search time in ms
}

/**
 * Search result item
 */
export interface SearchResult {
  id: string;
  type: 'tournament' | 'player' | 'store';
  title: string;
  description?: string;
  url: string;
  relevance: number;
  data: Tournament | Player | Store;
}

// ============================================
// Notification API Types
// ============================================

/**
 * Notification
 */
export interface Notification {
  id: string;
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: Record<string, unknown>;
  read: boolean;
  createdAt: string;
  readAt?: string;
}

/**
 * Notification types
 */
export type NotificationType = 
  | 'tournament_reminder'
  | 'tournament_cancelled'
  | 'tournament_updated'
  | 'registration_confirmed'
  | 'results_published'
  | 'achievement_unlocked'
  | 'system';

/**
 * Notification preferences
 */
export interface NotificationPreferences {
  email: {
    tournaments: boolean;
    results: boolean;
    achievements: boolean;
    news: boolean;
  };
  push: {
    tournaments: boolean;
    results: boolean;
    achievements: boolean;
  };
}

// ============================================
// File Upload API Types
// ============================================

/**
 * File upload response
 */
export interface FileUploadResponse {
  url: string;
  publicUrl: string;
  filename: string;
  size: number;
  mimeType: string;
}

/**
 * File upload error
 */
export interface FileUploadError {
  message: string;
  maxSize?: number;
  allowedTypes?: string[];
}

// ============================================
// Analytics API Types
// ============================================

/**
 * Analytics event
 */
export interface AnalyticsEvent {
  event: string;
  properties?: Record<string, unknown>;
  timestamp: string;
  userId?: string;
  sessionId: string;
}

/**
 * Analytics response
 */
export interface AnalyticsResponse {
  success: boolean;
  eventId: string;
}

// ============================================
// Webhook Types
// ============================================

/**
 * Webhook payload
 */
export interface WebhookPayload<T = unknown> {
  event: string;
  data: T;
  timestamp: string;
  signature: string;
}

/**
 * Webhook response
 */
export interface WebhookResponse {
  received: boolean;
  processed: boolean;
  error?: string;
}

// ============================================
// Type Guards
// ============================================

/**
 * Type guard for API response
 */
export function isApiResponse<T>(value: unknown): value is ApiResponse<T> {
  return (
    typeof value === 'object' &&
    value !== null &&
    'status' in value &&
    'success' in value
  );
}

/**
 * Type guard for API error
 */
export function isApiError(value: unknown): value is ApiError {
  return (
    typeof value === 'object' &&
    value !== null &&
    'code' in value &&
    'message' in value
  );
}

/**
 * Type guard for paginated response
 */
export function isPaginatedResponse<T>(value: unknown): value is PaginatedResponse<T> {
  return (
    typeof value === 'object' &&
    value !== null &&
    'data' in value &&
    'pagination' in value &&
    Array.isArray((value as PaginatedResponse<T>).data)
  );
}

/**
 * Type guard for Supabase error
 */
export function isSupabaseError(value: unknown): value is SupabaseError {
  return (
    typeof value === 'object' &&
    value !== null &&
    'message' in value &&
    typeof (value as SupabaseError).message === 'string'
  );
}
