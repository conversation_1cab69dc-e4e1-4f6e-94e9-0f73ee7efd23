/**
 * Form-related type definitions
 * Centralized location for all form types used throughout the application
 */

import { Tournament, Player } from '@/types/database';

// ============================================
// Generic Form Types
// ============================================

/**
 * Form field validation state
 */
export interface FieldValidation {
  isValid: boolean;
  error?: string;
  touched?: boolean;
}

/**
 * Generic form field props
 */
export interface FormFieldProps<T = string> {
  name: string;
  label?: string;
  value: T;
  onChange: (value: T) => void;
  onBlur?: () => void;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  placeholder?: string;
  helperText?: string;
  className?: string;
}

/**
 * Form submission state
 */
export interface FormState<T = unknown> {
  values: T;
  errors: Partial<Record<keyof T, string>>;
  touched: Partial<Record<keyof T, boolean>>;
  isSubmitting: boolean;
  isValid: boolean;
}

/**
 * Form submission handler
 */
export type FormSubmitHandler<T = unknown> = (
  values: T,
  helpers: FormHelpers
) => void | Promise<void>;

/**
 * Form helpers for submission
 */
export interface FormHelpers {
  setSubmitting: (isSubmitting: boolean) => void;
  setErrors: (errors: Record<string, string>) => void;
  setFieldError: (field: string, error: string) => void;
  resetForm: () => void;
}

// ============================================
// Event Form Types
// ============================================

/**
 * Event form data structure
 */
export interface EventFormData {
  name: string;
  date: string;
  time: string;
  storeName: string;
  storeAddress?: string;
  storeProvince?: string;
  maxParticipants: number;
  entryFee?: number;
  prizes?: string[];
  notes?: string;
  format?: string;
  isRecurring?: boolean;
  recurringDays?: number[];
}

/**
 * Event form validation rules
 */
export interface EventFormValidation {
  name: { required: boolean; minLength?: number; maxLength?: number };
  date: { required: boolean; minDate?: Date; maxDate?: Date };
  time: { required: boolean };
  storeName: { required: boolean };
  maxParticipants: { required: boolean; min?: number; max?: number };
  entryFee: { min?: number; max?: number };
}

/**
 * Event form mode
 */
export type EventFormMode = 'create' | 'edit' | 'duplicate';

/**
 * Event form props
 */
export interface EventFormProps {
  initialData?: Partial<EventFormData>;
  mode?: EventFormMode;
  onSubmit: FormSubmitHandler<EventFormData>;
  onCancel: () => void;
  isSubmitting?: boolean;
}

// ============================================
// Registration Form Types
// ============================================

/**
 * Registration form data
 */
export interface RegistrationFormData {
  playerName: string;
  playerEmail?: string;
  playerPhone?: string;
  deckName?: string;
  deckList?: string;
  notes?: string;
  acceptedTerms: boolean;
}

/**
 * Registration form props
 */
export interface RegistrationFormProps {
  tournament: Tournament;
  onSubmit: FormSubmitHandler<RegistrationFormData>;
  onCancel: () => void;
  isSubmitting?: boolean;
}

// ============================================
// Profile Form Types
// ============================================

/**
 * Profile form data
 */
export interface ProfileFormData {
  displayName: string;
  email: string;
  bio?: string;
  avatarUrl?: string;
  preferredStore?: string;
  notifications: {
    tournaments: boolean;
    results: boolean;
    news: boolean;
  };
}

/**
 * Profile form props
 */
export interface ProfileFormProps {
  initialData?: Partial<ProfileFormData>;
  onSubmit: FormSubmitHandler<ProfileFormData>;
  isEditing?: boolean;
}

// ============================================
// Search Form Types
// ============================================

/**
 * Search form data
 */
export interface SearchFormData {
  query: string;
  filters?: {
    category?: string;
    dateFrom?: string;
    dateTo?: string;
    location?: string;
    status?: string;
  };
  sortBy?: 'date' | 'name' | 'participants' | 'location';
  sortOrder?: 'asc' | 'desc';
}

/**
 * Search form props
 */
export interface SearchFormProps {
  onSearch: (data: SearchFormData) => void;
  placeholder?: string;
  showFilters?: boolean;
  initialValues?: Partial<SearchFormData>;
}

// ============================================
// Auth Form Types
// ============================================

/**
 * Magic link auth form data
 */
export interface MagicLinkFormData {
  email: string;
}

/**
 * Auth form props
 */
export interface AuthFormProps<T = MagicLinkFormData> {
  onSubmit: FormSubmitHandler<T>;
  isSubmitting?: boolean;
  error?: string;
}

// ============================================
// Tournament Results Form Types
// ============================================

/**
 * Tournament result entry
 */
export interface TournamentResultEntry {
  playerId: string;
  playerName: string;
  position: number;
  wins: number;
  losses: number;
  draws: number;
  points: number;
  deckName?: string;
}

/**
 * Tournament results form data
 */
export interface TournamentResultsFormData {
  tournamentId: string;
  date: string;
  totalParticipants: number;
  results: TournamentResultEntry[];
  notes?: string;
}

/**
 * Tournament results form props
 */
export interface TournamentResultsFormProps {
  tournament: Tournament;
  onSubmit: FormSubmitHandler<TournamentResultsFormData>;
  onCancel: () => void;
  players?: Player[];
  isSubmitting?: boolean;
}

// ============================================
// Filter Form Types
// ============================================

/**
 * Calendar filter form
 */
export interface CalendarFilterForm {
  stores: string[];
  formats: string[];
  dateRange: {
    from?: Date;
    to?: Date;
  };
  showPastEvents: boolean;
}

/**
 * Ranking filter form
 */
export interface RankingFilterForm {
  season?: string;
  store?: string;
  minGamesPlayed?: number;
  sortBy: 'points' | 'winRate' | 'gamesPlayed' | 'name';
  sortOrder: 'asc' | 'desc';
}

// ============================================
// Validation Helper Types
// ============================================

/**
 * Validation rule
 */
export type ValidationRule<T = unknown> = (value: T) => string | undefined;

/**
 * Validation schema
 */
export type ValidationSchema<T> = {
  [K in keyof T]?: ValidationRule<T[K]> | ValidationRule<T[K]>[];
};

/**
 * Form errors
 */
export type FormErrors<T> = Partial<Record<keyof T, string>>;

/**
 * Form touched fields
 */
export type FormTouched<T> = Partial<Record<keyof T, boolean>>;

// ============================================
// Utility Types
// ============================================

/**
 * Make all properties of T optional except the ones in K
 */
export type RequireOnly<T, K extends keyof T> = Partial<T> & Pick<T, K>;

/**
 * Form field change handler
 */
export type FieldChangeHandler<T = string> = (
  name: string,
  value: T
) => void;

/**
 * Form field blur handler
 */
export type FieldBlurHandler = (name: string) => void;

/**
 * Form reset handler
 */
export type FormResetHandler = () => void;

/**
 * Form validation handler
 */
export type FormValidationHandler<T = unknown> = (
  values: T
) => FormErrors<T> | Promise<FormErrors<T>>;

// ============================================
// Export Form Constants
// ============================================

/**
 * Common validation messages
 */
export const VALIDATION_MESSAGES = {
  required: 'Questo campo è obbligatorio',
  email: 'Inserisci un indirizzo email valido',
  minLength: (min: number) => `Minimo ${min} caratteri`,
  maxLength: (max: number) => `Massimo ${max} caratteri`,
  min: (min: number) => `Il valore deve essere almeno ${min}`,
  max: (max: number) => `Il valore non può superare ${max}`,
  pattern: 'Formato non valido',
  terms: 'Devi accettare i termini e condizioni',
  date: 'Data non valida',
  future: 'La data deve essere futura',
  past: 'La data deve essere passata',
} as const;

/**
 * Common form field names
 */
export const FORM_FIELDS = {
  // Common
  name: 'name',
  email: 'email',
  
  // Event
  date: 'date',
  time: 'time',
  storeName: 'storeName',
  maxParticipants: 'maxParticipants',
  entryFee: 'entryFee',
  
  // Registration
  playerName: 'playerName',
  deckName: 'deckName',
  acceptedTerms: 'acceptedTerms',
  
  // Profile
  displayName: 'displayName',
  bio: 'bio',
  preferredStore: 'preferredStore',
} as const;
