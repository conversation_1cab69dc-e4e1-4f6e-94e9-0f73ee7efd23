/**
 * Store management types and validation schemas
 * Provides type-safe interfaces for store CRUD operations
 */

import { z } from 'zod';
import type { Database } from '@/lib/supabase/types';

// Database types
export type Store = Database['public']['Tables']['stores']['Row'];
export type InsertStore = Database['public']['Tables']['stores']['Insert'];
export type UpdateStore = Database['public']['Tables']['stores']['Update'];

// ============================================
// Validation Schemas
// ============================================

/**
 * Store validation schema with security-focused input sanitization
 */
export const storeSchema = z.object({
  name: z
    .string()
    .min(1, 'Il nome è obbligatorio')
    .max(100, 'Il nome non può superare i 100 caratteri')
    .regex(/^[a-zA-Z0-9\s\-'àáâäèéêëìíîïòóôöùúûüñç\.]+$/, 'Il nome contiene caratteri non validi')
    .transform(str => str.trim()),
  
  address: z
    .string()
    .min(1, 'L\'indirizzo è obbligatorio')
    .max(200, 'L\'indirizzo non può superare i 200 caratteri')
    .regex(/^[a-zA-Z0-9\s\-'àáâäèéêëìíîïòóôöùúûüñç\.,\/]+$/, 'L\'indirizzo contiene caratteri non validi')
    .transform(str => str.trim()),
  
  city: z
    .string()
    .min(1, 'La città è obbligatoria')
    .max(100, 'La città non può superare i 100 caratteri')
    .regex(/^[a-zA-Z\s\-'àáâäèéêëìíîïòóôöùúûüñç]+$/, 'La città contiene caratteri non validi')
    .transform(str => str.trim()),
  
  color: z
    .string()
    .optional()
    .refine(
      (val) => !val || /^[a-z]+-[0-9]{3}$/.test(val) || val === 'circolo',
      'Il colore deve essere nel formato valido (es. blue-500) o "circolo"'
    )
});

/**
 * Store update schema (all fields optional except validation)
 */
export const storeUpdateSchema = storeSchema.partial();

/**
 * Store form data type (derived from schema)
 */
export type StoreFormData = z.infer<typeof storeSchema>;

/**
 * Store update form data type
 */
export type StoreUpdateFormData = z.infer<typeof storeUpdateSchema>;

// ============================================
// API Response Types
// ============================================

/**
 * Store API response wrapper
 */
export interface StoreApiResponse {
  data?: Store;
  error?: string;
  success: boolean;
}

/**
 * Store list API response
 */
export interface StoreListApiResponse {
  data?: Store[];
  error?: string;
  success: boolean;
  total?: number;
}

// ============================================
// UI State Types
// ============================================

/**
 * Store management UI state
 */
export interface StoreManagementState {
  stores: Store[];
  selectedStore: Store | null;
  isLoading: boolean;
  error: string | null;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
}

/**
 * Store form state
 */
export interface StoreFormState {
  mode: 'create' | 'edit';
  isOpen: boolean;
  isSubmitting: boolean;
  errors: Record<string, string>;
  initialData?: Store;
}

/**
 * Store delete confirmation state
 */
export interface StoreDeleteState {
  isOpen: boolean;
  store: Store | null;
  isDeleting: boolean;
}

// ============================================
// Color Options
// ============================================

/**
 * Available color options for stores
 */
export const STORE_COLOR_OPTIONS = [
  { value: 'circolo', label: 'Circolo (Default)', color: 'bg-blue-500' },
  { value: 'blue-500', label: 'Blu', color: 'bg-blue-500' },
  { value: 'green-500', label: 'Verde', color: 'bg-green-500' },
  { value: 'red-500', label: 'Rosso', color: 'bg-red-500' },
  { value: 'purple-500', label: 'Viola', color: 'bg-purple-500' },
  { value: 'yellow-500', label: 'Giallo', color: 'bg-yellow-500' },
  { value: 'pink-500', label: 'Rosa', color: 'bg-pink-500' },
  { value: 'indigo-500', label: 'Indaco', color: 'bg-indigo-500' },
  { value: 'gray-500', label: 'Grigio', color: 'bg-gray-500' },
  { value: 'fuchsia-500', label: 'Fucsia', color: 'bg-fuchsia-500' }
] as const;

/**
 * Store color type
 */
export type StoreColor = typeof STORE_COLOR_OPTIONS[number]['value'];

// ============================================
// Error Types
// ============================================

/**
 * Store operation error types
 */
export type StoreErrorType = 
  | 'VALIDATION_ERROR'
  | 'DUPLICATE_NAME'
  | 'NOT_FOUND'
  | 'PERMISSION_DENIED'
  | 'NETWORK_ERROR'
  | 'UNKNOWN_ERROR';

/**
 * Store operation error
 */
export interface StoreError {
  type: StoreErrorType;
  message: string;
  field?: string;
  details?: Record<string, unknown>;
}

// ============================================
// Operation Types
// ============================================

/**
 * Store CRUD operation types
 */
export type StoreOperation = 'create' | 'read' | 'update' | 'delete';

/**
 * Store operation result
 */
export interface StoreOperationResult<T = Store> {
  success: boolean;
  data?: T;
  error?: StoreError;
}
