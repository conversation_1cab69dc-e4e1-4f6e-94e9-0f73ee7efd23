import type { Database } from "@/lib/supabase/types";

type Tables = Database["public"]["Tables"];

export type Season = Tables["seasons"]["Row"];

export type Tournament = Tables["tournaments"]["Row"] & {
  store: Tables["stores"]["Row"] | null;
  season?: Season;
  tournament_registrations?: {
    count: number;
  }[];
  currentPlayers?: number;
};


export type StoreColorScheme = {
  bg: string;
  bgHover: string;
  bgSelected: string;
  text: string;
  border: string;
  light: string;
  progress: string;
};

export type ViewMode = 'calendar' | 'cards';

export const VIEW_MODE_KEY = 'lpa_calendar_view_mode';

// Tournament Filters
export type SortOrder = 'nearest-first' | 'farthest-first';

export interface TournamentFilters {
  sortOrder: SortOrder;
  startDate: string | null; // ISO date string
  endDate: string | null; // ISO date string
  storeId: string | null;
}

export interface FilterOption {
  value: string;
  label: string;
}

export const FILTERS_KEY = 'lpa_calendar_filters';

export const DEFAULT_FILTERS: TournamentFilters = {
  sortOrder: 'nearest-first',
  startDate: null,
  endDate: null,
  storeId: null,
};
