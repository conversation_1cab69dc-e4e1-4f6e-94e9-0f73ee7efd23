import { Archetype } from '../lib/services/archetypes';

export interface RegistrationFormData {
  fullName: string;
  contact: string;
  deckName: string;
  archetype: string; // Opzionale in fase di iscrizione, obbligatorio prima dell'evento
  decklist: string;
}

export interface TournamentRegistration {
  id: string;
  tournament_id: string;
  player_id: string;
  registration_date: string;
  deck_name: string | null;
  archetype_id: string | null; // Ora può essere null
  deck_list_url: string | null;
  deck_list?: string | null;
  archetypes?: Archetype;
  players?: {
    id: string;
    name: string;
    first_name: string | null;
    last_name: string | null;
  };
}

export interface RegistrationFormErrors {
  fullName: boolean;
  contact: boolean;
  deckName: boolean;
  archetype: boolean;
  decklist: boolean;
}

export interface ManaboxValidationResult {
  isValid: boolean;
  error?: string;
}

// Rich registration type including tournament details for profile page
export interface MyRegistrationWithTournament extends TournamentRegistration {
  tournament?: {
    id: string;
    title: string;
    date: string; // timestamptz
    time_start: string; // time
    time_end: string;   // time
    format: string | null;
    price: string | number | null;
    store?: {
      id: string;
      name: string;
      color?: string | null;
    } | null;
  } | null;
}
