/**
 * Event handler type definitions
 * Centralized location for all event handler types
 */

import React, { MouseEvent, ChangeEvent, FormEvent, KeyboardEvent, FocusEvent, DragEvent, TouchEvent } from 'react';

// ============================================
// Mouse Event Handlers
// ============================================

/**
 * Generic mouse event handler
 */
export type MouseEventHandler<T = HTMLElement> = (event: MouseEvent<T>) => void;

/**
 * Click event handler
 */
export type ClickHandler<T = HTMLElement> = MouseEventHandler<T>;

/**
 * Double click event handler
 */
export type DoubleClickHandler<T = HTMLElement> = MouseEventHandler<T>;

/**
 * Mouse enter/leave handlers
 */
export type MouseEnterHandler<T = HTMLElement> = MouseEventHandler<T>;
export type MouseLeaveHandler<T = HTMLElement> = MouseEventHandler<T>;

/**
 * Mouse move handler
 */
export type MouseMoveHandler<T = HTMLElement> = MouseEventHandler<T>;

/**
 * Mouse down/up handlers
 */
export type MouseDownHandler<T = HTMLElement> = MouseEventHandler<T>;
export type MouseUpHandler<T = HTMLElement> = MouseEventHandler<T>;

// ============================================
// Form Event Handlers
// ============================================

/**
 * Form submit handler
 */
export type FormSubmitHandler<T = HTMLFormElement> = (event: FormEvent<T>) => void;

/**
 * Form change handler
 */
export type FormChangeHandler<T = HTMLFormElement> = (event: FormEvent<T>) => void;

/**
 * Input change handler
 */
export type InputChangeHandler<T = HTMLInputElement> = (event: ChangeEvent<T>) => void;

/**
 * Select change handler
 */
export type SelectChangeHandler<T = HTMLSelectElement> = (event: ChangeEvent<T>) => void;

/**
 * Textarea change handler
 */
export type TextareaChangeHandler<T = HTMLTextAreaElement> = (event: ChangeEvent<T>) => void;

/**
 * Checkbox change handler
 */
export type CheckboxChangeHandler = (event: ChangeEvent<HTMLInputElement>) => void;

/**
 * Radio button change handler
 */
export type RadioChangeHandler = (event: ChangeEvent<HTMLInputElement>) => void;

/**
 * File input change handler
 */
export type FileChangeHandler = (event: ChangeEvent<HTMLInputElement>) => void;

// ============================================
// Keyboard Event Handlers
// ============================================

/**
 * Generic keyboard event handler
 */
export type KeyboardEventHandler<T = HTMLElement> = (event: KeyboardEvent<T>) => void;

/**
 * Key down handler
 */
export type KeyDownHandler<T = HTMLElement> = KeyboardEventHandler<T>;

/**
 * Key up handler
 */
export type KeyUpHandler<T = HTMLElement> = KeyboardEventHandler<T>;

/**
 * Key press handler (deprecated but still used)
 */
export type KeyPressHandler<T = HTMLElement> = KeyboardEventHandler<T>;

// ============================================
// Focus Event Handlers
// ============================================

/**
 * Generic focus event handler
 */
export type FocusEventHandler<T = HTMLElement> = (event: FocusEvent<T>) => void;

/**
 * Focus handler
 */
export type FocusHandler<T = HTMLElement> = FocusEventHandler<T>;

/**
 * Blur handler
 */
export type BlurHandler<T = HTMLElement> = FocusEventHandler<T>;

// ============================================
// Drag Event Handlers
// ============================================

/**
 * Generic drag event handler
 */
export type DragEventHandler<T = HTMLElement> = (event: DragEvent<T>) => void;

/**
 * Drag start handler
 */
export type DragStartHandler<T = HTMLElement> = DragEventHandler<T>;

/**
 * Drag end handler
 */
export type DragEndHandler<T = HTMLElement> = DragEventHandler<T>;

/**
 * Drag over handler
 */
export type DragOverHandler<T = HTMLElement> = DragEventHandler<T>;

/**
 * Drop handler
 */
export type DropHandler<T = HTMLElement> = DragEventHandler<T>;

// ============================================
// Touch Event Handlers
// ============================================

/**
 * Generic touch event handler
 */
export type TouchEventHandler<T = HTMLElement> = (event: TouchEvent<T>) => void;

/**
 * Touch start handler
 */
export type TouchStartHandler<T = HTMLElement> = TouchEventHandler<T>;

/**
 * Touch end handler
 */
export type TouchEndHandler<T = HTMLElement> = TouchEventHandler<T>;

/**
 * Touch move handler
 */
export type TouchMoveHandler<T = HTMLElement> = TouchEventHandler<T>;

// ============================================
// Custom Event Handlers
// ============================================

/**
 * Value change handler (for custom inputs)
 */
export type ValueChangeHandler<T> = (value: T) => void;

/**
 * Multiple value change handler
 */
export type MultiValueChangeHandler<T> = (values: T[]) => void;

/**
 * Option select handler
 */
export type OptionSelectHandler<T> = (option: T) => void;

/**
 * Date change handler
 */
export type DateChangeHandler = (date: Date | null) => void;

/**
 * Date range change handler
 */
export type DateRangeChangeHandler = (startDate: Date | null, endDate: Date | null) => void;

/**
 * Time change handler
 */
export type TimeChangeHandler = (time: string) => void;

/**
 * Search handler
 */
export type SearchHandler = (query: string) => void;

/**
 * Filter change handler
 */
export type FilterChangeHandler<T = Record<string, unknown>> = (filters: T) => void;

/**
 * Sort change handler
 */
export type SortChangeHandler<T = string> = (sortBy: T, sortOrder: 'asc' | 'desc') => void;

/**
 * Page change handler
 */
export type PageChangeHandler = (page: number) => void;

/**
 * Toggle handler
 */
export type ToggleHandler = (isOpen: boolean) => void;

/**
 * Close handler
 */
export type CloseHandler = () => void;

/**
 * Open handler
 */
export type OpenHandler = () => void;

/**
 * Cancel handler
 */
export type CancelHandler = () => void;

/**
 * Confirm handler
 */
export type ConfirmHandler<T = void> = (data?: T) => void;

/**
 * Delete handler
 */
export type DeleteHandler<T = string> = (id: T) => void;

/**
 * Edit handler
 */
export type EditHandler<T> = (item: T) => void;

/**
 * Save handler
 */
export type SaveHandler<T> = (data: T) => void | Promise<void>;

/**
 * Load more handler
 */
export type LoadMoreHandler = () => void | Promise<void>;

/**
 * Refresh handler
 */
export type RefreshHandler = () => void | Promise<void>;

/**
 * Retry handler
 */
export type RetryHandler = () => void | Promise<void>;

// ============================================
// Async Event Handlers
// ============================================

/**
 * Async click handler
 */
export type AsyncClickHandler<T = HTMLElement> = (event: MouseEvent<T>) => Promise<void>;

/**
 * Async form submit handler
 */
export type AsyncFormSubmitHandler<T = HTMLFormElement> = (event: FormEvent<T>) => Promise<void>;

/**
 * Async value change handler
 */
export type AsyncValueChangeHandler<T> = (value: T) => Promise<void>;

/**
 * Async search handler
 */
export type AsyncSearchHandler = (query: string) => Promise<void>;

// ============================================
// Event Handler Props
// ============================================

/**
 * Common mouse event props
 */
export interface MouseEventProps<T = HTMLElement> {
  onClick?: ClickHandler<T>;
  onDoubleClick?: DoubleClickHandler<T>;
  onMouseEnter?: MouseEnterHandler<T>;
  onMouseLeave?: MouseLeaveHandler<T>;
  onMouseMove?: MouseMoveHandler<T>;
  onMouseDown?: MouseDownHandler<T>;
  onMouseUp?: MouseUpHandler<T>;
}

/**
 * Common form event props
 */
export interface FormEventProps<T = HTMLFormElement> {
  onSubmit?: FormSubmitHandler<T>;
  onChange?: FormChangeHandler<T>;
}

/**
 * Common input event props
 */
export interface InputEventProps<T = HTMLInputElement> {
  onChange?: InputChangeHandler<T>;
  onFocus?: FocusHandler<T>;
  onBlur?: BlurHandler<T>;
  onKeyDown?: KeyDownHandler<T>;
  onKeyUp?: KeyUpHandler<T>;
}

/**
 * Common keyboard event props
 */
export interface KeyboardEventProps<T = HTMLElement> {
  onKeyDown?: KeyDownHandler<T>;
  onKeyUp?: KeyUpHandler<T>;
  onKeyPress?: KeyPressHandler<T>;
}

/**
 * Common focus event props
 */
export interface FocusEventProps<T = HTMLElement> {
  onFocus?: FocusHandler<T>;
  onBlur?: BlurHandler<T>;
}

/**
 * Common drag event props
 */
export interface DragEventProps<T = HTMLElement> {
  onDragStart?: DragStartHandler<T>;
  onDragEnd?: DragEndHandler<T>;
  onDragOver?: DragOverHandler<T>;
  onDrop?: DropHandler<T>;
  draggable?: boolean;
}

/**
 * Common touch event props
 */
export interface TouchEventProps<T = HTMLElement> {
  onTouchStart?: TouchStartHandler<T>;
  onTouchEnd?: TouchEndHandler<T>;
  onTouchMove?: TouchMoveHandler<T>;
}

// ============================================
// Utility Types
// ============================================

/**
 * Extract event type from handler
 */
export type ExtractEvent<T> = T extends (event: infer E) => void ? E : never;

/**
 * Make event handler optional
 */
export type OptionalHandler<T> = T | undefined;

/**
 * Combine multiple handlers
 */
export type CombinedHandler<T> = T | T[];

/**
 * Event handler with loading state
 */
export interface HandlerWithLoading<T extends (...args: unknown[]) => unknown> {
  handler: T;
  isLoading: boolean;
}

/**
 * Event handler with error state
 */
export interface HandlerWithError<T extends (...args: unknown[]) => unknown> {
  handler: T;
  error: Error | null;
}

// ============================================
// Event Handler Factories
// ============================================

/**
 * Create a debounced event handler
 */
export function createDebouncedHandler<T extends (...args: unknown[]) => unknown>(
  handler: T,
  delay: number
): T {
  let timeoutId: NodeJS.Timeout;
  return ((...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => handler(...args), delay);
  }) as T;
}

/**
 * Create a throttled event handler
 */
export function createThrottledHandler<T extends (...args: unknown[]) => unknown>(
  handler: T,
  limit: number
): T {
  let inThrottle = false;
  return ((...args: Parameters<T>) => {
    if (!inThrottle) {
      handler(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  }) as T;
}

/**
 * Create a handler that prevents default
 */
export function createPreventDefaultHandler<T extends (event: Event | React.SyntheticEvent) => void>(
  handler: T
): T {
  return ((event: ExtractEvent<T>) => {
    if ('preventDefault' in event) {
      event.preventDefault();
    }
    handler(event);
  }) as T;
}

/**
 * Create a handler that stops propagation
 */
export function createStopPropagationHandler<T extends (event: Event | React.SyntheticEvent) => void>(
  handler: T
): T {
  return ((event: ExtractEvent<T>) => {
    if ('stopPropagation' in event) {
      event.stopPropagation();
    }
    handler(event);
  }) as T;
}
