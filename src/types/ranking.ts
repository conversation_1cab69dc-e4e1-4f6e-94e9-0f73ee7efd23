import { Database } from "@/lib/supabase/types";

type Tables = Database["public"]["Tables"];

// Player type used by ranking UI. It extends the DB players row shape for compatibility
// but we only rely on id, name, totalPoints, and tournamentsPlayed. The optional
// tournament_results remains for backward compatibility with pre-aggregation code.
export type Player = Tables["players"]["Row"] & {
  tournament_results?: (Tables["tournament_results"]["Row"] & {
    tournament: Tables["tournaments"]["Row"] & {
      store: Tables["stores"]["Row"];
    };
  })[];
  totalPoints?: number;
  tournamentsPlayed?: number;
};

export type Tournament = Tables["tournaments"]["Row"] & {
  store: Tables["stores"]["Row"];
};

export type TournamentResult = Tables["tournament_results"]["Row"];

export type Store = Tables["stores"]["Row"]; 
