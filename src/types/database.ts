/**
 * Database type aliases
 * Convenient type aliases for commonly used database types
 */

import { Tables } from '@/lib/supabase/types';

// Table row type aliases
export type Player = Tables<'players'>;
export type Tournament = Tables<'tournaments'>;
export type Store = Tables<'stores'>;
export type Season = Tables<'seasons'>;
export type TournamentResult = Tables<'tournament_results'>;
export type TournamentRegistration = Tables<'tournament_registrations'>;
export type UserRole = Tables<'user_roles'>;
export type AuditLog = Tables<'audit_log'>;
export type SecurityLog = Tables<'security_log'>;
