import { StoreColorScheme } from "@/types/calendar";

export const DAYS_OF_WEEK = ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>b"];

export const MONTHS = [
  "Gen<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Ma<PERSON>", "Giu<PERSON>",
  "Luglio", "Agosto", "Settembre", "Ottobre", "Novembre", "Dicembre"
];

/**
 * Generates a color scheme from a Tailwind color code (e.g., "blue-500")
 */
export function getStoreColorScheme(color: string): StoreColorScheme {
  const colorBase = color.replace('-500', ''); // Extract base color (e.g., "blue" from "blue-500")
  
  return {
    bg: `bg-${color}`,
    bgHover: `hover:bg-${colorBase}-600`,
    bgSelected: `bg-${colorBase}-600`,
    text: "text-white",
    border: `border-${colorBase}-400/30`,
    light: `text-${colorBase}-400`,
    progress: `bg-${color}`
  };
}
