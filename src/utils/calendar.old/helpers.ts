import { StoreColorScheme } from "@/types/calendar";
import { getStoreColorScheme } from "./constants";

export function getDaysInMonth(year: number, month: number): number {
  return new Date(year, month + 1, 0).getDate();
}

export function getFirstDayOfMonth(year: number, month: number): number {
  return new Date(year, month, 1).getDay();
}

// Get color scheme from store object or tournament data (includes color from database)
export function getStoreColorFromData(store: { color?: string | null; name?: string } | null, tournament?: { title?: string }): StoreColorScheme {
  // Special case for tournaments without store (like Top 8)
  if (!store) {
    // Check if it's a Top 8 tournament
    if (tournament?.title && tournament.title.toLowerCase().includes('top 8')) {
      return getStoreColorScheme('amber-500'); // Yellow/amber color for Top 8
    }
    // Default color for other tournaments without store
    return getStoreColorScheme('gray-500');
  }
  
  // Use database color if available
  if (store.color) {
    return getStoreColorScheme(store.color);
  }
  
  // Fallback to name-based mapping if no color in database
  return getStoreColorByName(store.name || '');
}

// Legacy function - now used as fallback for name-based mapping
export function getStoreColorByName(storeName: string): StoreColorScheme {
  // Convert store name to lowercase for case-insensitive matching
  const normalizedName = storeName.toLowerCase();
  
  // Map store names to default colors
  if (normalizedName.includes('trick room') || normalizedName.includes('circolo') || normalizedName.includes('borgo molino')) {
    return getStoreColorScheme('blue-500');
  } else if (normalizedName.includes('fantasy world')) {
    return getStoreColorScheme('fuchsia-500');
  } else if (normalizedName.includes('top') && (normalizedName.includes('8') || normalizedName.includes('deck'))) {
    return getStoreColorScheme('amber-500');
  }
  
  // Default fallback - cycle through colors based on store name length
  const defaultColors = ['blue-500', 'fuchsia-500', 'amber-500'];
  const index = storeName.length % defaultColors.length;
  return getStoreColorScheme(defaultColors[index]);
}
