import { memo, useCallback } from 'react';
import { FixedSizeList as List } from 'react-window';
import { ArrowUpDown } from "lucide-react";
import { Player } from "@/types/ranking";

interface VirtualizedPlayersTableProps {
  players: Player[];
  onPlayerClick: (player: Player) => void;
  sortConfig: {
    key: "name" | "totalPoints";
    direction: "asc" | "desc";
  };
  onSort: (key: "name" | "totalPoints") => void;
  height?: number;
}

interface RowProps {
  index: number;
  style: React.CSSProperties;
  data: {
    players: Player[];
    onPlayerClick: (player: Player) => void;
  };
}

const Row = memo(function Row({ index, style, data }: RowProps) {
  const { players, onPlayerClick } = data;
  const player = players[index];
const tournamentsPlayed = (player.tournamentsPlayed ?? (player.tournament_results?.length || 0));
  const isTopEight = index < 8;

  return (
    <div
      style={style}
      className={`flex items-center border-t border-blue-500/20 hover:bg-blue-900/20 cursor-pointer px-4
        ${isTopEight ? "bg-blue-600/15 border border-blue-400 shadow-[0_0_12px_rgba(99,102,241,0.5)]" : ""}`}
      onClick={() => onPlayerClick(player)}
    >
      <div className="flex-1 flex items-center gap-3">
        <div
          className={`w-7 h-7 rounded-full flex items-center justify-center text-sm font-medium ${
            index === 0
              ? "bg-yellow-500/20 text-yellow-300"
              : index === 1
              ? "bg-gray-400/20 text-gray-300"
              : index === 2
              ? "bg-amber-700/20 text-amber-600"
              : "bg-blue-500/20 text-blue-300"
          }`}
        >
          {index + 1}
        </div>
        <span className="font-medium flex-1">{player.name}</span>
      </div>
      <div className="w-32 text-center">
        <span className="font-medium">{player.totalPoints} pt</span>
      </div>
      <div className="w-32 text-center">
        <span className="font-medium">{tournamentsPlayed}</span>
      </div>
    </div>
  );
});

export const VirtualizedPlayersTable = memo(function VirtualizedPlayersTable({
  players,
  onPlayerClick,
  sortConfig,
  onSort,
  height = 600
}: VirtualizedPlayersTableProps) {
  const itemData = { players, onPlayerClick };

  const Header = useCallback(() => (
    <div className="bg-blue-900/30 flex items-center px-4 py-2.5 sticky top-0 z-10">
      <button
        className="flex-1 flex items-center gap-2 text-sm font-medium text-blue-300 text-left"
        onClick={() => onSort("name")}
      >
        Giocatore
        <ArrowUpDown size={14} className={sortConfig.key === "name" ? "opacity-100" : "opacity-50"} />
      </button>
      <button
        className="w-32 flex items-center justify-center gap-2 text-sm font-medium text-blue-300"
        onClick={() => onSort("totalPoints")}
      >
        Totale Punti
        <ArrowUpDown 
          size={14} 
          className={sortConfig.key === "totalPoints" ? "opacity-100" : "opacity-50"}
          style={{ 
            transform: sortConfig.key === "totalPoints" && sortConfig.direction === "desc" 
              ? "scaleY(-1)" 
              : undefined 
          }}
        />
      </button>
      <div className="w-32 text-center">
        <span className="text-sm font-medium text-blue-300">
          Tornei Giocati
        </span>
      </div>
    </div>
  ), [sortConfig, onSort]);

  if (players.length === 0) {
    return (
      <div className="text-center py-8 text-blue-300">
        Nessun giocatore trovato
      </div>
    );
  }

  // For lists with fewer than 20 items, use regular rendering
  if (players.length < 20) {
    return (
      <div className="w-full">
        <Header />
        {players.map((player, index) => (
          <Row
            key={player.id}
            index={index}
            style={{ height: 56 }}
            data={itemData}
          />
        ))}
      </div>
    );
  }

  // For large lists, use virtualization
  return (
    <div className="w-full">
      <Header />
      <List
        height={height}
        itemCount={players.length}
        itemSize={56}
        width="100%"
        itemData={itemData}
      >
        {Row}
      </List>
    </div>
  );
});
