import { memo } from 'react';
import { Search } from "lucide-react";

interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
}

export const SearchBar = memo(function SearchBar({ value, onChange }: SearchBarProps) {
  return (
    <div className="relative flex-1">
      <Search
        size={16}
        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-400"
      />
      <input
        type="text"
        placeholder="Cerca giocatore..."
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="w-full pl-9 pr-3 py-1.5 bg-black/30 border border-blue-500/30 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500/50"
      />
    </div>
  );
});
