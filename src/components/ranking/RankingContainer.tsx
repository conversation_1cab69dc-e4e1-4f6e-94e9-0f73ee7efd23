import { Player } from "@/types/ranking";
import { SearchBar } from "./SearchBar";
import { PlayersTable } from "./PlayersTable";
import { Spinner } from "@/components/ui/Spinner";

interface RankingContainerProps {
  players: Player[];
  searchQuery: string;
  onSearchChange: (value: string) => void;
  sortConfig: {
    key: "name" | "totalPoints";
    direction: "asc" | "desc";
  };
  onSort: (key: "name" | "totalPoints") => void;
  onPlayerClick: (player: Player) => void;
  isLoading?: boolean;
  error?: string;
}

export function RankingContainer({
  players,
  searchQuery,
  onSearchChange,
  sortConfig,
  onSort,
  onPlayerClick,
  isLoading,
  error,
}: RankingContainerProps) {
  if (error) {
    return (
      <div className="text-center p-4 text-red-400">
        <p>Si è verificato un errore nel caricamento della classifica.</p>
        <p className="text-sm opacity-75">{error}</p>
      </div>
    );
  }

  return (
    <div className="bg-black/20 backdrop-blur-sm rounded-xl border border-blue-500/30 overflow-hidden max-w-2xl mx-auto">
      {/* Filtri */}
      <div className="p-3 border-b border-blue-500/20">
        <div className="flex flex-col sm:flex-row gap-3">
          <SearchBar value={searchQuery} onChange={onSearchChange} />
        </div>
      </div>

      {/* Tabella */}
      <div className="overflow-x-auto min-h-[200px] relative">
        {isLoading ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <Spinner />
          </div>
        ) : players.length === 0 ? (
          <div className="text-center p-4 text-blue-300/75">
            Nessun giocatore trovato
          </div>
        ) : (
          <PlayersTable
            players={players}
            onPlayerClick={onPlayerClick}
            sortConfig={sortConfig}
            onSort={onSort}
          />
        )}
      </div>
    </div>
  );
} 