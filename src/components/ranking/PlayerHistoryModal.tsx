"use client";

import React from "react";
import { X, Calendar, MapPin, Trophy } from "lucide-react";
import { Player } from "@/types/ranking";
import { format } from "date-fns";
import { it } from "date-fns/locale";

interface PlayerHistoryModalProps {
  player: Player | null;
  isOpen: boolean;
  onClose: () => void;
}

export function PlayerHistoryModal({ player, isOpen, onClose }: PlayerHistoryModalProps) {
  if (!isOpen || !player) return null;

  // Ordina i risultati per data decrescente
  const sortedResults = [...(player.tournament_results || [])].sort((a, b) => {
    const dateA = new Date(a.tournament.date);
    const dateB = new Date(b.tournament.date);
    return dateB.getTime() - dateA.getTime();
  });

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 rounded-xl border border-blue-500/30 w-full max-w-2xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden">
        {/* Header del Modale */}
        <div className="p-3 sm:p-4 border-b border-blue-500/20 flex justify-between items-center">
          <h3 className="text-lg sm:text-xl font-bold">Storia Tornei - {player.name}</h3>
          <button
            onClick={onClose}
            className="p-1 hover:bg-blue-900/50 rounded-full transition-colors"
          >
            <X size={18} className="sm:w-5 sm:h-5" />
          </button>
        </div>

        {/* Contenuto del Modale */}
        <div className="p-3 sm:p-4 overflow-y-auto max-h-[calc(95vh-8rem)] sm:max-h-[calc(90vh-8rem)]">
          <div className="space-y-3 sm:space-y-4">
            {sortedResults.length === 0 ? (
              <div className="text-center p-4 text-blue-300/75">
                Nessun torneo giocato
              </div>
            ) : (
              sortedResults.map((result) => (
                <div
                  key={`${result.id}`}
                  className="bg-black/20 backdrop-blur-sm rounded-lg border border-blue-500/30 p-3 sm:p-4"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium mb-2 text-sm sm:text-base">
                        {result.tournament.title}
                      </h4>
                      <div className="space-y-1.5 sm:space-y-2 text-xs sm:text-sm text-blue-200">
                        <div className="flex items-center gap-2">
                          <Calendar size={12} className="sm:w-3.5 sm:h-3.5 text-blue-400" />
                          <span>
                            {format(new Date(result.tournament.date), "d MMMM yyyy", { locale: it })}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin size={12} className="sm:w-3.5 sm:h-3.5 text-blue-400" />
                          <span>{result.tournament.store.name}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col items-end ml-3 sm:ml-4">
                      <div className="flex items-center gap-1.5 sm:gap-2">
                        <Trophy size={12} className="sm:w-3.5 sm:h-3.5 text-blue-400" />
                        <span className="font-medium text-xs sm:text-sm">#{result.position}</span>
                      </div>
                      <div className="text-base sm:text-lg font-bold text-blue-300 mt-1">
                        {result.points} pt
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Footer del Modale */}
        <div className="p-3 sm:p-4 border-t border-blue-500/20 bg-black/20">
          <div className="flex justify-between items-center">
            <span className="text-blue-200 text-sm sm:text-base">Totale Punti Stagione</span>
            <span className="text-xl sm:text-2xl font-bold">{player.totalPoints} pt</span>
          </div>
        </div>
      </div>
    </div>
  );
} 