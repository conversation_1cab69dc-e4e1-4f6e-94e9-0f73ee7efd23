'use client';

import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { z } from 'zod';
import { UserPlus, AlertCircle, Loader2 } from 'lucide-react';
import { logger } from '@/lib/utils/logger';
import { useLockBodyScroll } from '@/lib/hooks/useLockBodyScroll';

// Validation schema matching server-side validation
const onboardingSchema = z.object({
  firstName: z
    .string()
    .min(2, 'Il nome deve contenere almeno 2 caratteri')
    .max(40, 'Il nome non può superare 40 caratteri')
    .regex(
      /^[A-Za-zÀ-ÿ'\s-]+$/,
      'Il nome può contenere solo lettere, spazi, apostrofi e trattini'
    ),
  lastName: z
    .string()
    .min(2, 'Il cognome deve contenere almeno 2 caratteri')
    .max(40, 'Il cognome non può superare 40 caratteri')
    .regex(
      /^[A-Za-zÀ-ÿ'\s-]+$/,
      'Il cognome può contenere solo lettere, spazi, apostrofi e trattini'
    ),
});

type OnboardingFormData = z.infer<typeof onboardingSchema>;

interface OnboardingModalProps {
  isOpen: boolean;
  onComplete: (firstName: string, lastName: string) => Promise<void>;
  isCompleting: boolean;
  error: Error | null;
}

export function OnboardingModal({ 
  isOpen, 
  onComplete, 
  isCompleting, 
  error 
}: OnboardingModalProps) {
  const [formData, setFormData] = useState<OnboardingFormData>({
    firstName: '',
    lastName: '',
  });
  const [validationErrors, setValidationErrors] = useState<
    Partial<Record<keyof OnboardingFormData, string>>
  >({});
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const [mounted, setMounted] = useState(false);

  // Ensure this only runs on client side
  useEffect(() => {
    setMounted(true);
  }, []);

  // Lock body scroll when modal is open and component is mounted
  useLockBodyScroll(mounted && isOpen);

  // Validate form data in real-time after first submit attempt
  useEffect(() => {
    if (!hasSubmitted) return;

    const result = onboardingSchema.safeParse(formData);
    if (result.success) {
      setValidationErrors({});
    } else {
      const errors: Partial<Record<keyof OnboardingFormData, string>> = {};
      result.error.errors.forEach((err) => {
        if (err.path[0]) {
          errors[err.path[0] as keyof OnboardingFormData] = err.message;
        }
      });
      setValidationErrors(errors);
    }
  }, [formData, hasSubmitted]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setHasSubmitted(true);

    logger.debug('Onboarding form submitted', {
      component: 'OnboardingModal',
      firstNameLength: formData.firstName.length,
      lastNameLength: formData.lastName.length,
    });

    // Validate form data
    const result = onboardingSchema.safeParse(formData);
    if (!result.success) {
      const errors: Partial<Record<keyof OnboardingFormData, string>> = {};
      result.error.errors.forEach((err) => {
        if (err.path[0]) {
          errors[err.path[0] as keyof OnboardingFormData] = err.message;
        }
      });
      setValidationErrors(errors);
      logger.debug('Onboarding form validation failed', {
        component: 'OnboardingModal',
        errors,
      });
      return;
    }

    setValidationErrors({});

    try {
      await onComplete(result.data.firstName.trim(), result.data.lastName.trim());
      logger.info('Onboarding completed successfully', {
        component: 'OnboardingModal',
      });
    } catch (err) {
      logger.error('Onboarding completion failed', err, {
        component: 'OnboardingModal',
      });
      // Error is handled by parent component
    }
  };

  const handleInputChange = (field: keyof OnboardingFormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Don't render anything on server side or when closed
  if (!mounted || !isOpen) {
    return null;
  }

  const modalContent = (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm"
      aria-labelledby="onboarding-title"
      aria-describedby="onboarding-description"
      role="dialog"
      aria-modal="true"
    >
      {/* Overlay to prevent background interaction */}
      <div className="absolute inset-0" aria-hidden="true" />
      
      {/* Modal Content */}
      <div className="relative w-full max-w-md mx-4 bg-gradient-to-br from-sky-900 to-blue-900 rounded-xl shadow-2xl border border-sky-500/30">
        <div className="p-8">
          {/* Header */}
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-sky-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <UserPlus className="w-8 h-8 text-sky-300" />
            </div>
            <h2 id="onboarding-title" className="text-2xl font-bold text-white mb-2">
              Completa il tuo profilo
            </h2>
            <p id="onboarding-description" className="text-sky-300 text-sm">
              Per continuare, inserisci il tuo nome e cognome. Questi dati verranno utilizzati per identificarti nei tornei.
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-6 p-4 bg-red-500/15 border border-red-500/30 rounded-lg flex items-center gap-3">
              <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
              <div className="text-red-200 text-sm">
                <p className="font-medium">Errore durante il salvataggio</p>
                <p className="mt-1 opacity-90">
                  {error.message || 'Si è verificato un errore. Riprova.'}
                </p>
              </div>
            </div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-5">
            {/* First Name */}
            <div>
              <label 
                htmlFor="firstName" 
                className="block text-sm font-medium text-sky-300 mb-2"
              >
                Nome *
              </label>
              <input
                id="firstName"
                type="text"
                value={formData.firstName}
                onChange={(e) => handleInputChange('firstName', e.target.value)}
                disabled={isCompleting}
                className="w-full px-4 py-3 bg-blue-900/50 border border-blue-700 rounded-lg text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-sky-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                placeholder="Mario"
                autoComplete="given-name"
                aria-invalid={!!validationErrors.firstName}
                aria-describedby={validationErrors.firstName ? 'firstName-error' : undefined}
              />
              {validationErrors.firstName && (
                <p id="firstName-error" className="mt-1 text-sm text-red-400" role="alert">
                  {validationErrors.firstName}
                </p>
              )}
            </div>

            {/* Last Name */}
            <div>
              <label 
                htmlFor="lastName" 
                className="block text-sm font-medium text-sky-300 mb-2"
              >
                Cognome *
              </label>
              <input
                id="lastName"
                type="text"
                value={formData.lastName}
                onChange={(e) => handleInputChange('lastName', e.target.value)}
                disabled={isCompleting}
                className="w-full px-4 py-3 bg-blue-900/50 border border-blue-700 rounded-lg text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-sky-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                placeholder="Rossi"
                autoComplete="family-name"
                aria-invalid={!!validationErrors.lastName}
                aria-describedby={validationErrors.lastName ? 'lastName-error' : undefined}
              />
              {validationErrors.lastName && (
                <p id="lastName-error" className="mt-1 text-sm text-red-400" role="alert">
                  {validationErrors.lastName}
                </p>
              )}
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isCompleting || !formData.firstName.trim() || !formData.lastName.trim()}
              className="w-full bg-sky-600 hover:bg-sky-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-sky-500 flex items-center justify-center gap-2"
            >
              {isCompleting ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin" />
                  Salvataggio...
                </>
              ) : (
                <>
                  <UserPlus className="w-5 h-5" />
                  Salva e continua
                </>
              )}
            </button>
          </form>

          {/* Footer Note */}
          <p className="mt-6 text-center text-xs text-sky-400">
            Questi dati sono necessari per partecipare ai tornei e verranno mostrati nelle classifiche.
          </p>
        </div>
      </div>
    </div>
  );

  // Render modal in portal to body
  return createPortal(modalContent, document.body);
}
