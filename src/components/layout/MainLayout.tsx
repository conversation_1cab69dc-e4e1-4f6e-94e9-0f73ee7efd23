"use client";

import React from "react";
import { Header } from "../shared/Header";
import { Footer } from "../shared/Footer";

interface MainLayoutProps {
  children: React.ReactNode;
  currentPage?: "home" | "calendar" | "ranking" | "regolamento" | "tools";
}

export function MainLayout({ children, currentPage = "home" }: MainLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 text-white">
      <Header currentPage={currentPage} />
      <main className="container mx-auto p-4 sm:p-6">
        {children}
      </main>
      <Footer />
    </div>
  );
} 