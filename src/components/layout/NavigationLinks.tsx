import Link from 'next/link';

interface NavigationLinksProps {
  className?: string;
  onLinkClick?: () => void;
}

export function NavigationLinks({ className = '', onLinkClick }: NavigationLinksProps) {
  return (
    <>
      <Link 
        href="/calendar" 
        className={`text-sm hover:text-blue-300 transition-colors ${className}`}
        onClick={onLinkClick}
      >
        Calendario
      </Link>
      <Link 
        href="/classifica" 
        className={`text-sm hover:text-blue-300 transition-colors ${className}`}
        onClick={onLinkClick}
      >
        Classifica
      </Link>
      <a 
        href="https://magic.wizards.com/en/formats/pauper" 
        target="_blank" 
        rel="noopener noreferrer" 
        className={`text-sm hover:text-blue-300 transition-colors ${className}`}
        onClick={onLinkClick}
      >
        Cos&apos;è Pauper?
      </a>
    </>
  );
} 