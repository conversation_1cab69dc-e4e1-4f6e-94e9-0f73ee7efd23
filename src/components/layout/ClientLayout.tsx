"use client";

import { QueryProvider } from "@/lib/providers/QueryProvider";
import { AuthProvider, useAuthContext } from "@/lib/providers/AuthProvider";
import { ErrorBoundary } from "@/components/shared/ErrorBoundary";
import { ErrorFallback } from "@/components/shared/ErrorFallback";
import { OnboardingModal } from "@/components/auth/OnboardingModal";
import { ReactNode } from "react";
import { usePrefetchCriticalRoutes, usePrefetchSecondaryRoutes } from "@/lib/utils/navigation";
import { Toaster } from "react-hot-toast";

interface ClientLayoutProps {
  children: ReactNode;
}

// Component that renders onboarding modal when needed
function OnboardingWrapper({ children }: { children: ReactNode }) {
  const {
    user,
    needsOnboarding,
    completeOnboarding,
    isCompleting,
    onboardingError
  } = useAuthContext();

  return (
    <>
      {children}
      {/* Render onboarding modal when user needs onboarding */}
      {user && needsOnboarding && (
        <OnboardingModal
          isOpen={needsOnboarding}
          onComplete={completeOnboarding}
          isCompleting={isCompleting}
          error={onboardingError}
        />
      )}
    </>
  );
}

export function ClientLayout({ children }: ClientLayoutProps) {
  // Prefetch critical routes for better navigation performance
  usePrefetchCriticalRoutes();
  usePrefetchSecondaryRoutes();

  return (
    <ErrorBoundary
      level="page"
      fallback={<ErrorFallback variant="fullscreen" />}
      context={{ component: 'RootLayout' }}
    >
      <QueryProvider>
        <AuthProvider>
          <OnboardingWrapper>
            {children}
          </OnboardingWrapper>
        </AuthProvider>
      </QueryProvider>

      {/* Toast notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: 'rgba(30, 58, 138, 0.9)',
            color: 'white',
            border: '1px solid rgba(59, 130, 246, 0.3)',
            backdropFilter: 'blur(8px)',
          },
          success: {
            iconTheme: {
              primary: '#10b981',
              secondary: 'white',
            },
          },
          error: {
            iconTheme: {
              primary: '#ef4444',
              secondary: 'white',
            },
          },
        }}
      />
    </ErrorBoundary>
  );
}
