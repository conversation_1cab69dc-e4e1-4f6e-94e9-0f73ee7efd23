import React, { useState } from 'react';
import { Calendar, Clock, X, ChevronDown } from 'lucide-react';
import { TournamentFilters as IFilters, SortOrder, FilterOption } from '@/types/calendar';
import { useStores } from '@/lib/hooks/useStores';

interface TournamentFiltersProps {
  filters: IFilters;
  onUpdateFilter: <K extends keyof IFilters>(key: K, value: IFilters[K]) => void;
  onResetFilters: () => void;
  hasActiveFilters: boolean;
  disabled?: boolean;
  className?: string;
}

export function TournamentFilters({
  filters,
  onUpdateFilter,
  onResetFilters,
  hasActiveFilters,
  disabled = false,
  className = '',
}: TournamentFiltersProps) {
  const { data: stores = [], isLoading: isLoadingStores } = useStores();
  const [isExpanded, setIsExpanded] = useState(false);

  // Sort order options
  const sortOrderOptions: { value: SortOrder; label: string; icon: React.ReactNode }[] = [
    {
      value: 'nearest-first',
      label: 'Prima i più vicini',
      icon: <Clock className="w-4 h-4" />,
    },
    {
      value: 'farthest-first',
      label: 'Prima i più lontani',
      icon: <Clock className="w-4 h-4" />,
    },
  ];

  // Store options
  const storeOptions: FilterOption[] = [
    { value: '', label: 'Tutti i negozi' },
    ...stores.map(store => ({
      value: (store as { id: string; name: string }).id,
      label: (store as { id: string; name: string }).name,
    })),
  ];

  // Format date for input (YYYY-MM-DD)
  const formatDateForInput = (dateString: string | null): string => {
    if (!dateString) return '';
    return dateString.split('T')[0]; // Remove time part if present
  };

  // Convert input date to ISO string
  const handleDateChange = (key: 'startDate' | 'endDate', value: string) => {
    if (value === '') {
      onUpdateFilter(key, null);
    } else {
      // Convert to ISO date string (YYYY-MM-DD format)
      onUpdateFilter(key, value);
    }
  };

  return (
    <div className={`bg-black/20 backdrop-blur-sm rounded-lg border border-blue-500/30 ${className}`}>
      <div className="space-y-0">
        {/* Collapsible Header */}
        <div className="p-4 pb-3">
          <div className="flex items-center justify-between min-h-[2rem]">
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              disabled={disabled}
              className="flex items-center gap-2 text-sm font-medium text-white hover:text-blue-300 transition-colors disabled:opacity-50"
            >
              <Calendar className="w-4 h-4" />
              Filtri
              <ChevronDown 
                className={`w-4 h-4 transition-transform duration-200 ${
                  isExpanded ? 'rotate-180' : ''
                }`} 
              />
            </button>
            {hasActiveFilters && (
              <button
                onClick={onResetFilters}
                disabled={disabled}
                className="text-xs text-blue-400 hover:text-blue-300 transition-colors disabled:opacity-50 flex items-center gap-1"
              >
                <X className="w-3 h-3" />
                Reset
              </button>
            )}
          </div>
        </div>

        {/* Collapsible Content */}
        <div className={`overflow-hidden transition-all duration-300 ${
          isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
        }`}>
          <div className="px-4 pb-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
          {/* Sort Order */}
          <div className="space-y-1">
            <label className="text-xs font-medium text-blue-300">
              Ordinamento
            </label>
            <select
              value={filters.sortOrder}
              onChange={(e) => onUpdateFilter('sortOrder', e.target.value as SortOrder)}
              disabled={disabled}
              className="w-full px-3 py-2 text-sm bg-blue-950/50 border border-blue-500/30 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"
            >
              {sortOrderOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Start Date */}
          <div className="space-y-1">
            <label className="text-xs font-medium text-blue-300">
              Data inizio
            </label>
            <input
              type="date"
              value={formatDateForInput(filters.startDate)}
              onChange={(e) => handleDateChange('startDate', e.target.value)}
              disabled={disabled}
              className="w-full px-3 py-2 text-sm bg-blue-950/50 border border-blue-500/30 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 [color-scheme:dark]"
            />
          </div>

          {/* End Date */}
          <div className="space-y-1">
            <label className="text-xs font-medium text-blue-300">
              Data fine
            </label>
            <input
              type="date"
              value={formatDateForInput(filters.endDate)}
              onChange={(e) => handleDateChange('endDate', e.target.value)}
              disabled={disabled}
              min={filters.startDate || undefined}
              className="w-full px-3 py-2 text-sm bg-blue-950/50 border border-blue-500/30 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 [color-scheme:dark]"
            />
          </div>

          {/* Store Filter */}
          <div className="space-y-1">
            <label className="text-xs font-medium text-blue-300">
              Negozio
            </label>
            <select
              value={filters.storeId || ''}
              onChange={(e) => onUpdateFilter('storeId', e.target.value || null)}
              disabled={disabled || isLoadingStores}
              className="w-full px-3 py-2 text-sm bg-blue-950/50 border border-blue-500/30 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"
            >
              {storeOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
            </div>

            {/* Active filters indicator */}
            {hasActiveFilters && (
              <div className="flex flex-wrap gap-2 pt-2 border-t border-blue-500/20">
                <span className="text-xs text-blue-400">Filtri attivi:</span>
                <div className="flex flex-wrap gap-1">
                  {filters.sortOrder !== 'nearest-first' && (
                    <span className="px-2 py-1 text-xs bg-blue-500/20 text-blue-300 rounded-full">
                      {sortOrderOptions.find(o => o.value === filters.sortOrder)?.label}
                    </span>
                  )}
                  {filters.startDate && (
                    <span className="px-2 py-1 text-xs bg-blue-500/20 text-blue-300 rounded-full">
                      Da: {new Date(filters.startDate).toLocaleDateString('it-IT')}
                    </span>
                  )}
                  {filters.endDate && (
                    <span className="px-2 py-1 text-xs bg-blue-500/20 text-blue-300 rounded-full">
                      A: {new Date(filters.endDate).toLocaleDateString('it-IT')}
                    </span>
                  )}
                  {filters.storeId && (
                    <span className="px-2 py-1 text-xs bg-blue-500/20 text-blue-300 rounded-full">
                      {(stores.find(s => (s as { id: string; name: string }).id === filters.storeId) as { id: string; name: string } | undefined)?.name || 'Negozio'}
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
