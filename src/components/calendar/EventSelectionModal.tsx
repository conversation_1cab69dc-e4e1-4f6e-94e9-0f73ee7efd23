import { useState, useEffect } from 'react';
import { X, Calendar, Clock, Search } from 'lucide-react';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';
import { Tournament } from '@/types/calendar';
import { Colors } from '@/lib/constants/colors';
import { useLockBodyScroll } from '@/lib/hooks/useLockBodyScroll';
import { logger } from '@/lib/utils/logger';

interface EventSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectEvent: (tournament: Tournament) => void;
}

export function EventSelectionModal({ isOpen, onClose, onSelectEvent }: EventSelectionModalProps) {
  const [tournaments, setTournaments] = useState<Tournament[]>([]);
  const [filteredTournaments, setFilteredTournaments] = useState<Tournament[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  // Blocca lo scroll del body quando il modal è aperto
  useLockBodyScroll(isOpen);

  useEffect(() => {
    const fetchTournaments = async () => {
      try {
        setIsLoading(true);
        const { tournamentsService } = await import('@/lib/services/tournaments');
        const data = await tournamentsService.getAll();
        
        // Ordina i tornei per data (più vicini prima)
        const sortedTournaments = [...data].sort((a, b) => 
          new Date((a as Tournament).date).getTime() - new Date((b as Tournament).date).getTime()
        );
        
        setTournaments(sortedTournaments);
        setFilteredTournaments(sortedTournaments);
      } catch (error) {
        logger.error('Errore durante il caricamento dei tornei', error, { component: 'EventSelectionModal' });
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
      fetchTournaments();
    }
  }, [isOpen]);

  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredTournaments(tournaments);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = tournaments.filter(tournament => 
      tournament.title.toLowerCase().includes(query) ||
      (tournament.store?.name && tournament.store.name.toLowerCase().includes(query)) ||
      format(new Date(tournament.date), 'dd/MM/yyyy').includes(query)
    );
    
    setFilteredTournaments(filtered);
  }, [searchQuery, tournaments]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70">
      <div className="relative w-full max-w-2xl max-h-[80vh] sm:max-h-[90vh] overflow-y-auto bg-black/20 backdrop-blur-sm border border-blue-500/30 rounded-xl p-4 sm:p-6">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-blue-300 hover:text-white transition-colors"
        >
          <X size={20} />
        </button>

        <h2 className="text-xl sm:text-2xl font-bold mb-4">Seleziona un Evento da Modificare</h2>

        <div className="relative mb-4">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Cerca per titolo, negozio o data..."
            className="w-full pl-10 pr-4 py-2 bg-blue-900/50 border border-blue-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-400" />
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : filteredTournaments.length === 0 ? (
          <p className="text-center py-8 text-blue-300">Nessun evento trovato</p>
        ) : (
          <div className="space-y-3 max-h-[50vh] sm:max-h-[60vh] overflow-y-auto pr-1">
            {filteredTournaments.map(tournament => {
              const storeColor = Colors.getStoreScheme(tournament.store, tournament);
              return (
                <button
                  key={tournament.id}
                  onClick={() => onSelectEvent(tournament)}
                  className={`w-full text-left p-3 rounded-lg border ${storeColor.border} hover:bg-blue-900/50 transition-colors`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium">{tournament.title}</h3>
                      <div className="flex items-center gap-2 text-sm text-blue-300 mt-1">
                        <Calendar size={14} />
                        <span>
                          {format(new Date(tournament.date), "d MMMM yyyy", { locale: it })}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-blue-300 mt-1">
                        <Clock size={14} />
                        <span>
                          {format(new Date(`2000-01-01T${tournament.time_start}`), "HH:mm")} - 
                          {format(new Date(`2000-01-01T${tournament.time_end}`), "HH:mm")}
                        </span>
                      </div>
                    </div>
                    <div className="text-sm text-right">
                      {tournament.store ? (
                        <span className={`px-2 py-1 rounded-full ${storeColor.bg} text-xs`}>
                          {tournament.store.name}
                        </span>
                      ) : (
                        <span className="px-2 py-1 rounded-full bg-gray-500 text-xs">
                          Nessuna location
                        </span>
                      )}
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
} 