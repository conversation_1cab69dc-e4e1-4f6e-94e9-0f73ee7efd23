"use client";

import React from "react";
import { format } from "date-fns";
import { it } from "date-fns/locale";

interface DefaultMessageProps {
  selectedDate: Date | null;
}

export function DefaultMessage({ selectedDate }: DefaultMessageProps) {
  return (
    <div className="bg-black/20 rounded-lg p-4 text-center">
      {selectedDate ? (
        <p className="text-blue-300">
          Nessun torneo programmato per il{" "}
          {format(selectedDate, "d MMMM yyyy", { locale: it })}
        </p>
      ) : (
        <p className="text-blue-300">
          Seleziona una data per vedere i dettagli dei tornei
        </p>
      )}
    </div>
  );
} 