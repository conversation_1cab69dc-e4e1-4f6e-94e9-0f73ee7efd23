"use client";

import React from "react";
import { X, Users, AlertCircle } from "lucide-react";
import { Tournament } from "@/types/calendar";
import { Colors } from "@/lib/constants/colors";
import { useTournamentRegistrations } from "@/lib/hooks/useRegistration";
import { Spinner } from "@/components/ui/Spinner";
import { validateManaboxUrl } from "@/lib/utils/manaboxValidation";
import type { TournamentRegistration } from "@/types/registration";
import { useIsAdmin } from "@/hooks/useIsAdmin";

interface TournamentRegistrationsModalProps {
  tournament: Tournament | null;
  isOpen: boolean;
  onClose: () => void;
}

// Tooltip component for status indicators
function StatusTooltip({
  children,
  content,
  show
}: {
  children: React.ReactNode;
  content: string;
  show: boolean;
}) {
  if (!show) return <>{children}</>;

  return (
    <div className="relative group">
      {children}
      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-lg border border-gray-700 whitespace-nowrap z-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
        {content}
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
      </div>
    </div>
  );
}

function isRegistrationIncomplete(reg: TournamentRegistration, tournament: Tournament): { invalid: boolean; reason?: string } {
  // Check if tournament has started - if yes, data is locked and we show the current state
  const eventDate = new Date(tournament.date);
  const dateStr = eventDate.toISOString().split('T')[0];
  const start = new Date(`${dateStr}T${tournament.time_start}`);
  const hasEventStarted = new Date() >= start;

  // Check archetype (required)
  const archetypeId = reg.archetype_id;
  const archetypeMissing = !archetypeId || archetypeId.trim() === '';

  // Check decklist (required)
  const url = reg.deck_list_url || "";
  const decklistMissing = !url.trim();
  const decklistInvalid = url.trim() && !validateManaboxUrl(url).isValid;

  // If event has started, show final state with appropriate message
  if (hasEventStarted) {
    if (archetypeMissing && decklistMissing) {
      return { invalid: true, reason: "Dati incompleti (evento iniziato)" };
    } else if (archetypeMissing) {
      return { invalid: true, reason: "Archetipo mancante (evento iniziato)" };
    } else if (decklistMissing) {
      return { invalid: true, reason: "Decklist mancante (evento iniziato)" };
    } else if (decklistInvalid) {
      return { invalid: true, reason: "Decklist non valida (evento iniziato)" };
    }
    return { invalid: false };
  }

  // Event hasn't started yet - show current issues that can still be fixed
  if (archetypeMissing) {
    return { invalid: true, reason: "Archetipo mancante" };
  }
  if (decklistMissing) {
    return { invalid: true, reason: "Decklist mancante" };
  }
  if (decklistInvalid) {
    const v = validateManaboxUrl(url);
    return { invalid: true, reason: v.error || "URL non valido" };
  }

  // All data is complete
  return { invalid: false };
}

export function TournamentRegistrationsModal({ tournament, isOpen, onClose }: TournamentRegistrationsModalProps) {
  const tournamentId = tournament?.id || "";
  const { isAdmin } = useIsAdmin();
  const { data: registrations, isLoading, error } = useTournamentRegistrations(tournamentId, tournament || undefined, isAdmin);

  if (!isOpen || !tournament) return null;

  // Check if tournament is finished OR user is admin (admins see everything)
  // Use consistent date-only logic: only tournaments from yesterday and earlier are considered "past"
  // let isTournamentFinished = false;
  // const tournamentDate = new Date(tournament.date);
  // const today = new Date();

  // Set today to start of day (00:00:00) for comparison
  // const todayStartOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());

  // Set tournament date to start of day (00:00:00) for comparison
  // const tournamentStartOfDay = new Date(tournamentDate.getFullYear(), tournamentDate.getMonth(), tournamentDate.getDate());

  // Only tournaments from yesterday and earlier are considered "finished"
  // isTournamentFinished = tournamentStartOfDay < todayStartOfDay;
  
  // const canSeeAllData = isAdmin || isTournamentFinished;
  
  const storeColor = Colors.getStoreScheme(tournament.store, tournament);

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 rounded-xl border border-blue-500/30 w-full max-w-2xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="p-3 sm:p-4 border-b border-blue-500/20 flex justify-between items-center">
          <h3 className="text-lg sm:text-xl font-bold flex items-center gap-2">
            <Users size={18} className={storeColor.light} />
            Iscritti - {tournament.title}
          </h3>
          <button
            onClick={onClose}
            className="p-1 hover:bg-blue-900/50 rounded-full transition-colors"
            aria-label="Chiudi"
          >
            <X size={18} className="sm:w-5 sm:h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-3 sm:p-4 overflow-y-auto max-h-[calc(95vh-8rem)] sm:max-h-[calc(90vh-8rem)]">
          {isLoading && (
            <div className="flex items-center justify-center py-8">
              <Spinner />
            </div>
          )}

          {error && (
            <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4 mb-4">
              <div className="flex items-center gap-2">
                <AlertCircle size={18} className="text-red-400" />
                <p className="text-sm text-red-300">Errore nel caricamento delle iscrizioni</p>
              </div>
            </div>
          )}

          {!isLoading && !error && (registrations?.length || 0) === 0 && (
            <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 text-center">
              <p className="text-sm text-blue-300">Nessun iscritto per questo torneo</p>
            </div>
          )}

          {!isLoading && !error && (registrations?.length || 0) > 0 && (
            <div className="bg-black/20 rounded-lg overflow-hidden">
              {isAdmin ? (
                // Admin view - full table with all data
                <table className="w-full">
                  <thead>
                    <tr className="bg-blue-900/30">
                      <th className="px-3 py-2 text-left text-xs font-medium text-blue-300">Giocatore</th>
                      {/*
                      <th className="px-3 py-2 text-left text-xs font-medium text-blue-300">
                        {canSeeAllData ? 'Archetipo' : 'Archetipo (nascosto)'}
                      </th>
                      */}
                      <th className="px-3 py-2 text-center text-xs font-medium text-blue-300">Stato</th>
                    </tr>
                  </thead>
                  <tbody>
                    {registrations!.map((reg) => {
                      const status = isRegistrationIncomplete(reg, tournament);
                      const playerName = (reg.players?.first_name && reg.players?.last_name)
                        ? `${reg.players.first_name} ${reg.players.last_name}`
                        : (reg.players?.name || 'Utente');

                      // Show archetype for finished tournaments OR admins (admins see everything)
                      // const archetypeName = canSeeAllData
                      //   ? (reg.archetypes?.name || '—')
                      //   : '•••';

                      return (
                        <tr key={reg.id} className="border-t border-blue-500/20">
                          <td className="px-3 py-2 text-sm font-medium text-white">{playerName}</td>
                          {/*
                          <td className="px-3 py-2 text-sm text-blue-300">
                            {canSeeAllData ? (
                              archetypeName
                            ) : (
                              <span className="text-blue-400/60 italic" title="Informazioni visibili solo dopo il torneo">
                                {archetypeName}
                              </span>
                            )}
                          </td>
                          */}
                          <td className="px-3 py-2">
                            <div className="flex items-center justify-center">
                              <StatusTooltip
                                content={status.invalid ? (status.reason || "Dati mancanti") : "Dati completi"}
                                show={status.invalid}
                              >
                                <span
                                  className={`inline-block w-2.5 h-2.5 rounded-full cursor-help ${status.invalid ? "bg-red-500" : "bg-emerald-500"}`}
                                  aria-label={status.invalid ? "Dati incompleti" : "Dati completi"}
                                />
                              </StatusTooltip>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              ) : (
                // Non-admin view - simple list with only names
                <div className="space-y-2">
                  <div className="bg-blue-900/30 px-3 py-2">
                    <h4 className="text-xs font-medium text-blue-300 uppercase tracking-wide">
                      Giocatori Iscritti ({registrations!.length})
                    </h4>
                  </div>
                  <div className="px-3 pb-3">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                      {registrations!.map((reg, index) => {
                        const playerName = (reg.players?.first_name && reg.players?.last_name)
                          ? `${reg.players.first_name} ${reg.players.last_name}`
                          : (reg.players?.name || 'Utente');

                        return (
                          <div
                            key={reg.id}
                            className="flex items-center gap-2 px-3 py-2 bg-blue-900/20 rounded-lg border border-blue-500/20"
                          >
                            <span className="text-xs text-blue-400 font-mono w-6">
                              {(index + 1).toString().padStart(2, '0')}
                            </span>
                            <span className="text-sm font-medium text-white">
                              {playerName}
                            </span>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-3 sm:p-4 border-t border-blue-500/20 bg-black/20">
          <div className="flex justify-end">
            <button 
              onClick={onClose}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg font-medium transition-colors text-sm"
            >
              Chiudi
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
