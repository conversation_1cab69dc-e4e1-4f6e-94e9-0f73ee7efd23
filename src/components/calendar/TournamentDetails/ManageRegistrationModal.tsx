"use client";

import React, { useState, useEffect } from "react";
import { X, Check, AlertCircle, CalendarIcon, Clock, MapPin, Coins, Gamepad2, Lock, Save } from "lucide-react";
import { Tournament } from "@/types/calendar";
import { Colors } from "@/lib/constants/colors";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import { useArchetypes } from "@/lib/hooks/useArchetypes";
import { useLockBodyScroll } from "@/lib/hooks/useLockBodyScroll";
import { useUserRegistration, useUpdateRegistration } from "@/lib/hooks/useRegistration";
import { Select } from "@/components/ui/Select";
import { validateManaboxUrl, formatManaboxUrl } from "@/lib/utils/manaboxValidation";

interface ManageRegistrationModalProps {
  tournament: Tournament | null;
  isOpen: boolean;
  onClose: () => void;
}

export function ManageRegistrationModal({
  tournament,
  isOpen,
  onClose
}: ManageRegistrationModalProps) {
  const { data: archetypes, isLoading: isLoadingArchetypes } = useArchetypes();
  const { data: userRegistration, isLoading: isLoadingRegistration } = useUserRegistration(tournament?.id || '');
  const { mutateAsync: updateRegistration, isPending: isUpdating, isSuccess, error: updateError } = useUpdateRegistration();

  // Lock body scroll when modal is open
  useLockBodyScroll(isOpen);

  const [formData, setFormData] = useState({
    deckName: "",
    archetype: "",
    decklist: ""
  });
  const [errors, setErrors] = useState({
    deckName: false,
    archetype: false,
    decklist: false
  });
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Load existing registration data
  useEffect(() => {
    if (userRegistration) {
      setFormData({
        deckName: userRegistration.deck_name || "",
        archetype: userRegistration.archetype_id || "",
        decklist: userRegistration.deck_list_url || ""
      });
    }
  }, [userRegistration]);

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      setFormData({ deckName: "", archetype: "", decklist: "" });
      setErrors({ deckName: false, archetype: false, decklist: false });
      setIsSubmitted(false);
    }
  }, [isOpen]);

  // Handle success
  useEffect(() => {
    if (isSuccess) {
      setIsSubmitted(true);
    }
  }, [isSuccess]);

  const handleArchetypeChange = (value: string) => {
    setFormData(prev => ({ ...prev, archetype: value }));
    setErrors(prev => ({ ...prev, archetype: false }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!userRegistration) return;

    // Reset errors
    setErrors({ deckName: false, archetype: false, decklist: false });

    // Validate decklist URL if provided
    let hasErrors = false;
    if (formData.decklist.trim()) {
      const validation = validateManaboxUrl(formData.decklist.trim());
      if (!validation.isValid) {
        setErrors(prev => ({ ...prev, decklist: true }));
        hasErrors = true;
      }
    }

    if (hasErrors) return;

    // Preparazione dati per API
    const formattedDecklistUrl = formData.decklist ? formatManaboxUrl(formData.decklist) : null;

    // Invio dati al server
    try {
      await updateRegistration({
        registrationId: userRegistration.id,
        deckName: formData.deckName.trim() || null,
        archetypeId: formData.archetype.trim() || null,
        deckListUrl: formattedDecklistUrl || null
      });
    } catch (error) {
      console.error('Error updating registration:', error);
    }
  };

  if (!isOpen || !tournament) return null;

  // Check if tournament has started
  const eventDate = new Date(tournament.date);
  const dateStr = eventDate.toISOString().split('T')[0];
  const start = new Date(`${dateStr}T${tournament.time_start}`);
  const hasEventStarted = new Date() >= start;
  const editingAllowed = !hasEventStarted;

  // Get store color scheme
  const storeColor = Colors.getStoreScheme(tournament.store, tournament);

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 rounded-xl border border-blue-500/30 w-full max-w-2xl max-h-[85vh] sm:max-h-[98vh] overflow-hidden">
        {/* Header del Modale */}
        <div className="p-3 sm:p-4 border-b border-blue-500/20 flex justify-between items-center">
          <h3 className="text-lg sm:text-xl font-bold">
            {isSubmitted ? "Iscrizione Aggiornata" : `Gestisci Iscrizione - ${tournament.title}`}
          </h3>
          <button
            onClick={onClose}
            className="p-1 hover:bg-blue-900/50 rounded-full transition-colors"
          >
            <X size={18} className="sm:w-5 sm:h-5" />
          </button>
        </div>

        {/* Contenuto del Modale */}
        <div className="p-3 sm:p-4 overflow-y-auto max-h-[calc(85vh-4rem)] sm:max-h-[calc(98vh-4rem)]">
          {isSubmitted ? (
            /* Messaggio di Successo */
            <div className="text-center py-8 space-y-4">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto">
                <Check size={32} className="text-green-400" />
              </div>
              <div>
                <h4 className="text-xl font-bold text-green-400 mb-2">Iscrizione Aggiornata!</h4>
                <p className="text-blue-200 mb-4">
                  I tuoi dati per il torneo sono stati aggiornati con successo.
                </p>
                <button
                  onClick={onClose}
                  className={`px-6 py-2 ${storeColor.bg} ${storeColor.bgHover} rounded-lg font-medium transition-colors`}
                >
                  Chiudi
                </button>
              </div>
            </div>
          ) : isLoadingRegistration ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500" />
              <span className="ml-2 text-blue-200">Caricamento...</span>
            </div>
          ) : !userRegistration ? (
            <div className="text-center py-8">
              <p className="text-red-400">Iscrizione non trovata</p>
            </div>
          ) : (
            <>
              {/* Dettagli del Torneo */}
              <div className="mb-6 p-4 bg-black/20 rounded-lg border border-blue-500/20">
                <h4 className="text-lg font-bold mb-3 text-white">{tournament.title}</h4>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                  {/* Data e Ora */}
                  <div className="flex items-center gap-2 text-blue-200">
                    <CalendarIcon size={16} className="text-blue-400" />
                    <span>
                      {format(new Date(tournament.date), "EEEE d MMMM yyyy", { locale: it })}
                    </span>
                  </div>

                  <div className="flex items-center gap-2 text-blue-200">
                    <Clock size={16} className="text-blue-400" />
                    <span>
                      {tournament.time_start.slice(0, 5)} - {tournament.time_end.slice(0, 5)}
                    </span>
                  </div>

                  {/* Negozio */}
                  <div className="flex items-center gap-2 text-blue-200">
                    <MapPin size={16} className="text-blue-400" />
                    <span>{tournament.store?.name || 'Sede da definire'}</span>
                  </div>

                  {/* Prezzo */}
                  <div className="flex items-center gap-2 text-blue-200">
                    <Coins size={16} className="text-blue-400" />
                    <span>€{tournament.price}</span>
                  </div>

                  {/* Formato */}
                  <div className="flex items-center gap-2 text-blue-200 sm:col-span-2">
                    <Gamepad2 size={16} className="text-blue-400" />
                    <span>{tournament.format}</span>
                  </div>
                </div>
              </div>

              {/* Warning se evento iniziato */}
              {hasEventStarted && (
                <div className="mb-4 p-3 bg-red-500/10 border border-red-500/30 rounded-lg">
                  <div className="flex items-start gap-2">
                    <Lock className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
                    <div className="text-sm text-red-200">
                      <p className="font-medium">Evento iniziato</p>
                      <p>Non è più possibile modificare i dati dell&apos;iscrizione dopo l&apos;inizio dell&apos;evento.</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Errore di aggiornamento */}
              {updateError && (
                <div className="mb-4 p-3 bg-red-500/10 border border-red-500/30 rounded-lg">
                  <div className="flex items-start gap-2">
                    <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
                    <div className="text-sm text-red-200">
                      <p className="font-medium">Errore nell&apos;aggiornamento</p>
                      <p>{updateError instanceof Error ? updateError.message : 'Si è verificato un errore imprevisto'}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Form di Aggiornamento */}
              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Nome Deck */}
                <div>
                  <label htmlFor="deckName" className="block text-sm font-medium mb-1 text-blue-300">
                    Nome Deck
                    <span className="text-xs text-blue-300/70 bg-blue-500/10 px-2 py-0.5 rounded ml-2">
                      opzionale
                    </span>
                  </label>
                  <input
                    type="text"
                    id="deckName"
                    value={formData.deckName}
                    onChange={(e) => setFormData(prev => ({ ...prev, deckName: e.target.value }))}
                    disabled={!editingAllowed || isUpdating}
                    className="w-full px-3 py-2 bg-blue-900/50 border border-blue-700 rounded text-white placeholder-blue-400 disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="es. Burn Aggro"
                  />
                </div>

                {/* Archetipo */}
                <div>
                  <label htmlFor="archetype" className="block text-sm font-medium mb-1 text-blue-300">
                    Archetipo
                  </label>
                  <Select
                    value={formData.archetype}
                    onChange={handleArchetypeChange}
                    options={archetypes?.map(archetype => ({
                      value: archetype.id,
                      label: archetype.name,
                      description: archetype.description || undefined
                    })) || []}
                    placeholder={isLoadingArchetypes ? "Caricamento..." : "Seleziona l'archetipo del tuo deck"}
                    hasError={errors.archetype}
                    disabled={!editingAllowed || isLoadingArchetypes || isUpdating}
                    allowEmpty={true}
                    emptyLabel="Nessun archetipo selezionato"
                  />
                  {errors.archetype && (
                    <p className="mt-1 text-xs text-red-400">Seleziona un archetipo per completare l&apos;iscrizione</p>
                  )}
                </div>

                {/* Decklist */}
                <div>
                  <label htmlFor="decklist" className="block text-sm font-medium mb-1 text-blue-300">
                    Decklist (Manabox)
                  </label>
                  <input
                    type="url"
                    id="decklist"
                    value={formData.decklist}
                    onChange={(e) => setFormData(prev => ({ ...prev, decklist: e.target.value }))}
                    disabled={!editingAllowed || isUpdating}
                    className={`w-full px-3 py-2 bg-blue-900/50 border rounded text-white placeholder-blue-400 disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.decklist ? 'border-red-500 focus:ring-red-500' : 'border-blue-700'
                    }`}
                    placeholder="https://manabox.app/decks/..."
                  />
                  {errors.decklist && (
                    <p className="mt-1 text-xs text-red-400">URL Manabox non valido</p>
                  )}
                  <p className="mt-1 text-xs text-blue-400">
                    Inserisci il link alla tua decklist su Manabox per completare l&apos;iscrizione
                  </p>
                </div>

                {/* Bottone di Aggiornamento */}
                {editingAllowed && (
                  <button
                    type="submit"
                    disabled={isUpdating}
                    className={`w-full py-3 ${storeColor.bg} ${storeColor.bgHover} disabled:opacity-50 disabled:cursor-not-allowed rounded-lg font-medium transition-colors flex items-center justify-center gap-2`}
                  >
                    {isUpdating ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white" />
                        Aggiornamento in corso...
                      </>
                    ) : (
                      <>
                        <Save size={16} />
                        Aggiorna Iscrizione
                      </>
                    )}
                  </button>
                )}
              </form>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
