"use client";

import React from "react";
import { Calendar as CalendarI<PERSON>, Clock, MapPin, Users, Coins, Trophy, Gamepad2 } from "lucide-react";
import { Tournament, StoreColorScheme } from "@/types/calendar";
import { format } from "date-fns";
import { it } from "date-fns/locale";

interface TournamentInfoProps {
  tournament: Tournament;
  storeColor: StoreColorScheme;
  currentPlayers: number;
}

export function TournamentInfo({ tournament, storeColor, currentPlayers }: TournamentInfoProps) {
  // Funzione per generare il link a Google Maps che include il nome del negozio
  const getGoogleMapsLink = (address: string, storeName: string): string => {
    if (address === "Da stabilire") return "#";
    return `https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(storeName + ', ' + address)}`;
  };

  return (
    <div className="space-y-3 sm:space-y-4">
      <div className="flex items-start gap-2 sm:gap-3">
        <div className="w-4 h-4 flex items-center justify-center mt-0.5">
          <CalendarIcon size={16} className={storeColor.light} />
        </div>
        <div>
          <p className="font-medium text-sm sm:text-base">
            {format(new Date(tournament.date), "EEEE d MMMM yyyy", { locale: it })}
          </p>
        </div>
      </div>
      
      <div className="flex items-start gap-2 sm:gap-3">
        <div className="w-4 h-4 flex items-center justify-center mt-0.5">
          <Clock size={16} className={storeColor.light} />
        </div>
        <div>
          <p className="font-medium text-sm sm:text-base">
            {format(new Date(`2000-01-01T${tournament.time_start}`), "HH:mm")} - {format(new Date(`2000-01-01T${tournament.time_end}`), "HH:mm")}
          </p>
        </div>
      </div>
      
      <div className="flex items-start gap-2 sm:gap-3">
        <div className="w-4 h-4 flex items-center justify-center mt-0.5">
          <Gamepad2 size={16} className={storeColor.light} />
        </div>
        <div>
          <p className="font-medium text-sm sm:text-base capitalize">Formato: {tournament.format}</p>
        </div>
      </div>

      {tournament.store && (
        <div className="flex items-start gap-2 sm:gap-3">
          <div className="w-4 h-4 flex items-center justify-center mt-0.5">
            <MapPin size={16} className={storeColor.light} />
          </div>
          <div>
            <p className="font-medium text-sm sm:text-base">{tournament.store.name}</p>
            <a 
              href={getGoogleMapsLink(tournament.store.address, tournament.store.name)} 
              target="_blank" 
              rel="noopener noreferrer" 
              className="text-xs sm:text-sm text-blue-300 hover:text-blue-200 transition-colors underline"
            >
              {tournament.store.address}
            </a>
          </div>
        </div>
      )}
      
      <div className="flex items-start gap-2 sm:gap-3">
        <div className="w-4 h-4 flex items-center justify-center mt-0.5">
          <Users size={16} className={storeColor.light} />
        </div>
        <div>
          <p className="font-medium text-sm sm:text-base">
            {currentPlayers} / {tournament.max_players} partecipanti
          </p>
          <div className="w-full bg-blue-900/50 rounded-full h-1.5 sm:h-2 mt-1">
            <div 
              className={`${storeColor.progress} h-1.5 sm:h-2 rounded-full`} 
              style={{ width: `${(currentPlayers / tournament.max_players) * 100}%` }}
            ></div>
          </div>
        </div>
      </div>
      
      <div className="flex items-start gap-2 sm:gap-3">
        <div className="w-4 h-4 flex items-center justify-center mt-0.5">
          <Coins size={16} className={storeColor.light} />
        </div>
        <div>
          <p className="font-medium text-sm sm:text-base">
            Costo: {tournament.price} €
          </p>
        </div>
      </div>
      
      <div className="flex items-start gap-2 sm:gap-3">
        <div className="w-4 h-4 flex items-center justify-center mt-0.5">
          <Trophy size={16} className={storeColor.light} />
        </div>
        <div>
          <p className="font-medium text-sm sm:text-base">Premio</p>
          <p className="text-xs sm:text-sm text-blue-300">{tournament.prize_pool}</p>
        </div>
      </div>
      
      <div className="pt-3 sm:pt-4 border-t border-blue-500/20">
        <p className="text-xs sm:text-sm">{tournament.description}</p>
      </div>
    </div>
  );
}
