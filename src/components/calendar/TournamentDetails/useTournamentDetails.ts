"use client";

import { useState } from "react";
import { useQueryClient } from '@tanstack/react-query';
import { Tournament } from "@/types/calendar";
import { useIsAdmin } from "@/hooks/useIsAdmin";
import { useDeleteTournament } from "@/lib/hooks/useTournaments";
import { logger } from "@/lib/utils/logger";

export function useTournamentDetails(
  tournament: Tournament | null,
  onRegistrationClick: () => void,
  onEditClick?: (tournament: Tournament) => void
) {
  const { isAdmin, loading: isAdminLoading } = useIsAdmin();
  const [showResultsModal, setShowResultsModal] = useState(false);
  const [showRegistrationsModal, setShowRegistrationsModal] = useState(false);
  const [showManageRegistrationModal, setShowManageRegistrationModal] = useState(false);
  const deleteTournament = useDeleteTournament();
  const queryClient = useQueryClient();

  // Funzione per verificare se un torneo è già passato
  const isEventPast = (date: string): boolean => {
    const eventDate = new Date(date);
    eventDate.setHours(23, 59, 59, 999); // Imposta alla fine della giornata
    const today = new Date();
    return eventDate < today;
  };

  const handleDeleteTournament = async () => {
    if (tournament) {
      try {
        await deleteTournament.mutateAsync(tournament.id);
      } catch (error) {
        logger.error("Errore durante l'eliminazione del torneo", error, { component: 'TournamentDetails' });
      }
    }
  };

  const handleEditClick = () => {
    if (tournament && onEditClick) {
      onEditClick(tournament);
    }
  };

  const handleTournamentAction = () => {
    if (tournament && isEventPast(tournament.date)) {
      setShowResultsModal(true);
    } else {
      onRegistrationClick();
    }
  };

  const closeResultsModal = () => {
    setShowResultsModal(false);
  };

  const openRegistrationsModal = () => {
    // Invalidate registrations cache to ensure fresh data when modal opens
    if (tournament?.id) {
      // Invalidate all tournament registration queries for this specific tournament
      // This ensures fresh data regardless of the includeDeckData parameter
      queryClient.invalidateQueries({
        queryKey: ['tournament-registrations', tournament.id],
        exact: false // This will match all variants with additional parameters
      });
      
      // Also invalidate tournament data to refresh participant count
      queryClient.invalidateQueries({
        queryKey: ['tournaments', tournament.id]
      });
    }
    
    setShowRegistrationsModal(true);
  };

  const closeRegistrationsModal = () => {
    setShowRegistrationsModal(false);
  };

  const openManageRegistrationModal = () => {
    setShowManageRegistrationModal(true);
  };

  const closeManageRegistrationModal = () => {
    setShowManageRegistrationModal(false);
  };

  return {
    isAdmin,
    isAdminLoading,
    showResultsModal,
    showRegistrationsModal,
    showManageRegistrationModal,
    isEventPast,
    handleDeleteTournament,
    handleEditClick,
    handleTournamentAction,
    openRegistrationsModal,
    openManageRegistrationModal,
    closeResultsModal,
    closeRegistrationsModal,
    closeManageRegistrationModal
  };
}
