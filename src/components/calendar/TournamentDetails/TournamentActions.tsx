"use client";

import React, { useState } from "react";
import { Edit, Trash2, Users, User } from "lucide-react";
import { Tournament, StoreColorScheme } from "@/types/calendar";
import { useRegistrationStatus } from "@/lib/hooks/useRegistration";
import { useAuthContext } from "@/lib/providers/AuthProvider";

interface TournamentActionsProps {
  tournament: Tournament;
  storeColor: StoreColorScheme;
  isAdmin: boolean;
  isAdminLoading: boolean;
  isPastEvent: boolean;
  currentPlayers: number;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onTournamentAction: () => void;
  onViewRegistrations?: () => void;
  onPlayerRegistrationClick?: () => void;
  onManageRegistration?: () => void; // Nuovo: per gestire la propria iscrizione
}

export function TournamentActions({
  tournament,
  storeColor,
  isAdmin,
  isAdminLoading,
  isPastEvent,
  currentPlayers,
  onEditClick,
  onDeleteClick,
  onTournamentAction,
  onViewRegistrations,
  onPlayerRegistrationClick,
  onManageRegistration
}: TournamentActionsProps) {
  const { user, player } = useAuthContext();
  const { data: isRegistered, isLoading: isRegistrationLoading } = useRegistrationStatus(tournament?.id || '');
  
  // Check if user is authenticated and has completed onboarding
  const isAuthenticated = !!user;
  const hasCompletedProfile = !!(player?.first_name && player?.last_name);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);

  const handleDeleteClick = () => {
    setShowDeleteConfirmation(true);
  };

  const handleConfirmDelete = () => {
    setShowDeleteConfirmation(false);
    onDeleteClick();
  };

  const handleCancelDelete = () => {
    setShowDeleteConfirmation(false);
  };

  if (showDeleteConfirmation) {
    return (
      <div className="mt-3 sm:mt-4 space-y-3">
        <p className="text-sm text-blue-200">Sei sicuro di voler eliminare questo torneo?</p>
        <div className="flex gap-2">
          <button 
            className="flex-1 py-2 bg-red-600 hover:bg-red-700 rounded-lg font-medium transition-colors text-sm sm:text-base"
            onClick={handleConfirmDelete}
          >
            Conferma
          </button>
          <button 
            className="flex-1 py-2 bg-blue-900/50 hover:bg-blue-900/70 rounded-lg font-medium transition-colors text-sm sm:text-base"
            onClick={handleCancelDelete}
          >
            Annulla
          </button>
        </div>
      </div>
    );
  }

  if (isAdmin && !isAdminLoading) {
    const isTournamentFull = currentPlayers >= tournament.max_players;
    const canRegister = isAuthenticated && hasCompletedProfile && !isPastEvent && !isTournamentFull;
    
    return (
      <div className="mt-3 sm:mt-4 space-y-4">
        {/* Sezione Azioni Admin */}
        <div className="space-y-2">
          <h4 className="text-xs font-medium text-blue-300 uppercase tracking-wide flex items-center gap-1">
            <Users size={12} />
            Azioni Admin
          </h4>
          <div className="flex gap-2">
            <button 
              className={`flex-1 py-2 ${storeColor.bg} ${storeColor.bgHover} rounded-lg font-medium transition-colors text-sm sm:text-base flex items-center justify-center gap-1`}
              onClick={onEditClick}
            >
              <Edit size={16} />
              Modifica
            </button>
            <button 
              className={`flex-1 py-2 ${storeColor.bg} ${storeColor.bgHover} rounded-lg font-medium transition-colors text-sm sm:text-base flex items-center justify-center gap-1`}
              onClick={handleDeleteClick}
            >
              <Trash2 size={16} />
              Elimina
            </button>
          </div>
          {onViewRegistrations && (
            <div>
              <button 
                className={`w-full py-2 ${storeColor.bg} ${storeColor.bgHover} rounded-lg font-medium transition-colors text-sm sm:text-base flex items-center justify-center gap-1`}
                onClick={onViewRegistrations}
              >
                <Users size={16} />
                Vedi iscritti
              </button>
            </div>
          )}
        </div>
        
        {/* Sezione Azioni Giocatore */}
        <div className="space-y-2">
          <h4 className="text-xs font-medium text-blue-300 uppercase tracking-wide flex items-center gap-1">
            <User size={12} />
            Azioni Giocatore
          </h4>
          <div>
            {isPastEvent ? (
              <button
                className={`w-full py-2 ${storeColor.bg} ${storeColor.bgHover} rounded-lg font-medium transition-colors text-sm sm:text-base`}
                onClick={onTournamentAction}
              >
                Risultato Evento
              </button>
            ) : !isAuthenticated ? (
              <button
                className={`w-full py-2 bg-blue-600 hover:bg-blue-700 rounded-lg font-medium transition-colors text-sm sm:text-base`}
                onClick={onPlayerRegistrationClick || onTournamentAction}
              >
                Accedi per iscriverti
              </button>
            ) : !hasCompletedProfile ? (
              <button
                className={`w-full py-2 bg-amber-600 hover:bg-amber-700 rounded-lg font-medium transition-colors text-sm sm:text-base`}
                onClick={onPlayerRegistrationClick || onTournamentAction}
              >
                Completa profilo per iscriverti
              </button>
            ) : isRegistrationLoading ? (
              <button 
                className="w-full py-2 bg-gray-600 rounded-lg font-medium text-sm sm:text-base" 
                disabled
              >
                Controllo iscrizione...
              </button>
            ) : isRegistered ? (
              <button 
                className="w-full py-2 bg-green-600 hover:bg-green-700 rounded-lg font-medium transition-colors text-sm sm:text-base"
                onClick={onPlayerRegistrationClick || onTournamentAction}
              >
                Gestisci iscrizione
              </button>
            ) : isTournamentFull ? (
              <button 
                className="w-full py-2 bg-gray-600 rounded-lg font-medium text-sm sm:text-base" 
                disabled
              >
                Torneo Completo
              </button>
            ) : canRegister ? (
              <button 
                className={`w-full py-2 ${storeColor.bg} ${storeColor.bgHover} rounded-lg font-medium transition-colors text-sm sm:text-base`}
                onClick={onPlayerRegistrationClick || onTournamentAction}
              >
                Iscriviti al torneo
              </button>
            ) : (
              <button 
                className="w-full py-2 bg-gray-600 rounded-lg font-medium text-sm sm:text-base" 
                disabled
              >
                Non disponibile
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  // For non-admin users, show different buttons based on registration status
  const isTournamentFull = currentPlayers >= tournament.max_players;

  return (
    <div className="mt-3 sm:mt-4 space-y-2">
      {/* Main action button */}
      <button
        className={`w-full py-2 ${storeColor.bg} ${storeColor.bgHover} rounded-lg font-medium transition-colors text-sm sm:text-base`}
        onClick={
          isPastEvent
            ? onTournamentAction
            : isRegistered && onManageRegistration
              ? onManageRegistration
              : (onPlayerRegistrationClick || onTournamentAction)
        }
        disabled={!isPastEvent && isTournamentFull && !isRegistered}
      >
        {isPastEvent
          ? "Risultato Evento"
          : !isAuthenticated
            ? "Accedi per iscriverti"
            : !hasCompletedProfile
              ? "Completa profilo per iscriverti"
              : isRegistrationLoading
                ? "Controllo iscrizione..."
                : isRegistered
                  ? "Gestisci iscrizione"
                  : isTournamentFull
                    ? "Torneo Completo"
                    : "Iscriviti al torneo"
        }
      </button>

      {/* View registrations button for registered users */}
      {isAuthenticated && isRegistered && onViewRegistrations && (
        <button
          className={`w-full py-2 bg-blue-600 hover:bg-blue-700 rounded-lg font-medium transition-colors text-sm sm:text-base flex items-center justify-center gap-1`}
          onClick={onViewRegistrations}
        >
          <Users size={16} />
          Vedi iscritti
        </button>
      )}
    </div>
  );
}
