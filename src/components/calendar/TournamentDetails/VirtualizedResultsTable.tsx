import { memo, useMemo } from 'react';
import { FixedSizeList as List } from 'react-window';
import { Trophy, Medal } from 'lucide-react';

interface Result {
  id: string;
  position: number;
  player_id: string;
  points: number;
  player?: {
    id: string;
    name: string;
  };
}

interface VirtualizedResultsTableProps {
  results: Result[];
  height?: number;
}

interface RowProps {
  index: number;
  style: React.CSSProperties;
  data: Result[];
}

const Row = memo(function Row({ index, style, data }: RowProps) {
  const result = data[index];
  const isTop3 = result.position <= 3;
  const isWinner = result.position === 1;

  return (
    <div
      style={style}
      className={`flex items-center px-4 py-2 border-b border-blue-500/20 hover:bg-blue-900/20 transition-colors
        ${isTop3 ? 'bg-blue-600/10' : ''}
        ${isWinner ? 'bg-yellow-500/10' : ''}`}
    >
      <div className="w-16 flex justify-center">
        {result.position === 1 && <Trophy className="w-5 h-5 text-yellow-500" />}
        {result.position === 2 && <Medal className="w-5 h-5 text-gray-400" />}
        {result.position === 3 && <Medal className="w-5 h-5 text-amber-600" />}
        {result.position > 3 && (
          <span className="text-sm text-blue-300">{result.position}°</span>
        )}
      </div>
      <div className="flex-1 font-medium">
        {result.player?.name || 'Giocatore sconosciuto'}
      </div>
      <div className="w-20 text-right font-semibold">
        {result.points} pt
      </div>
    </div>
  );
});

export const VirtualizedResultsTable = memo(function VirtualizedResultsTable({
  results,
  height = 400
}: VirtualizedResultsTableProps) {
  // Sort results by position
  const sortedResults = useMemo(() => 
    [...results].sort((a, b) => a.position - b.position),
    [results]
  );

  if (sortedResults.length === 0) {
    return (
      <div className="text-center py-8 text-blue-300">
        Nessun risultato disponibile
      </div>
    );
  }

  // For small lists, use regular rendering
  if (sortedResults.length < 15) {
    return (
      <div className="w-full">
        <div className="bg-blue-900/30 px-4 py-2 font-semibold text-sm text-blue-300 flex items-center">
          <div className="w-16 text-center">Pos.</div>
          <div className="flex-1">Giocatore</div>
          <div className="w-20 text-right">Punti</div>
        </div>
        {sortedResults.map((result, index) => (
          <Row
            key={result.id}
            index={index}
            style={{ height: 48 }}
            data={sortedResults}
          />
        ))}
      </div>
    );
  }

  // For large lists, use virtualization
  return (
    <div className="w-full">
      <div className="bg-blue-900/30 px-4 py-2 font-semibold text-sm text-blue-300 flex items-center sticky top-0 z-10">
        <div className="w-16 text-center">Pos.</div>
        <div className="flex-1">Giocatore</div>
        <div className="w-20 text-right">Punti</div>
      </div>
      <List
        height={height}
        itemCount={sortedResults.length}
        itemSize={48}
        width="100%"
        itemData={sortedResults}
      >
        {Row}
      </List>
    </div>
  );
});
