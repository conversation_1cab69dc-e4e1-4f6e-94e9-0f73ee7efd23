"use client";

import React from "react";
import { Tournament } from "@/types/calendar";
import { Colors } from "@/lib/constants/colors";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import { TournamentResultsModal } from "./TournamentResultsModal";
import { TournamentInfo } from "./TournamentInfo";
import { TournamentActions } from "./TournamentActions";
import { useTournamentDetails } from "./useTournamentDetails";
import { useTournament } from "@/lib/hooks/useTournaments";
import { TournamentRegistrationsModal } from "./TournamentRegistrationsModal";
import { ManageRegistrationModal } from "./ManageRegistrationModal";

interface TournamentDetailsProps {
  selectedTournament: Tournament | null;
  selectedDate: Date | null;
  onRegistrationClick: () => void;
  onEditClick?: (tournament: Tournament) => void;
}

export function TournamentDetails({ 
  selectedTournament, 
  selectedDate, 
  onRegistrationClick,
  onEditClick
}: TournamentDetailsProps) {
  const {
    isAdmin,
    isAdminLoading,
    showResultsModal,
    showRegistrationsModal,
    showManageRegistrationModal,
    isEventPast,
    handleDeleteTournament,
    handleEditClick,
    handleTournamentAction,
    openRegistrationsModal,
    openManageRegistrationModal,
    closeResultsModal,
    closeRegistrationsModal,
    closeManageRegistrationModal
  } = useTournamentDetails(selectedTournament, onRegistrationClick, onEditClick);

  // Hook deve essere chiamato sempre, non condizionatamente
  const { data: freshTournament } = useTournament(selectedTournament?.id || '');

  if (selectedTournament) {
    // Usa dati aggiornati se disponibili, altrimenti quelli originali
    const tournamentData = freshTournament || selectedTournament;
    
    const storeColor = Colors.getStoreScheme(tournamentData.store, tournamentData);
    const currentPlayers = tournamentData.tournament_registrations?.[0]?.count || 0;
    const isPastEvent = isEventPast(tournamentData.date);
    
    return (
      <>
        <div className={`bg-black/20 backdrop-blur-sm rounded-xl border ${storeColor.border} p-4 sm:p-6 h-full`}>
          <h3 className="text-lg sm:text-xl font-bold mb-3 sm:mb-4">{tournamentData.title}</h3>
          
          <TournamentInfo 
            tournament={tournamentData}
            storeColor={storeColor}
            currentPlayers={currentPlayers}
          />
          
          <TournamentActions
            tournament={tournamentData}
            storeColor={storeColor}
            isAdmin={isAdmin}
            isAdminLoading={isAdminLoading}
            isPastEvent={isPastEvent}
            currentPlayers={currentPlayers}
            onEditClick={handleEditClick}
            onDeleteClick={handleDeleteTournament}
            onTournamentAction={handleTournamentAction}
            onViewRegistrations={openRegistrationsModal}
            onPlayerRegistrationClick={onRegistrationClick}
            onManageRegistration={openManageRegistrationModal}
          />
        </div>
        
        {/* Modale per i risultati degli eventi passati */}
        {showResultsModal && (
          <TournamentResultsModal 
            tournament={tournamentData}
            isOpen={showResultsModal}
            onClose={closeResultsModal}
          />
        )}

        {/* Modale iscritti */}
        {showRegistrationsModal && (
          <TournamentRegistrationsModal
            tournament={tournamentData}
            isOpen={showRegistrationsModal}
            onClose={closeRegistrationsModal}
          />
        )}

        {/* Modale gestione iscrizione */}
        {showManageRegistrationModal && (
          <ManageRegistrationModal
            tournament={tournamentData}
            isOpen={showManageRegistrationModal}
            onClose={closeManageRegistrationModal}
          />
        )}
      </>
    );
  }
  
  if (selectedDate) {
    return (
      <div className="bg-black/20 backdrop-blur-sm rounded-xl border border-blue-500/30 p-4 sm:p-6 h-full">
        <h3 className="text-lg sm:text-xl font-bold mb-3 sm:mb-4">
          {format(selectedDate, "EEEE d MMMM yyyy", { locale: it })}
        </h3>
        
        <p className="text-blue-300 text-sm sm:text-base">Nessun torneo programmato per questa data.</p>
      </div>
    );
  }
  
  return (
    <div className="bg-black/20 backdrop-blur-sm rounded-xl border border-blue-500/30 p-4 sm:p-6 h-full">
      <h3 className="text-lg sm:text-xl font-bold mb-3 sm:mb-4">Dettagli Torneo</h3>
      <p className="text-blue-300 text-sm sm:text-base">Seleziona una data nel calendario per visualizzare i dettagli del torneo.</p>
    </div>
  );
}
