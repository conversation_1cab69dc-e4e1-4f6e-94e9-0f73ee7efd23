import { useState } from 'react';
import { ChevronDown, Plus, Edit } from 'lucide-react';
import { useAuthContext } from '@/lib/providers/AuthProvider';
import { useIsAdmin } from '@/hooks/useIsAdmin';

interface CalendarActionsProps {
  onAddEvent: () => void;
  onEditEvent: () => void;
}

export function CalendarActions({ onAddEvent, onEditEvent }: CalendarActionsProps) {
  const { user, loading: authLoading } = useAuthContext();
  const { isAdmin, loading: adminLoading } = useIsAdmin();
  const [isOpen, setIsOpen] = useState(false);

  if (authLoading || adminLoading) return null;
  if (!user || !isAdmin) return null;

  return (
    <div className="relative inline-block">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-1 px-3 py-1.5 bg-black/20 backdrop-blur-sm border border-blue-500/30 hover:bg-black/30 rounded-lg text-sm font-medium transition-colors"
      >
        Azioni Calendario
        <ChevronDown size={16} className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-1 w-48 bg-black/20 backdrop-blur-sm border border-blue-500/30 rounded-lg shadow-lg z-10">
          <div className="py-1">
            <button
              onClick={() => {
                setIsOpen(false);
                onAddEvent();
              }}
              className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm hover:bg-blue-900/50 transition-colors"
            >
              <Plus size={16} />
              Aggiungi Evento
            </button>
            <button
              onClick={() => {
                setIsOpen(false);
                onEditEvent();
              }}
              className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm hover:bg-blue-900/50 transition-colors"
            >
              <Edit size={16} />
              Modifica Evento
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
