import { useEffect, useState } from 'react';
import { Colors } from '@/lib/constants/colors';
import type { Database } from '@/lib/supabase/types';
import { logger } from '@/lib/utils/logger';

type Store = Database['public']['Tables']['stores']['Row'];

export function CalendarLegend() {
  const [stores, setStores] = useState<Store[]>([]);
  const [hasTop8, setHasTop8] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const { supabase } = await import('@/lib/supabase/client');
        
        // Fetch stores
        const { data: storesData, error: storesError } = await supabase
          .from('stores')
          .select('*');
        
        if (storesError) {
          logger.error('Error loading stores', storesError, { component: 'CalendarLegend' });
          setStores([]); // Set empty array on error
        } else if (storesData) {
          setStores(storesData);
        }
        
        // Check if there are Top 8 tournaments
        const { data: top8Data, error: top8Error } = await supabase
          .from('tournaments')
          .select('id')
          .ilike('title', '%top 8%')
          .limit(1);
        
        if (top8Error) {
          logger.error('Error checking Top 8 tournaments', top8Error, { component: 'CalendarLegend' });
        } else {
          setHasTop8((top8Data || []).length > 0);
        }
      } catch (error) {
        logger.error('Error loading data', error, { component: 'CalendarLegend' });
      }
    };
    
    fetchData();
  }, []);

  return (
    <div className="p-3 sm:p-4 border-t border-blue-500/20">
      <div className="flex flex-wrap items-center gap-2 sm:gap-3 text-xs sm:text-sm">
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 sm:w-3 sm:h-3 rounded-full ring-2 ring-blue-500"></div>
          <span>Oggi</span>
        </div>
        {stores.map(store => {
          const storeColor = Colors.getStoreScheme(store);
          return (
            <div key={store.id} className="flex items-center gap-1">
              <div className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full ${storeColor.bg}`}></div>
              <span>{store.name}</span>
            </div>
          );
        })}
        {hasTop8 && (
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 sm:w-3 sm:h-3 rounded-full bg-amber-500"></div>
            <span>Top 8</span>
          </div>
        )}
      </div>
    </div>
  );
}
