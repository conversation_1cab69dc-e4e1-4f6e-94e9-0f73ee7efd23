"use client";

import React from "react";

interface SelectedDateInfoProps {
  selectedDate: Date;
}

export function SelectedDateInfo({ selectedDate }: SelectedDateInfoProps) {
  return (
    <div className="bg-black/20 backdrop-blur-sm rounded-xl border border-blue-500/30 p-4 sm:p-6 h-full">
      <h3 className="text-lg sm:text-xl font-bold mb-3 sm:mb-4">
        {selectedDate.toLocaleDateString('it-IT', { 
          weekday: 'long', 
          day: 'numeric', 
          month: 'long', 
          year: 'numeric' 
        })}
      </h3>
      
      <p className="text-blue-300 text-sm sm:text-base">Nessun torneo programmato per questa data.</p>
    </div>
  );
} 