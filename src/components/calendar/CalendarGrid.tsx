"use client";

import React, { useMemo, useCallback } from "react";
import { Tournament } from "@/types/calendar";
import { Colors } from "@/lib/constants/colors";
// import { DefaultMessage } from "./DefaultMessage";
// import { TournamentDetails } from "./TournamentDetails";
// import { format } from "date-fns";
// import { it } from "date-fns/locale";
import { useAuthContext } from "@/lib/providers/AuthProvider";
import { supabase } from "@/lib/supabase/client";
import { logger } from '@/lib/utils/logger';

interface CalendarGridProps {
  currentMonth: number;
  currentYear: number;
  firstDayOfMonth: number;
  daysInMonth: number;
  tournamentDays: Record<number, Tournament[]>;
  selectedDate: Date | null;
  selectedTournament: Tournament | null;
  onDayClick: (day: number) => void;
  onTournamentClick: (tournament: Tournament) => void;
  onRegistrationClick: () => void;
  onCreateEvent?: (date: Date) => void;
}

export function CalendarGrid({
  currentMonth,
  currentYear,
  firstDayOfMonth,
  daysInMonth,
  tournamentDays,
  selectedDate,
  // selectedTournament,
  onDayClick,
  // onTournamentClick,
  // onRegistrationClick,
  onCreateEvent
}: CalendarGridProps) {
  const days = useMemo(() => Array.from({ length: daysInMonth }, (_, i) => i + 1), [daysInMonth]);
  const emptyDays = useMemo(() => Array.from({ length: firstDayOfMonth }, (_, i) => i), [firstDayOfMonth]);
  const { user } = useAuthContext();
  const [isAdmin, setIsAdmin] = React.useState(false);
  
  // Verifica se l'utente è un admin quando il componente viene montato o l'utente cambia
  React.useEffect(() => {
    const checkAdminRole = async () => {
      if (!user) {
        setIsAdmin(false);
        return;
      }

      try {
        // Verifica se l'utente è admin
        const { data, error } = await supabase.rpc('is_admin');

        if (error) {
          logger.error('Errore durante la verifica del ruolo admin', error, { component: 'CalendarGrid' });
          setIsAdmin(false);
        } else {
          setIsAdmin(!!data);
        }
      } catch (error) {
        logger.error('Errore durante la verifica del ruolo admin', error, { component: 'CalendarGrid' });
        setIsAdmin(false);
      }
    };

    checkAdminRole();
  }, [user]);
  
  // Ottieni la data attuale
  const today = new Date();
  const isCurrentMonth = today.getMonth() === currentMonth && today.getFullYear() === currentYear;
  const currentDay = today.getDate();

  // Funzione per gestire il doppio click su una cella
  const handleDoubleClick = useCallback((day: number) => {
    if (isAdmin && onCreateEvent) {
      const date = new Date(currentYear, currentMonth, day);
      onCreateEvent(date);
    }
  }, [isAdmin, onCreateEvent, currentYear, currentMonth]);

  // Get store color based on tournament's store data (database-ready)
  const getStoreColorForTournament = useCallback((tournament: Tournament) => {
    return Colors.getStoreScheme(tournament.store, tournament);
  }, []);

  return (
    <div className="grid grid-cols-7 gap-1 md:max-w-4xl md:mx-auto">
      {/* Intestazioni giorni */}
      {useMemo(() => ["Dom", "Lun", "Mar", "Mer", "Gio", "Ven", "Sab"], []).map((day) => (
        <div
          key={day}
          className="h-8 flex items-center justify-center text-xs font-medium text-blue-300"
        >
          {day}
        </div>
      ))}

      {/* Giorni vuoti */}
      {emptyDays.map((_, index) => (
        <div
          key={`empty-${index}`}
          className="aspect-square md:aspect-auto md:h-24 bg-black/20 border border-blue-900/20 rounded-lg"
        />
      ))}

      {/* Giorni del mese */}
      {days.map((day) => {
        const isSelected =
          selectedDate?.getDate() === day &&
          selectedDate?.getMonth() === currentMonth;
        const isToday = isCurrentMonth && day === currentDay;
        const tournamentsOnDay = tournamentDays[day] || [];
        // const hasMultipleTournaments = tournamentsOnDay.length > 1;

        // Determina lo sfondo in base allo stato della casella
        let bgClass = "bg-black/20 hover:bg-black/30";
        let borderClass = "border border-blue-900/40";
        
        if (tournamentsOnDay.length > 0) {
          bgClass = "bg-blue-800/30 hover:bg-blue-800/40";
          borderClass = "border border-blue-600/60";
        }
        
        if (isSelected) {
          bgClass = "bg-blue-600/40 hover:bg-blue-600/50";
          borderClass = "border border-blue-400/80";
        }

        return (
          <button
            key={day}
            onClick={() => onDayClick(day)}
            onDoubleClick={() => handleDoubleClick(day)}
            className={`
              aspect-square md:aspect-auto md:h-24 relative rounded-lg transition-all
              ${bgClass} ${borderClass}
              ${isToday ? "ring-2 ring-blue-500" : ""}
              ${isSelected ? "shadow-[0_0_15px_rgba(129,140,248,0.5)]" : ""}
              ${isAdmin ? "cursor-pointer" : ""}
            `}
          >
            {/* Numero del giorno */}
            <span className={`
              absolute top-1 left-1 text-xs font-medium
              ${tournamentsOnDay.length > 0 ? "text-blue-300" : ""}
              ${isToday ? "text-blue-400" : ""}
              ${isSelected ? "text-white" : ""}
            `}>
              {day}
            </span>

            {/* Mobile: Solo colore dello store */}
            <div className="md:hidden absolute inset-0 flex items-center justify-center">
              {tournamentsOnDay.length > 0 && (
                <div className="flex flex-col items-center gap-1">
                  {tournamentsOnDay.map((tournament) => {
                    const storeColor = getStoreColorForTournament(tournament);
                    return (
                      <div 
                        key={tournament.id}
                        className={`${storeColor.bg} w-4 h-4 rounded-full`}
                      />
                    );
                  })}
                </div>
              )}
            </div>

            {/* Desktop: Colore dello store + "Tappa di lega" */}
            <div className="hidden md:block absolute inset-x-1 top-6 space-y-1">
              {tournamentsOnDay.map((tournament) => {
                const storeColor = getStoreColorForTournament(tournament);
                return (
                  <div
                    key={tournament.id}
                    className={`
                      ${storeColor.bg} rounded px-1 py-0.5
                      text-[10px] leading-tight text-white
                      overflow-hidden text-ellipsis whitespace-nowrap
                    `}
                  >
                    <span>{tournament.title}</span>
                  </div>
                );
              })}
            </div>
          </button>
        );
      })}
    </div>
  );
} 