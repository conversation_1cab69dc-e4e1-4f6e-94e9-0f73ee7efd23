"use client";

import React, { useState, useEffect } from "react";
import { X, Check, AlertCircle, CalendarIcon, Clock, MapPin, Coins, Gamepad2, Lock, LogIn, User<PERSON>he<PERSON>, User } from "lucide-react";
import Link from "next/link";
import { Tournament } from "@/types/calendar";
import { Colors } from "@/lib/constants/colors";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import { useArchetypes } from "@/lib/hooks/useArchetypes";
import { useLockBodyScroll } from "@/lib/hooks/useLockBodyScroll";
import { useCreateRegistration, useRegistrationStatus } from "@/lib/hooks/useRegistration";
import { Select } from "@/components/ui/Select";
import { useAuthContext } from "@/lib/providers/AuthProvider";
import { getDisplayName } from "@/lib/utils";
import { validateManaboxUrl, formatManaboxUrl } from "@/lib/utils/manaboxValidation";
import { RegistrationFormData, RegistrationFormErrors } from "@/types/registration";

interface RegistrationModalProps {
  tournament: Tournament | null;
  isOpen: boolean;
  onCloseAction: () => void;
}

export function RegistrationModal({ tournament, isOpen, onCloseAction }: RegistrationModalProps) {
  const { data: archetypes, isLoading: isLoadingArchetypes } = useArchetypes();
  const { user, player } = useAuthContext();
  const { mutate: createRegistration, isPending: isCreating, isSuccess, error: registrationError } = useCreateRegistration();
  const { data: isRegistered } = useRegistrationStatus(tournament?.id || '');
  
  // Lock body scroll when modal is open
  useLockBodyScroll(isOpen);
  
  // Check if user is authenticated and has completed onboarding
  const isAuthenticated = !!user;
  const hasCompletedProfile = !!(player?.first_name && player?.last_name);
  
  
  const [formData, setFormData] = useState<RegistrationFormData>({
    fullName: "",
    contact: "",
    deckName: "",
    archetype: "",
    decklist: ""
  });
  const [errors, setErrors] = useState<RegistrationFormErrors>({
    fullName: false,
    contact: false,
    deckName: false,
    archetype: false,
    decklist: false
  });
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({});
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Reset form quando si apre il modale
  useEffect(() => {
    if (isOpen) {
      // Pre-fill form data for authenticated users
      const preFilledData = {
        fullName: isAuthenticated && hasCompletedProfile 
          ? getDisplayName(player, user) 
          : "",
        contact: isAuthenticated && user?.email ? user.email : "",
        deckName: "",
        archetype: "",
        decklist: ""
      };
      
      setFormData(preFilledData);
      setErrors({
        fullName: false,
        contact: false,
        deckName: false,
        archetype: false,
        decklist: false
      });
      setIsSubmitted(false);
      setValidationErrors({});
    }
  }, [isOpen, isAuthenticated, hasCompletedProfile, player, user]);

  useEffect(() => {
    if (isSuccess) {
      setIsSubmitted(true);
    }
  }, [isSuccess]);

  if (!isOpen || !tournament) return null;

  const storeColor = Colors.getStoreScheme(tournament.store);

  // If user is not authenticated, show authentication prompt
  if (!isAuthenticated) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 rounded-xl border border-blue-500/30 w-full max-w-md overflow-hidden">
          {/* Header del Modale */}
          <div className="p-4 border-b border-blue-500/20 flex justify-between items-center">
            <h3 className="text-lg sm:text-xl font-bold">
              Accesso Richiesto
            </h3>
            <button
              onClick={onCloseAction}
              className="p-1 hover:bg-blue-900/50 rounded-full transition-colors"
            >
              <X size={18} className="sm:w-5 sm:h-5" />
            </button>
          </div>

          {/* Contenuto del Modale */}
          <div className="p-6">
            <div className="flex flex-col items-center justify-center text-center space-y-6">
              {/* Icon */}
              <div className={`w-20 h-20 rounded-full flex items-center justify-center ${storeColor.bg}/20 border ${storeColor.border}`}>
                <UserCheck size={40} className={storeColor.light} />
              </div>
              
              {/* Message */}
              <div className="space-y-3">
                <h4 className="text-xl font-bold text-white">
                  Accedi per iscriverti
                </h4>
                <p className="text-blue-200 text-sm leading-relaxed">
                  Devi prima effettuare login o registrazione per poterti iscrivere al torneo <strong>{tournament.title}</strong>
                </p>
              </div>
              
              {/* CTA Button */}
              <Link 
                href={{
                  pathname: '/login',
                  query: { next: '/calendar' }
                }}
                className={`flex items-center gap-2 px-6 py-3 ${storeColor.bg} ${storeColor.bgHover} rounded-lg font-medium transition-colors text-white text-sm sm:text-base`}
              >
                <LogIn size={18} />
                Login / Registrati
              </Link>
              
              {/* Additional info */}
              <p className="text-blue-300/70 text-xs">
                Dopo l&apos;accesso potrai iscriverti a tutti i tornei e tenere traccia delle tue partecipazioni
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Reset error quando l'utente inizia a digitare
    if (errors[name as keyof typeof errors]) {
      setErrors(prev => ({
        ...prev,
        [name]: false
      }));
    }
  };
  
  const handleArchetypeChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      archetype: value
    }));
    // Reset error quando l'utente seleziona un'opzione
    if (errors.archetype) {
      setErrors(prev => ({
        ...prev,
        archetype: false
      }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validazione form di base
    const newErrors = {
      fullName: formData.fullName.trim() === "",
      contact: formData.contact.trim() === "",
      deckName: false, // deckName is optional
      archetype: false, // archetype is now optional during registration
      decklist: false // decklist is optional
    };
    
    setErrors(newErrors);
    setValidationErrors({});
    
    // Se ci sono errori form, non procedere
    if (Object.values(newErrors).some(error => error)) {
      return;
    }
    
    // Validazione avanzata
    const newValidationErrors: {[key: string]: string} = {};
    
    // Validazione URL Manabox se specificato
    if (formData.decklist.trim()) {
      const manaboxValidation = validateManaboxUrl(formData.decklist);
      if (!manaboxValidation.isValid) {
        newValidationErrors.decklist = manaboxValidation.error || 'URL Manabox non valido';
      }
    }
    
    // Se ci sono errori di validazione avanzata, non procedere
    if (Object.keys(newValidationErrors).length > 0) {
      setValidationErrors(newValidationErrors);
      return;
    }
    
    // Preparazione dati per API
    const formattedDecklistUrl = formData.decklist ? formatManaboxUrl(formData.decklist) : null;
    
    // Invio dati al server
    if (tournament) {
      createRegistration({
        tournamentId: tournament.id,
        deckName: formData.deckName.trim() || undefined,
        archetypeId: formData.archetype.trim() || undefined,
        deckListUrl: formattedDecklistUrl || undefined
      });
    }
  };
  
  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 rounded-xl border border-blue-500/30 w-full max-w-2xl max-h-[85vh] sm:max-h-[98vh] overflow-hidden">
        {/* Header del Modale */}
        <div className="p-3 sm:p-4 border-b border-blue-500/20 flex justify-between items-center">
          <h3 className="text-lg sm:text-xl font-bold">
            {isSubmitted ? "Iscrizione Completata" : `Iscrizione - ${tournament.title}`}
          </h3>
          <button
            onClick={onCloseAction}
            className="p-1 hover:bg-blue-900/50 rounded-full transition-colors"
          >
            <X size={18} className="sm:w-5 sm:h-5" />
          </button>
        </div>

        {/* Contenuto del Modale */}
        <div className="p-3 sm:p-4 overflow-y-auto max-h-[calc(85vh-8rem)] sm:max-h-[calc(95vh-8rem)]">
          {isSubmitted ? (
            <div className="flex flex-col items-center justify-center py-8">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mb-4">
                <Check size={32} className="text-green-400" />
              </div>
              <h4 className="text-xl font-bold mb-2">Iscrizione Confermata!</h4>
              <p className="text-center text-blue-200 mb-4">
                La tua iscrizione a {tournament.title} è stata registrata con successo.
              </p>
              
              {/* Link al profilo */}
              <Link 
                href="/profile"
                className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600/80 hover:bg-blue-600 border border-blue-500/50 rounded-lg text-sm font-medium transition-colors text-white"
              >
                <User size={16} />
                Vedi le tue iscrizioni
              </Link>
              
              <p className="text-center text-blue-300/70 text-xs mt-3">
                Puoi gestire tutte le tue iscrizioni dalla pagina del profilo
              </p>
            </div>
          ) : registrationError ? (
            <div className="flex flex-col items-center justify-center py-8">
              <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mb-4">
                <AlertCircle size={32} className="text-red-400" />
              </div>
              <h4 className="text-xl font-bold mb-2">Errore</h4>
              <p className="text-center text-blue-200">
                {registrationError instanceof Error 
                  ? registrationError.message 
                  : "Si è verificato un errore durante l'iscrizione. Riprova più tardi."}
              </p>
              <button 
                onClick={() => {
                  // Reset dell'errore e permetti di riprovare
                  setIsSubmitted(false);
                }}
                className="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm transition-colors"
              >
                Riprova
              </button>
            </div>
          ) : (
            <>
              {/* Riepilogo Torneo */}
              <div className={`mb-4 p-3 rounded-lg bg-black/30 border ${storeColor.border}`}>
                <div className="flex flex-col space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 flex items-center justify-center">
                      <CalendarIcon size={16} className={storeColor.light} />
                    </div>
                    <span className="text-sm">
                      {format(new Date(tournament.date), "EEEE d MMMM", { locale: it })}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 flex items-center justify-center">
                      <Gamepad2 size={16} className={storeColor.light} />
                    </div>
                    <span className="text-sm capitalize">Formato: {tournament.format}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 flex items-center justify-center">
                      <Clock size={16} className={storeColor.light} />
                    </div>
                    <span className="text-sm">
                      {format(new Date(`2000-01-01T${tournament.time_start}`), "HH:mm")}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 flex items-center justify-center">
                      <MapPin size={16} className={storeColor.light} />
                    </div>
                    <span className="text-sm">{tournament.store?.name || 'Sede da definire'}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 flex items-center justify-center">
                      <Coins size={16} className={storeColor.light} />
                    </div>
                    <span className="text-sm">Prezzo: {tournament.price} €</span>
                  </div>
                </div>
              </div>
              
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label htmlFor="fullName" className="flex items-center gap-2 text-sm font-medium mb-1">
                    Nome e Cognome*
                    {isAuthenticated && hasCompletedProfile && (
                      <div className="flex items-center gap-1 text-xs text-emerald-400">
                        <Lock size={12} />
                        <span>Auto-compilato</span>
                      </div>
                    )}
                  </label>
                  <input
                    type="text"
                    id="fullName"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleChange}
                    disabled={isAuthenticated && hasCompletedProfile}
                    className={`w-full px-3 py-2 border rounded-lg text-sm transition-colors ${
                      isAuthenticated && hasCompletedProfile
                        ? "bg-emerald-500/10 border-emerald-500/30 text-emerald-100 cursor-not-allowed"
                        : `bg-black/30 ${errors.fullName ? "border-red-500" : "border-blue-500/30"} focus:outline-none focus:ring-2 focus:ring-blue-500/50`
                    }`}
                    placeholder={isAuthenticated && hasCompletedProfile ? "Campo compilato automaticamente" : "Inserisci il tuo nome e cognome"}
                  />
                  {errors.fullName && (
                    <p className="mt-1 text-xs text-red-400">Campo obbligatorio</p>
                  )}
                </div>
                
                <div>
                  <label htmlFor="contact" className="flex items-center gap-2 text-sm font-medium mb-1">
                    Indirizzo Email*
                    {isAuthenticated && user?.email && (
                      <div className="flex items-center gap-1 text-xs text-emerald-400">
                        <Lock size={12} />
                        <span>Auto-compilato</span>
                      </div>
                    )}
                  </label>
                  <input
                    type="email"
                    id="contact"
                    name="contact"
                    value={formData.contact}
                    onChange={handleChange}
                    disabled={isAuthenticated && !!user?.email}
                    className={`w-full px-3 py-2 border rounded-lg text-sm transition-colors ${
                      isAuthenticated && user?.email
                        ? "bg-emerald-500/10 border-emerald-500/30 text-emerald-100 cursor-not-allowed"
                        : `bg-black/30 ${errors.contact ? "border-red-500" : "border-blue-500/30"} focus:outline-none focus:ring-2 focus:ring-blue-500/50`
                    }`}
                    placeholder={isAuthenticated && user?.email ? "Campo compilato automaticamente" : "Inserisci il tuo indirizzo email"}
                  />
                  {errors.contact && (
                    <p className="mt-1 text-xs text-red-400">Campo obbligatorio</p>
                  )}
                </div>
                
                <div>
                  <label htmlFor="deckName" className="flex items-center gap-2 text-sm font-medium mb-1">
                    Nome Deck
                    <span className="text-xs text-blue-300/70 bg-blue-500/10 px-2 py-0.5 rounded">
                      opzionale
                    </span>
                  </label>
                  <input
                    type="text"
                    id="deckName"
                    name="deckName"
                    value={formData.deckName}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 bg-black/30 border ${
                      errors.deckName ? "border-red-500" : "border-blue-500/30"
                    } rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500/50`}
                    placeholder="Es. Mono Red Burn, Boros Bully, ecc."
                  />
                  {errors.deckName && (
                    <p className="mt-1 text-xs text-red-400">Campo obbligatorio</p>
                  )}
                </div>
                
                <div>
                  <label htmlFor="archetype" className="flex items-center gap-2 text-sm font-medium mb-1">
                    Archetipo
                    <span className="text-xs text-blue-300/70 bg-blue-500/10 px-2 py-0.5 rounded">
                      opzionale
                    </span>
                  </label>
                  <Select
                    value={formData.archetype}
                    onChange={handleArchetypeChange}
                    options={archetypes?.map(archetype => ({
                      value: archetype.id,
                      label: archetype.name,
                      description: archetype.description || undefined
                    })) || []}
                    placeholder={isLoadingArchetypes ? "Caricamento..." : "Seleziona l'archetipo del tuo deck (opzionale)"}
                    hasError={errors.archetype}
                    disabled={isLoadingArchetypes}
                    allowEmpty={true}
                    emptyLabel="Nessun archetipo selezionato"
                  />
                </div>
                
                <div>
                  <label htmlFor="decklist" className="flex items-center gap-2 text-sm font-medium mb-1">
                    Link decklist Manabox
                    <span className="text-xs text-blue-300/70 bg-blue-500/10 px-2 py-0.5 rounded">
                      opzionale
                    </span>
                  </label>
                  <input
                    type="text"
                    id="decklist"
                    name="decklist"
                    value={formData.decklist}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 bg-black/30 border ${
                      errors.decklist ? "border-red-500" : "border-blue-500/30"
                    } rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500/50`}
                    placeholder="Es. https://manabox.app/decks/el916JggRo2_E6znaKy5lw"
                  />
                  {errors.decklist && (
                    <p className="mt-1 text-xs text-red-400">Campo obbligatorio</p>
                  )}
                  {validationErrors.decklist && (
                    <p className="mt-1 text-xs text-red-400">{validationErrors.decklist}</p>
                  )}
                  <p className="mt-1 text-xs text-blue-300/70">
                    Decklist ed archetipo sono obbligatori per partecipare, ma possono essere aggiunti o modificati in qualsiasi momento fino all&apos;inizio dell&apos;evento attraverso il profilo.
                  </p>
                </div>
                
                <div className="pt-2">
                  <button
                    type="submit"
                    disabled={isCreating || isRegistered}
                    className={`w-full py-2 ${
                      isCreating 
                        ? "bg-blue-700/50 cursor-not-allowed" 
                        : isRegistered
                        ? "bg-green-700/50 cursor-not-allowed"
                        : `${storeColor.bg} ${storeColor.bgHover}`
                    } rounded-lg font-medium transition-colors text-sm sm:text-base flex items-center justify-center`}
                  >
                    {isCreating ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Invio in corso...
                      </>
                    ) : isRegistered ? (
                      "Già iscritto"
                    ) : (
                      "Conferma Iscrizione"
                    )}
                  </button>
                </div>
              </form>
            </>
          )}
        </div>

        {/* Footer del Modale */}
        <div className="p-3 sm:p-4 border-t border-blue-500/20 bg-black/20">
          <div className="flex justify-between items-center">
            <span className="text-blue-200 text-xs sm:text-sm">
              {isSubmitted 
                ? "Riceverai una conferma via email" 
                : registrationError
                ? "Ci sono problemi con l'iscrizione"
                : "Tutti i campi contrassegnati con * sono obbligatori"}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
} 