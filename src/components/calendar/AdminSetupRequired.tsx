import { AlertTriangle } from 'lucide-react';

interface AdminSetupRequiredProps {
  error?: string;
}

export function AdminSetupRequired({ error }: AdminSetupRequiredProps) {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70">
      <div className="w-full max-w-md bg-black/20 backdrop-blur-sm border border-blue-500/30 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-4">
          <AlertTriangle size={24} className="text-amber-400" />
          <h2 className="text-xl font-bold">Configurazione Admin Richiesta</h2>
        </div>
        
        <p className="mb-4">
          La configurazione del ruolo di amministratore non è stata completata. 
          Per utilizzare le funzionalità di amministrazione, è necessario:
        </p>
        
        <ol className="list-decimal pl-5 mb-4 space-y-2">
          <li>Eseguire le migrazioni SQL per creare la tabella <code className="bg-blue-900/50 px-1 rounded">user_roles</code></li>
          <li>Assegnare il ruolo di amministratore a un utente</li>
        </ol>
        
        {error && (
          <div className="bg-red-900/30 border border-red-700 rounded-lg p-3 mb-4">
            <p className="text-sm font-medium text-red-300">Dettagli errore:</p>
            <p className="text-sm text-red-200 font-mono">{error}</p>
          </div>
        )}
        
        <p className="text-sm text-blue-300">
          Consulta il file <code className="bg-blue-900/50 px-1 rounded">docs/admin-setup.md</code> per istruzioni dettagliate.
        </p>
      </div>
    </div>
  );
} 