import React from 'react';

interface ConfirmationDialogProps {
  mode: 'create' | 'edit';
  onCancel: () => void;
  onConfirm: () => void;
}

export function ConfirmationDialog({ mode, onCancel, onConfirm }: ConfirmationDialogProps) {
  const confirmationMessage = mode === 'create' 
    ? 'Sei sicuro di voler creare questo evento?' 
    : 'Sei sicuro di voler modificare questo evento?';

  return (
    <div className="space-y-4">
      <p className="text-center text-lg">{confirmationMessage}</p>
      <div className="flex justify-center gap-4 mt-6">
        <button
          onClick={onCancel}
          className="px-4 py-2 bg-black/30 hover:bg-blue-900/50 border border-blue-500/30 rounded-lg transition-colors"
        >
          Annulla
        </button>
        <button
          onClick={onConfirm}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors text-white font-medium"
        >
          Conferma
        </button>
      </div>
    </div>
  );
}
