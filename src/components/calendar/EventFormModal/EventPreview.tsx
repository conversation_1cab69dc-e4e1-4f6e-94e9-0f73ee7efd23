import React from 'react';
import { Tournament } from '@/types/calendar';
import { TournamentDetails } from '../TournamentDetails';

interface EventPreviewProps {
  previewData: Tournament;
  formData: Partial<Tournament>;
  onBack: () => void;
}

export function EventPreview({ previewData, formData, onBack }: EventPreviewProps) {
  return (
    <div className="space-y-4">
      <TournamentDetails
        selectedTournament={previewData}
        selectedDate={formData.date ? new Date(formData.date) : null}
        onRegistrationClick={() => {}}
      />
      <div className="flex justify-between mt-4">
        <button
          onClick={onBack}
          className="px-4 py-2 bg-black/30 hover:bg-blue-900/50 border border-blue-500/30 rounded-lg transition-colors"
        >
          Torna al form
        </button>
      </div>
    </div>
  );
}
