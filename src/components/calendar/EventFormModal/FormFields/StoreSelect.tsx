import React from 'react';
import { MapPin } from 'lucide-react';
import { Store } from '../useEventForm';

interface StoreSelectProps {
  storeId: string;
  stores: Store[];
  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
}

export function StoreSelect({ storeId, stores, onChange }: StoreSelectProps) {
  return (
    <div className="space-y-2">
      <label className="flex items-center gap-2 text-sm font-medium">
        <MapPin size={16} className="text-blue-400" />
        Negozio
      </label>
      <select
        name="store_id"
        value={storeId || ''}
        onChange={onChange}
        className="w-full px-3 py-2 bg-black/30 border border-blue-500/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 text-white"
      >
        <option value="">Seleziona un negozio</option>
        {stores.map(store => (
          <option key={store.id} value={store.id}>
            {store.name} - {store.address}
          </option>
        ))}
      </select>
    </div>
  );
}
