import React from 'react';
import { Users, Coins } from 'lucide-react';

interface ParticipantFieldsProps {
  maxPlayers: number | undefined;
  price: number | undefined;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export function ParticipantFields({ maxPlayers, price, onChange }: ParticipantFieldsProps) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
      <div className="space-y-2">
        <label className="flex items-center gap-2 text-sm font-medium">
          <Users size={16} className="text-blue-400" />
          Partecipanti massimi
        </label>
        <input
          type="number"
          name="max_players"
          value={maxPlayers ?? ''}
          onChange={onChange}
          min="1"
          inputMode="numeric"
          pattern="[0-9]*"
          placeholder="30"
          className="w-full px-3 py-2 bg-black/30 border border-blue-500/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 text-white placeholder:text-gray-400"
        />
      </div>

      <div className="space-y-2">
        <label className="flex items-center gap-2 text-sm font-medium">
          <Coins size={16} className="text-blue-400" />
          Costo (€)
        </label>
        <input
          type="number"
          name="price"
          value={price ?? ''}
          onChange={onChange}
          min="0"
          step="0.01"
          inputMode="decimal"
          pattern="[0-9]*\.?[0-9]*"
          placeholder="5"
          className="w-full px-3 py-2 bg-black/30 border border-blue-500/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 text-white placeholder:text-gray-400"
        />
      </div>
    </div>
  );
}
