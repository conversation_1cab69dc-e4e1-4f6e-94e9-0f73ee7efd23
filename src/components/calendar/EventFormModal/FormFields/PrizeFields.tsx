import React from 'react';
import { Trophy, FileText } from 'lucide-react';

interface PrizeFieldsProps {
  prizePool: string;
  description: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}

export function PrizeFields({ prizePool, description, onChange }: PrizeFieldsProps) {
  return (
    <>
      <div className="space-y-2">
        <label className="flex items-center gap-2 text-sm font-medium">
          <Trophy size={16} className="text-blue-400" />
          Premio
        </label>
        <input
          type="text"
          name="prize_pool"
          value={prizePool || ''}
          onChange={onChange}
          className="w-full px-3 py-2 bg-black/30 border border-blue-500/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 text-white"
          placeholder="Descrizione del premio"
        />
      </div>

      <div className="space-y-2">
        <label className="flex items-center gap-2 text-sm font-medium">
          <FileText size={16} className="text-blue-400" />
          Descrizione
        </label>
        <textarea
          name="description"
          value={description || ''}
          onChange={onChange}
          rows={2}
          className="w-full px-3 py-2 bg-black/30 border border-blue-500/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 resize-none text-white"
          placeholder="Descrizione del torneo"
        />
      </div>
    </>
  );
}
