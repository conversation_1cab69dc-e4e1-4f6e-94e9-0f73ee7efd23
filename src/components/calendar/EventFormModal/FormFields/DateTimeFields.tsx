import React from 'react';
import { Calendar, Clock } from 'lucide-react';

interface DateTimeFieldsProps {
  date: string;
  timeStart: string;
  timeEnd: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const inputClasses = `
  w-full px-3 py-2 bg-black/30 border border-blue-500/30 rounded-lg 
  focus:outline-none focus:ring-2 focus:ring-blue-500/50 text-white 
  [&::-webkit-calendar-picker-indicator]:filter 
  [&::-webkit-calendar-picker-indicator]:brightness-0 
  [&::-webkit-calendar-picker-indicator]:invert 
  [&::-webkit-calendar-picker-indicator]:opacity-70 
  [&::-webkit-calendar-picker-indicator]:sepia 
  [&::-webkit-calendar-picker-indicator]:saturate-200 
  [&::-webkit-calendar-picker-indicator]:hue-rotate-180
`;

export function DateTimeFields({ date, timeStart, timeEnd, onChange }: DateTimeFieldsProps) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
      <div className="space-y-2">
        <label className="flex items-center gap-2 text-sm font-medium">
          <Calendar size={16} className="text-blue-400" />
          Data
        </label>
        <input
          type="date"
          name="date"
          value={date || ''}
          onChange={onChange}
          className={inputClasses}
        />
      </div>

      <div className="space-y-2">
        <label className="flex items-center gap-2 text-sm font-medium">
          <Clock size={16} className="text-blue-400" />
          Orario
        </label>
        <div className="flex gap-2">
          <input
            type="time"
            name="time_start"
            value={timeStart?.slice(0, 5) || ''}
            onChange={onChange}
            className={inputClasses}
          />
          <span className="self-center">-</span>
          <input
            type="time"
            name="time_end"
            value={timeEnd?.slice(0, 5) || ''}
            onChange={onChange}
            className={inputClasses}
          />
        </div>
      </div>
    </div>
  );
}
