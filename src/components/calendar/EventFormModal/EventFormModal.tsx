import { useState } from 'react';
import { X } from 'lucide-react';
import { format } from 'date-fns';
import { Tournament } from '@/types/calendar';
import { useEventForm } from './useEventForm';
import { EventForm } from './EventForm';
import { EventPreview } from './EventPreview';
import { ConfirmationDialog } from './ConfirmationDialog';
import { useLockBodyScroll } from '@/lib/hooks/useLockBodyScroll';
import { logger } from '@/lib/utils/logger';

interface EventFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialData?: Tournament | null;
  mode: 'create' | 'edit';
  preselectedDate?: Date | null;
}

export function EventFormModal({ 
  isOpen, 
  onClose, 
  initialData, 
  mode, 
  preselectedDate 
}: EventFormModalProps) {
  const [showPreview, setShowPreview] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [previewData, setPreviewData] = useState<Tournament | null>(null);
  
  const { 
    formData, 
    stores, 
    handleChange, 
    handleNumberChange, 
    handleSubmit 
  } = useEventForm(initialData || null, preselectedDate || null, mode, isOpen);

  // Blocca lo scroll del body quando il modal è aperto
  useLockBodyScroll(isOpen);

  if (!isOpen) return null;

  const getPreviewData = async (): Promise<Tournament> => {
    const selectedStore = stores.find(store => store.id === formData.store_id);
    let seasonId = initialData?.season_id;
    
    if (!seasonId) {
      try {
        const { supabase } = await import('@/lib/supabase/client');
        const { data: activeSeason } = await supabase
          .from('seasons')
          .select('id')
          .eq('is_active', true)
          .single();
        
        seasonId = (activeSeason as { id: string } | null)?.id || 'unknown-season';
      } catch (error) {
        logger.error('Error fetching active season for preview', error, { component: 'EventFormModal' });
        seasonId = 'unknown-season';
      }
    }
    
    return {
      id: initialData?.id || 'preview',
      title: formData.title || 'Nuovo Torneo',
      date: formData.date || format(new Date(), 'yyyy-MM-dd'),
      time_start: formData.time_start || '18:00:00',
      time_end: formData.time_end || '22:00:00',
      format: formData.format || 'Pauper',
      store_id: formData.store_id || '',
      season_id: seasonId,
      max_players: formData.max_players || 32,
      price: formData.price || 5,
      prize_pool: formData.prize_pool || 'Premi in buoni negozio in base ai partecipanti',
      description: formData.description || 'Torneo Pauper settimanale',
      created_at: initialData?.created_at || new Date().toISOString(),
      updated_at: new Date().toISOString(),
      store: {
        id: selectedStore?.id || '',
        name: selectedStore?.name || 'Negozio',
        address: selectedStore?.address || 'Indirizzo',
        city: selectedStore?.city || '',
        color: initialData?.store?.color || 'blue-500',
        created_at: initialData?.store?.created_at || new Date().toISOString(),
      },
      tournament_registrations: initialData?.tournament_registrations || [{ count: 0 }],
    };
  };

  const handlePreview = async () => {
    const preview = await getPreviewData();
    setPreviewData(preview);
    setShowPreview(true);
  };

  const handleConfirmSubmit = async () => {
    await handleSubmit();
    onClose();
  };

  const renderContent = () => {
    if (showPreview && previewData) {
      return (
        <EventPreview
          previewData={previewData}
          formData={formData}
          onBack={() => setShowPreview(false)}
        />
      );
    }
    
    if (showConfirmation) {
      return (
        <ConfirmationDialog
          mode={mode}
          onCancel={() => setShowConfirmation(false)}
          onConfirm={handleConfirmSubmit}
        />
      );
    }
    
    return (
      <EventForm
        formData={formData}
        stores={stores}
        mode={mode}
        onSubmit={(e) => e.preventDefault()}
        onChange={handleChange}
        onNumberChange={handleNumberChange}
        onPreview={handlePreview}
        onConfirm={() => setShowConfirmation(true)}
      />
    );
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70">
      <div className="relative w-full max-w-2xl max-h-[80vh] sm:max-h-[90vh] overflow-y-auto bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 backdrop-blur-sm border border-blue-500/30 rounded-xl p-4 sm:p-6">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-blue-300 hover:text-white transition-colors"
        >
          <X size={20} />
        </button>

        <h2 className="text-xl sm:text-2xl font-bold mb-4 text-white">
          {mode === 'create' ? 'Aggiungi Nuovo Evento' : 'Modifica Evento'}
        </h2>

        {renderContent()}
      </div>
    </div>
  );
}
