import { memo } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { MONTHS } from '@/lib/utils/calendar';

interface CalendarHeaderProps {
  currentMonth: number;
  currentYear: number;
  onPrevMonth: () => void;
  onNextMonth: () => void;
}

export const CalendarHeader = memo(function CalendarHeader({
  currentMonth,
  currentYear,
  onPrevMonth,
  onNextMonth
}: CalendarHeaderProps) {
  return (
    <div className="flex justify-between items-center p-3 sm:p-4 border-b border-blue-500/20">
      <button 
        onClick={onPrevMonth}
        className="p-1 sm:p-2 rounded-full hover:bg-blue-900/50 transition-colors"
      >
        <ChevronLeft size={18} />
      </button>
      
      <h2 className="text-lg sm:text-xl font-semibold">
        {MONTHS[currentMonth]} {currentYear}
      </h2>
      
      <button 
        onClick={onNextMonth}
        className="p-1 sm:p-2 rounded-full hover:bg-blue-900/50 transition-colors"
      >
        <ChevronRight size={18} />
      </button>
    </div>
  );
});
