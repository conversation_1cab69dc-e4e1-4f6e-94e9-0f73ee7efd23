"use client";

import React from 'react';
import { Calendar, LayoutGrid } from 'lucide-react';
import { ViewMode } from '@/types/calendar';
import { cn } from '@/lib/utils';

interface ViewModeSelectorProps {
  viewMode: ViewMode;
  onChange: (mode: ViewMode) => void;
  disabled?: boolean;
  className?: string;
}

export function ViewModeSelector({ 
  viewMode, 
  onChange, 
  disabled = false, 
  className 
}: ViewModeSelectorProps) {
  return (
    <div className={cn(
      "inline-flex rounded-lg border border-blue-500/30 bg-black/20 backdrop-blur-sm p-1",
      disabled && "opacity-50 cursor-not-allowed",
      className
    )}>
      <button
        onClick={() => !disabled && onChange('calendar')}
        disabled={disabled}
        className={cn(
          "flex items-center justify-center px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200",
          "hover:bg-blue-900/50 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-0",
          viewMode === 'calendar' 
            ? "bg-blue-600/50 text-white shadow-sm" 
            : "text-blue-300 hover:text-white",
          disabled && "cursor-not-allowed"
        )}
        aria-pressed={viewMode === 'calendar'}
        aria-label="Visualizzazione calendario"
      >
        <Calendar size={16} className="mr-1.5" />
        <span className="hidden sm:inline">Calendario</span>
        <span className="sr-only">Visualizzazione a calendario</span>
      </button>
      
      <button
        onClick={() => !disabled && onChange('cards')}
        disabled={disabled}
        className={cn(
          "flex items-center justify-center px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200",
          "hover:bg-blue-900/50 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-0",
          viewMode === 'cards' 
            ? "bg-blue-600/50 text-white shadow-sm" 
            : "text-blue-300 hover:text-white",
          disabled && "cursor-not-allowed"
        )}
        aria-pressed={viewMode === 'cards'}
        aria-label="Visualizzazione schede"
      >
        <LayoutGrid size={16} className="mr-1.5" />
        <span className="hidden sm:inline">Schede</span>
        <span className="sr-only">Visualizzazione a schede</span>
      </button>
    </div>
  );
}
