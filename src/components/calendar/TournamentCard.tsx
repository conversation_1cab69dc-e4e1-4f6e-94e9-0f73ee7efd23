"use client";

import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Calendar as CalendarIcon, Clock, Users, Euro } from 'lucide-react';
import { Tournament } from '@/types/calendar';
import { Colors } from '@/lib/constants/colors';
import { Card } from '@/components/ui/Card';
import { TournamentInfo } from './TournamentDetails/TournamentInfo';
import { TournamentActions } from './TournamentDetails/TournamentActions';
import { useTournamentDetails } from './TournamentDetails/useTournamentDetails';
import { TournamentRegistrationsModal } from './TournamentDetails';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';
import { cn } from '@/lib/utils';

interface TournamentCardProps {
  tournament: Tournament;
  onRegistrationClick: () => void;
  onEditClick?: (tournament: Tournament) => void;
  onTournamentClick: (tournament: Tournament) => void;
  isSelected?: boolean;
  disableSelection?: boolean;
}

export function TournamentCard({ 
  tournament, 
  onRegistrationClick, 
  onEditClick,
  onTournamentClick,
  isSelected = false,
  disableSelection = false
}: TournamentCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Create wrapper function to first select tournament then open registration
  const handleRegistrationAction = () => {
    // First, select the tournament
    onTournamentClick(tournament);
    // Then, open the registration modal
    onRegistrationClick();
  };
  
  const {
    isAdmin,
    isAdminLoading,
    showRegistrationsModal,
    isEventPast,
    handleDeleteTournament,
    handleEditClick,
    handleTournamentAction,
    openRegistrationsModal,
    closeRegistrationsModal,
  } = useTournamentDetails(tournament, handleRegistrationAction, onEditClick);
  
  const storeColor = Colors.getStoreScheme(tournament.store, tournament);
  const currentPlayers = tournament.tournament_registrations?.[0]?.count || 0;
  const isPastEvent = isEventPast(tournament.date);

  const handleCardClick = () => {
    if (!disableSelection) {
      onTournamentClick(tournament);
    }
  };

  const handleExpandToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  return (
    <Card 
      variant="default"
      padding="none"
      hover={!disableSelection}
      className={cn(
        "transition-all duration-200 w-full",
        !disableSelection && "cursor-pointer",
        !disableSelection && isSelected && "ring-2 ring-blue-400/60 shadow-lg shadow-blue-500/20",
        storeColor.border
      )}
      onClick={disableSelection ? undefined : handleCardClick}
    >
      {/* Main Card Content */}
      <div className="p-4">
        {/* Header with Title and Expand Button */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-start gap-3 flex-1 min-w-0">
            {/* Store Color Indicator */}
            <div className={`${storeColor.bg} w-4 h-4 rounded-full flex-shrink-0 mt-1`} />
            
            <div className="flex-1 min-w-0">
              <h3 className="font-bold text-white text-base leading-tight mb-1 truncate">
                {tournament.title}
              </h3>
              <div className={`${storeColor.bg} ${storeColor.text} text-xs px-2 py-1 rounded-full inline-block`}>
                {tournament.store?.name}
              </div>
            </div>
          </div>
          
          <button
            onClick={handleExpandToggle}
            className="p-1.5 hover:bg-blue-900/50 rounded-lg transition-colors flex-shrink-0"
            aria-label={isExpanded ? "Comprimi dettagli" : "Espandi dettagli"}
          >
            {isExpanded ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
          </button>
        </div>

        {/* Tournament Meta Info */}
        <div className="grid grid-cols-2 gap-3 mb-3">
          <div className="flex items-center gap-2 text-sm text-blue-300">
            <CalendarIcon size={14} className="flex-shrink-0" />
            <span className="truncate">
              {format(new Date(tournament.date), "d MMM yyyy", { locale: it })}
            </span>
          </div>
          
          <div className="flex items-center gap-2 text-sm text-blue-300">
            <Clock size={14} className="flex-shrink-0" />
            <span className="truncate">
              {format(new Date(`2000-01-01T${tournament.time_start}`), "HH:mm")}
            </span>
          </div>
        </div>

        {/* Participants and Price */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2 text-sm text-blue-200">
            <Users size={14} className="flex-shrink-0" />
            <span>
              {currentPlayers}/{tournament.max_players} partecipanti
            </span>
          </div>
          
          <div className="flex items-center gap-1 text-sm text-green-300">
            <Euro size={14} className="flex-shrink-0" />
            <span>{tournament.price}</span>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-2">
          <div className="w-full bg-blue-900/30 rounded-full h-2">
            <div 
              className={`${storeColor.progress} h-2 rounded-full transition-all duration-300`} 
              style={{ width: `${(currentPlayers / tournament.max_players) * 100}%` }}
            />
          </div>
          <div className="flex justify-between text-xs text-blue-400 mt-1">
            <span>Posti disponibili</span>
            <span>{tournament.max_players - currentPlayers} rimasti</span>
          </div>
        </div>
      </div>

      {/* Expanded Content */}
      {isExpanded && (
        <div className="border-t border-blue-500/20 p-4 space-y-4">
          <TournamentInfo 
            tournament={tournament}
            storeColor={storeColor}
            currentPlayers={currentPlayers}
          />
          
          <TournamentActions 
            tournament={tournament}
            storeColor={storeColor}
            isAdmin={isAdmin}
            isAdminLoading={isAdminLoading}
            isPastEvent={isPastEvent}
            currentPlayers={currentPlayers}
            onEditClick={handleEditClick}
            onDeleteClick={handleDeleteTournament}
            onTournamentAction={handleTournamentAction}
            onViewRegistrations={openRegistrationsModal}
          />
        </div>
      )}

      {showRegistrationsModal && (
        <TournamentRegistrationsModal
          tournament={tournament}
          isOpen={showRegistrationsModal}
          onClose={closeRegistrationsModal}
        />
      )}
    </Card>
  );
}
