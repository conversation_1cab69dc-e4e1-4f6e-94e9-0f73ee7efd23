"use client";

import React, { useMemo, useState, useEffect } from 'react';
import { useMyRegistrations, useUpdateRegistration, useDeleteRegistration } from '@/lib/hooks/useMyRegistrations';
import { useArchetypes } from '@/lib/hooks/useArchetypes';
import type { MyRegistrationWithTournament } from '@/types/registration';
import { Card } from '@/components/ui/Card';
import { CalendarIcon, Clock, MapPin, AlertCircle, CheckCircle2, Save, Loader2, ChevronDown, ChevronUp, Trash2 } from 'lucide-react';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';
import { validateManaboxUrl, formatManaboxUrl } from '@/lib/utils/manaboxValidation';

function isDecklistMissingOrInvalid(reg: MyRegistrationWithTournament, hasEventStarted: boolean = false): { invalid: boolean; reason?: string } {
  const url = reg.deck_list_url || '';
  if (!url.trim()) {
    return {
      invalid: true,
      reason: hasEventStarted ? 'Decklist mancante (evento iniziato)' : 'Decklist mancante'
    };
  }
  const validation = validateManaboxUrl(url);
  if (!validation.isValid) {
    return {
      invalid: true,
      reason: hasEventStarted ? 'URL non valido (evento iniziato)' : (validation.error || 'URL non valido')
    };
  }
  return { invalid: false };
}

function isArchetypeMissing(reg: MyRegistrationWithTournament, hasEventStarted: boolean = false): { invalid: boolean; reason?: string } {
  const archetypeId = reg.archetype_id;
  if (!archetypeId || archetypeId.trim() === '') {
    return {
      invalid: true,
      reason: hasEventStarted ? 'Archetipo mancante (evento iniziato)' : 'Archetipo mancante'
    };
  }
  return { invalid: false };
}

function DeleteRegistrationButton({ disabled, isDeleting, onConfirm }: {
  disabled: boolean;
  isDeleting: boolean;
  onConfirm: () => Promise<void>;
}) {
  const [confirming, setConfirming] = React.useState(false);

  if (!confirming) {
    return (
      <button
        onClick={() => setConfirming(true)}
        disabled={disabled || isDeleting}
        className={`w-full sm:w-auto inline-flex items-center justify-center gap-2 px-3 py-2 rounded-md text-white ${disabled || isDeleting ? 'bg-red-700/40 cursor-not-allowed' : 'bg-red-600 hover:bg-red-700'}`}
      >
        {isDeleting ? <Loader2 className="w-4 h-4 animate-spin" /> : <Trash2 className="w-4 h-4" />}
        {isDeleting ? 'Eliminazione...' : 'Elimina iscrizione'}
      </button>
    );
  }

  return (
    <div className="p-3 bg-red-500/10 border border-red-500/30 rounded text-red-200 max-w-full">
      <p className="text-sm mb-2">
        Sei sicuro di voler eliminare la tua iscrizione? Questa operazione è irreversibile e cancellerà definitivamente i tuoi dati di registrazione per questo evento.
      </p>
      <div className="grid grid-cols-1 gap-2">
        <button
          onClick={async () => { await onConfirm(); }}
          disabled={isDeleting}
          className={`w-full inline-flex items-center justify-center gap-2 px-3 py-1.5 rounded-md text-white ${isDeleting ? 'bg-red-700/40 cursor-not-allowed' : 'bg-red-600 hover:bg-red-700'}`}
        >
          {isDeleting ? <Loader2 className="w-4 h-4 animate-spin" /> : null}
          {isDeleting ? 'Elimino...' : 'Conferma eliminazione'}
        </button>
        <button
          onClick={() => setConfirming(false)}
          disabled={isDeleting}
          className="w-full inline-flex items-center justify-center gap-2 px-3 py-1.5 rounded-md text-white bg-gray-600 hover:bg-gray-700"
        >
          Annulla
        </button>
      </div>
    </div>
  );
}

function getEventTimes(t: NonNullable<MyRegistrationWithTournament['tournament']>) {
  const eventDate = new Date(t.date);
  const dateStr = eventDate.toISOString().split('T')[0];
  const start = new Date(`${dateStr}T${t.time_start}`);
  const end = new Date(`${dateStr}T${t.time_end}`);
  return { start, end };
}

export function UserEventsSection() {
  const { data, isLoading, error } = useMyRegistrations();
  const { data: archetypes } = useArchetypes();
  const [activeTab, setActiveTab] = useState<'ongoing' | 'past'>('ongoing');

  const { ongoing, past } = useMemo(() => {
    const today = new Date();
    const regs = (data || []).filter(r => !!r.tournament);
    const ongoing: MyRegistrationWithTournament[] = [];
    const past: MyRegistrationWithTournament[] = [];
    
    // Set today to start of day (00:00:00) for comparison
    const todayStartOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    
    for (const r of regs) {
      const t = r.tournament!;
      const tournamentDate = new Date(t.date);
      
      // Set tournament date to start of day (00:00:00) for comparison
      const tournamentStartOfDay = new Date(tournamentDate.getFullYear(), tournamentDate.getMonth(), tournamentDate.getDate());
      
      // Use same logic as registration service:
      // Only tournaments from yesterday and earlier are considered "past"
      // Today's tournaments and future tournaments are "ongoing"
      if (tournamentStartOfDay >= todayStartOfDay) {
        ongoing.push(r);
      } else {
        past.push(r);
      }
    }
    
    // Sort: ongoing by date ascending, past by date descending
    ongoing.sort((a,b) => new Date(a.tournament!.date).getTime() - new Date(b.tournament!.date).getTime());
    past.sort((a,b) => new Date(b.tournament!.date).getTime() - new Date(a.tournament!.date).getTime());
    return { ongoing, past };
  }, [data]);

  if (isLoading) {
    return (
      <div className="mt-8">
        <Card className="p-6">
          <div className="flex items-center gap-3 text-blue-200">
            <Loader2 className="w-5 h-5 animate-spin" />
            <span>Caricamento eventi...</span>
          </div>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mt-8">
        <Card className="p-6 border-red-500/30">
          <div className="flex items-center gap-3 text-red-300">
            <AlertCircle className="w-5 h-5" />
            <span>Errore nel caricamento degli eventi utente.</span>
          </div>
        </Card>
      </div>
    );
  }

  const list = activeTab === 'ongoing' ? ongoing : past;

  return (
    <section className="mt-8">
      <h2 className="text-xl font-semibold mb-4">Le mie iscrizioni</h2>

      {/* Tabs */}
      <div className="flex gap-2 mb-4">
        <button
          onClick={() => setActiveTab('ongoing')}
          className={`px-4 py-2 rounded-lg border transition-colors ${activeTab === 'ongoing' ? 'bg-blue-600 text-white border-blue-500' : 'bg-blue-900/30 text-blue-200 border-blue-700 hover:bg-blue-900/50'}`}
        >
          In corso
        </button>
        <button
          onClick={() => setActiveTab('past')}
          className={`px-4 py-2 rounded-lg border transition-colors ${activeTab === 'past' ? 'bg-blue-600 text-white border-blue-500' : 'bg-blue-900/30 text-blue-200 border-blue-700 hover:bg-blue-900/50'}`}
        >
          Passati
        </button>
      </div>

      {list.length === 0 ? (
        <Card className="p-6">
          <p className="text-blue-200">Nessun evento {activeTab === 'ongoing' ? 'in corso' : 'passato'}.</p>
        </Card>
      ) : (
        <div className="space-y-4">
          {list.map((reg) => (
            <EventItem
              key={reg.id}
              reg={reg}
              archetypes={archetypes || []}
            />
          ))}
        </div>
      )}
    </section>
  );
}

function EventItem({ reg, archetypes }: {
  reg: MyRegistrationWithTournament;
  archetypes: Array<{ id: string; name: string; description?: string | null }>;
}) {
  const t = reg.tournament!;
  const { start } = getEventTimes(t);
  const editingAllowed = new Date() < start;
  const [local, setLocal] = React.useState({
    deckName: reg.deck_name || '',
    archetypeId: reg.archetype_id || '',
    deckListUrl: reg.deck_list_url || '',
  });
  const [errors, setErrors] = React.useState<{ deckListUrl?: string } | null>(null);
  const [saveSuccess, setSaveSuccess] = React.useState(false);
  const [isExpanded, setIsExpanded] = React.useState(false);

  const { mutateAsync, isPending, error, reset } = useUpdateRegistration();
  const { mutateAsync: deleteAsync, isPending: isDeleting, error: deleteError, reset: resetDelete } = useDeleteRegistration();

  // Clear success after a short delay
  useEffect(() => {
    if (saveSuccess) {
      const timer = setTimeout(() => setSaveSuccess(false), 2500);
      return () => clearTimeout(timer);
    }
  }, [saveSuccess]);

  const hasEventStarted = new Date() >= start;
  const decklistStatus = isDecklistMissingOrInvalid(reg, hasEventStarted);
  const archetypeStatus = isArchetypeMissing(reg, hasEventStarted);

  const handleSave = async () => {
    // Validate decklist if provided
    if (local.deckListUrl?.trim()) {
      const v = validateManaboxUrl(local.deckListUrl);
      if (!v.isValid) {
        setErrors({ deckListUrl: v.error || 'URL non valido' });
        return;
      }
    }
    setErrors(null);

    const payload: { registrationId: string; deckName?: string | null; archetypeId?: string | null; deckListUrl?: string | null } = {
      registrationId: reg.id,
      deckName: local.deckName,
      archetypeId: local.archetypeId || null,
      deckListUrl: local.deckListUrl?.trim() ? (formatManaboxUrl(local.deckListUrl) || null) : null,
    };

    try {
      reset();
      await mutateAsync(payload);
      setSaveSuccess(true);
    } catch {
      // error handled via error object
    }
  };

  return (
    <Card className="p-4">
      <div className="flex flex-col gap-4">
        <div className="flex-1">
          <div className="flex items-start justify-between gap-2 mb-1">
            <div className="flex items-center gap-2 flex-wrap">
              <h3 className="font-semibold text-white">{t.title}</h3>
              {/* Red indicator if decklist invalid/missing */}
              {decklistStatus.invalid ? (
                <span title={decklistStatus.reason} className="inline-block w-2.5 h-2.5 rounded-full bg-red-500" />
              ) : (
                <CheckCircle2 className="w-4 h-4 text-emerald-400" />
              )}
              <span className={`${editingAllowed ? 'text-emerald-300' : 'text-red-300'} text-xs sm:text-sm`}>
                {editingAllowed ? 'Modifiche consentite' : 'Modifiche bloccate'}
              </span>
            </div>
            <button
              onClick={() => setIsExpanded(v => !v)}
              className="p-1.5 hover:bg-blue-900/50 rounded-md transition-colors text-blue-200 mt-0.5"
              aria-label={isExpanded ? 'Comprimi dettagli' : 'Espandi dettagli'}
            >
              {isExpanded ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
            </button>
          </div>
          {/* Missing info message if decklist or archetype invalid/missing */}
          {(decklistStatus.invalid || archetypeStatus.invalid) && (
            <div className="mt-2 inline-flex items-start gap-2 text-sm text-red-200 bg-red-500/10 border border-red-500/30 px-2 py-1 rounded max-w-full">
              <AlertCircle className="w-5 h-5 flex-shrink-0" />
              <div className="leading-snug whitespace-normal break-words">
                {decklistStatus.invalid && archetypeStatus.invalid ? (
                  <span>
                    {hasEventStarted
                      ? 'Decklist e archetipo mancanti. Non è più possibile modificare i dati dopo l\'inizio dell\'evento.'
                      : 'Decklist e archetipo mancanti. Completa entrambi per partecipare all\'evento.'
                    }
                  </span>
                ) : decklistStatus.invalid ? (
                  <span>
                    {decklistStatus.reason}.
                    {hasEventStarted
                      ? ' Non è più possibile modificare i dati dopo l\'inizio dell\'evento.'
                      : ' Aggiungi o correggi il link Manabox per completare.'
                    }
                  </span>
                ) : (
                  <span>
                    {archetypeStatus.reason}.
                    {hasEventStarted
                      ? ' Non è più possibile modificare i dati dopo l\'inizio dell\'evento.'
                      : ' Seleziona l\'archetipo del tuo deck per completare.'
                    }
                  </span>
                )}
              </div>
            </div>
          )}

          <div className="mt-2 grid grid-cols-1 sm:grid-cols-3 gap-2 text-blue-300 text-sm">
            <div className="flex items-center gap-1">
              <CalendarIcon className="w-4 h-4" />
              <span>{format(new Date(t.date), 'EEEE d MMMM', { locale: it })}</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="w-4 h-4" />
              <span>{format(new Date(`2000-01-01T${t.time_start}`), 'HH:mm')}</span>
            </div>
            <div className="flex items-center gap-1">
              <MapPin className="w-4 h-4" />
              <span>{t.store?.name || 'Sede da definire'}</span>
            </div>
          </div>

        </div>
      </div>

      {isExpanded && (
        <>
          {/* Editable fields */}
          <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-3">
            <div>
              <label className="block text-xs text-blue-300 mb-1">Nome Deck</label>
              <input
                value={local.deckName}
                onChange={(e) => setLocal(s => ({ ...s, deckName: e.target.value }))}
                disabled={!editingAllowed || isPending}
                className="w-full px-3 py-2 bg-blue-900/50 border border-blue-700 rounded text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Es. Mono Red Burn"
              />
            </div>
            <div>
              <label className="block text-xs text-blue-300 mb-1 flex items-center gap-2">
                Archetipo
              </label>
              <select
                value={local.archetypeId}
                onChange={(e) => setLocal(s => ({ ...s, archetypeId: e.target.value }))}
                disabled={!editingAllowed || isPending}
                className="w-full px-3 py-2 bg-blue-900/50 border border-blue-700 rounded text-white"
              >
                <option value="">Nessun archetipo selezionato</option>
                {archetypes.map(a => (
                  <option key={a.id} value={a.id}>{a.name}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-xs text-blue-300 mb-1">Link decklist Manabox</label>
              <input
                value={local.deckListUrl}
                onChange={(e) => setLocal(s => ({ ...s, deckListUrl: e.target.value }))}
                disabled={!editingAllowed || isPending}
                className={`w-full px-3 py-2 bg-blue-900/50 border ${errors?.deckListUrl ? 'border-red-500' : 'border-blue-700'} rounded text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="https://manabox.app/decks/xxxxx"
              />
              {errors?.deckListUrl && (
                <p className="mt-1 text-xs text-red-400">{errors.deckListUrl}</p>
              )}
            </div>
          </div>

          {/* Success or error messages after save */}
          <div className="mt-4">
            {saveSuccess && (
              <div className="mb-3 flex items-center gap-2 p-2 bg-emerald-500/15 border border-emerald-500/30 rounded text-emerald-200 text-sm">
                <CheckCircle2 className="w-4 h-4" />
                <span>Dati aggiornati con successo.</span>
              </div>
            )}
            {error && (
              <div className="mb-3 flex items-center gap-2 p-2 bg-red-500/15 border border-red-500/30 rounded text-red-200 text-sm">
                <AlertCircle className="w-4 h-4" />
                <span>{error instanceof Error ? error.message : 'Errore durante il salvataggio.'}</span>
              </div>
            )}
          </div>

          <div className="mt-2 flex flex-col sm:flex-row sm:items-center justify-between gap-3">
            <div>
              {/* Delete flow with confirm panel */}
              <DeleteRegistrationButton
                disabled={!editingAllowed}
                isDeleting={isDeleting}
                onConfirm={async () => {
                  resetDelete();
                  await deleteAsync(reg.id);
                }}
              />
              {deleteError && (
                <div className="mt-2 inline-flex items-center gap-2 p-2 bg-red-500/15 border border-red-500/30 rounded text-red-200 text-xs">
                  <AlertCircle className="w-4 h-4" />
                  <span>{deleteError instanceof Error ? deleteError.message : 'Errore cancellazione'}</span>
                </div>
              )}
            </div>
            <button
              onClick={handleSave}
              disabled={!editingAllowed || isPending}
              className={`w-full sm:w-auto inline-flex items-center justify-center gap-2 px-4 py-2 rounded-md text-white ${!editingAllowed || isPending ? 'bg-blue-700/50 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'}`}
            >
              {isPending ? <Loader2 className="w-4 h-4 animate-spin" /> : <Save className="w-4 h-4" />}
              {isPending ? 'Salvataggio...' : 'Salva modifiche'}
            </button>
          </div>
        </>
      )}
    </Card>
  );
}
