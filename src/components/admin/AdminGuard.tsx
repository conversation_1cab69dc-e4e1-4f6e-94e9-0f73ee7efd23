'use client';

import { useEffect, useState, ReactNode } from 'react';
import { useAuthContext } from '@/lib/providers/AuthProvider';
import { useRouter } from 'next/navigation';
import { useIsAdmin } from '@/hooks/useIsAdmin';

interface AdminGuardProps {
  children: ReactNode;
}

export function AdminGuard({ children }: AdminGuardProps) {
  const { user, loading } = useAuthContext();
  const { isAdmin, loading: adminLoading } = useIsAdmin();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (loading || adminLoading) return;

    const handleAuth = async () => {
      if (!user) {
        // Utente non autenticato → salva URL corrente e reindirizza a /login con next
        setIsAuthorized(false);
        setIsLoading(false);
        if (typeof window !== 'undefined') {
          const currentPath = window.location.pathname + window.location.search;
          sessionStorage.setItem('returnUrl', currentPath);
          router.push(`/login?next=${encodeURIComponent(currentPath)}`);
        }
        return;
      }

      if (!isAdmin) {
        // Autenticato ma non admin → redirect home
        setIsAuthorized(false);
        setIsLoading(false);
        router.push('/');
        return;
      }

      // Admin autenticato → autorizzato
      setIsAuthorized(true);
      setIsLoading(false);
    };

    handleAuth();
  }, [user, loading, isAdmin, adminLoading, router]);

  // Mostra un loader durante i controlli
  if (loading || adminLoading || isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-black to-blue-950 text-white flex items-center justify-center">
        <div className="animate-pulse text-blue-300">Verificando l&apos;accesso...</div>
      </div>
    );
  }

  return isAuthorized ? <>{children}</> : null;
}
