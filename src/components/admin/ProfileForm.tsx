'use client';

import { useAuthContext } from '@/lib/providers/AuthProvider';

export function ProfileForm() {
  const { user } = useAuthContext();

  return (
    <div className="space-y-8">
      <div className="p-6 bg-sky-900/30 backdrop-blur-sm rounded-lg border border-sky-500/30 shadow-lg">
        <h2 className="text-xl font-semibold mb-4 text-white">Informazioni Profilo</h2>
        
        <div className="space-y-4">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-sky-300 mb-1">
              Email
            </label>
            <input
              id="email"
              type="email"
              value={user?.email || ''}
              disabled
              className="w-full px-3 py-2 bg-blue-900/50 border border-blue-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-70 disabled:cursor-not-allowed"
            />
            <p className="mt-1 text-xs text-sky-300">L&apos;email non può essere modificata</p>
          </div>
        </div>
      </div>
    </div>
  );
} 