'use client';

import React, { useState, useEffect } from 'react';
import { Save, X, AlertCircle, Loader2 } from 'lucide-react';
import { FormField, Input, Select } from '@/components/ui/FormField';
import { Modal } from '@/components/ui/Modal';
import {
  storeSchema,
  storeUpdateSchema,
  type Store,
  type StoreFormData,
  STORE_COLOR_OPTIONS
} from '@/types/stores';
import { cn } from '@/lib/utils';
import { z } from 'zod';

interface StoreFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: StoreFormData) => Promise<void>;
  initialData?: Store;
  mode: 'create' | 'edit';
  isSubmitting?: boolean;
}

/**
 * Store form component with validation and error handling
 */
export function StoreForm({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  mode,
  isSubmitting = false
}: StoreFormProps) {
  const [formData, setFormData] = useState<StoreFormData>({
    name: '',
    address: '',
    city: '',
    color: 'circolo'
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isDirty, setIsDirty] = useState(false);

  // Initialize form data when modal opens or initialData changes
  useEffect(() => {
    if (isOpen) {
      if (mode === 'edit' && initialData) {
        setFormData({
          name: initialData.name,
          address: initialData.address,
          city: initialData.city,
          color: initialData.color || 'circolo'
        });
      } else {
        setFormData({
          name: '',
          address: '',
          city: '',
          color: 'circolo'
        });
      }
      setErrors({});
      setIsDirty(false);
    }
  }, [isOpen, mode, initialData]);

  const handleInputChange = (field: keyof StoreFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setIsDirty(true);
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    try {
      const schema = mode === 'create' ? storeSchema : storeUpdateSchema;
      schema.parse(formData);
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        error.errors.forEach((err) => {
          if (err.path.length > 0) {
            newErrors[err.path[0] as string] = err.message;
          }
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
      onClose();
    } catch (error) {
      // Error handling is done by the parent component
      console.error('Form submission error:', error);
    }
  };

  const handleClose = () => {
    if (isDirty && !isSubmitting) {
      if (window.confirm('Ci sono modifiche non salvate. Sei sicuro di voler chiudere?')) {
        onClose();
      }
    } else {
      onClose();
    }
  };

  const title = mode === 'create' ? 'Nuovo Negozio' : 'Modifica Negozio';
  const submitLabel = mode === 'create' ? 'Crea Negozio' : 'Salva Modifiche';

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={title}
      size="md"
      closeOnOverlayClick={!isDirty && !isSubmitting}
      closeOnEscape={!isDirty && !isSubmitting}
    >
      <form onSubmit={handleSubmit} className="p-6 space-y-6">
        {/* Store Name */}
        <FormField
          label="Nome Negozio"
          required
          error={errors.name}
          hint="Il nome del negozio deve essere unico"
        >
          <Input
            type="text"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="es. Fantasy World"
            error={!!errors.name}
            disabled={isSubmitting}
            maxLength={100}
          />
        </FormField>

        {/* Address */}
        <FormField
          label="Indirizzo"
          required
          error={errors.address}
          hint="Indirizzo completo del negozio"
        >
          <Input
            type="text"
            value={formData.address}
            onChange={(e) => handleInputChange('address', e.target.value)}
            placeholder="es. Via della Pace, 36"
            error={!!errors.address}
            disabled={isSubmitting}
            maxLength={200}
          />
        </FormField>

        {/* City */}
        <FormField
          label="Città"
          required
          error={errors.city}
          hint="Città dove si trova il negozio"
        >
          <Input
            type="text"
            value={formData.city}
            onChange={(e) => handleInputChange('city', e.target.value)}
            placeholder="es. Macerata"
            error={!!errors.city}
            disabled={isSubmitting}
            maxLength={100}
          />
        </FormField>

        {/* Color */}
        <FormField
          label="Colore"
          error={errors.color}
          hint="Colore per identificare il negozio nel calendario"
        >
          <Select
            value={formData.color}
            onChange={(e) => handleInputChange('color', e.target.value)}
            error={!!errors.color}
            disabled={isSubmitting}
          >
            {STORE_COLOR_OPTIONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </Select>
        </FormField>

        {/* Color Preview */}
        {formData.color && (
          <div className="flex items-center gap-2 text-sm text-blue-300">
            <span>Anteprima colore:</span>
            <div 
              className={cn(
                'w-4 h-4 rounded-full border border-white/20',
                formData.color === 'circolo' ? 'bg-blue-500' : `bg-${formData.color}`
              )}
            />
          </div>
        )}

        {/* Form Actions */}
        <div className="flex gap-3 pt-4 border-t border-blue-500/20">
          <button
            type="button"
            onClick={handleClose}
            disabled={isSubmitting}
            className="flex-1 px-4 py-2 bg-black/30 hover:bg-blue-900/50 border border-blue-500/30 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <X size={16} className="mr-2 inline" />
            Annulla
          </button>
          
          <button
            type="submit"
            disabled={isSubmitting || Object.keys(errors).length > 0}
            className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {isSubmitting ? (
              <>
                <Loader2 size={16} className="mr-2 animate-spin" />
                {mode === 'create' ? 'Creazione...' : 'Salvataggio...'}
              </>
            ) : (
              <>
                <Save size={16} className="mr-2" />
                {submitLabel}
              </>
            )}
          </button>
        </div>

        {/* Validation Summary */}
        {Object.keys(errors).length > 0 && (
          <div className="p-3 bg-red-900/20 border border-red-500/30 rounded-lg">
            <div className="flex items-center gap-2 text-red-400 text-sm">
              <AlertCircle size={16} />
              <span>Correggi gli errori prima di continuare</span>
            </div>
          </div>
        )}
      </form>
    </Modal>
  );
}
