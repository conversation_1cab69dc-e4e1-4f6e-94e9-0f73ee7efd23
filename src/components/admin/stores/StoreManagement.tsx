'use client';

import React, { useState } from 'react';
import { toast } from 'react-hot-toast';
import { StoreList } from './StoreList';
import { StoreForm } from './StoreForm';
import { StoreDeleteConfirmation } from './StoreDeleteConfirmation';
import { useStoreManagement } from '@/lib/hooks/useStores';
import { ErrorBoundary } from '@/components/shared/ErrorBoundary';
import { ErrorFallback } from '@/components/shared/ErrorFallback';
import type { Store, StoreFormData, StoreUpdateFormData } from '@/types/stores';
import { logger } from '@/lib/utils/logger';

interface StoreManagementState {
  showCreateForm: boolean;
  showEditForm: boolean;
  showDeleteConfirmation: boolean;
  selectedStore: Store | null;
}

/**
 * Main store management component with full CRUD functionality
 */
export function StoreManagement() {
  const {
    stores,
    isLoading,
    error,
    createStore,
    updateStore,
    deleteStore,
    isCreating,
    isUpdating,
    isDeleting,
    createError,
    updateError,
    deleteError
  } = useStoreManagement();

  const [state, setState] = useState<StoreManagementState>({
    showCreateForm: false,
    showEditForm: false,
    showDeleteConfirmation: false,
    selectedStore: null
  });

  // Handle create store
  const handleCreateStore = () => {
    setState(prev => ({
      ...prev,
      showCreateForm: true,
      selectedStore: null
    }));
  };

  const handleCreateSubmit = async (data: StoreFormData) => {
    try {
      await createStore(data);
      setState(prev => ({ ...prev, showCreateForm: false }));
      toast.success('Negozio creato con successo!');
      logger.info('Store created successfully', { component: 'StoreManagement', storeName: data.name });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Errore durante la creazione del negozio';
      toast.error(errorMessage);
      logger.error('Failed to create store', error, { component: 'StoreManagement', data });
      throw error; // Re-throw to prevent form from closing
    }
  };

  const handleCreateCancel = () => {
    setState(prev => ({ ...prev, showCreateForm: false }));
  };

  // Handle edit store
  const handleEditStore = (store: Store) => {
    setState(prev => ({
      ...prev,
      showEditForm: true,
      selectedStore: store
    }));
  };

  const handleEditSubmit = async (data: StoreFormData) => {
    if (!state.selectedStore) return;

    // Convert to update format by making all fields optional except those provided
    const updateData: StoreUpdateFormData = {
      name: data.name,
      address: data.address,
      city: data.city,
      color: data.color
    };

    try {
      await updateStore({ id: state.selectedStore.id, storeData: updateData });
      setState(prev => ({ ...prev, showEditForm: false, selectedStore: null }));
      toast.success('Negozio aggiornato con successo!');
      logger.info('Store updated successfully', {
        component: 'StoreManagement',
        storeId: state.selectedStore.id,
        storeName: data.name
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Errore durante l\'aggiornamento del negozio';
      toast.error(errorMessage);
      logger.error('Failed to update store', error, {
        component: 'StoreManagement',
        storeId: state.selectedStore.id,
        data
      });
      throw error; // Re-throw to prevent form from closing
    }
  };

  const handleEditCancel = () => {
    setState(prev => ({ ...prev, showEditForm: false, selectedStore: null }));
  };

  // Handle delete store
  const handleDeleteStore = (store: Store) => {
    setState(prev => ({
      ...prev,
      showDeleteConfirmation: true,
      selectedStore: store
    }));
  };

  const handleDeleteConfirm = async () => {
    if (!state.selectedStore) return;

    try {
      await deleteStore(state.selectedStore.id);
      setState(prev => ({ ...prev, showDeleteConfirmation: false, selectedStore: null }));
      toast.success('Negozio eliminato con successo!');
      logger.info('Store deleted successfully', { 
        component: 'StoreManagement', 
        storeId: state.selectedStore.id,
        storeName: state.selectedStore.name 
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Errore durante l\'eliminazione del negozio';
      toast.error(errorMessage);
      logger.error('Failed to delete store', error, { 
        component: 'StoreManagement', 
        storeId: state.selectedStore.id 
      });
      throw error; // Re-throw to prevent modal from closing
    }
  };

  const handleDeleteCancel = () => {
    setState(prev => ({ ...prev, showDeleteConfirmation: false, selectedStore: null }));
  };

  // Show error notifications for operation failures
  React.useEffect(() => {
    if (createError) {
      toast.error(`Errore creazione: ${createError.message}`);
    }
  }, [createError]);

  React.useEffect(() => {
    if (updateError) {
      toast.error(`Errore aggiornamento: ${updateError.message}`);
    }
  }, [updateError]);

  React.useEffect(() => {
    if (deleteError) {
      toast.error(`Errore eliminazione: ${deleteError.message}`);
    }
  }, [deleteError]);

  return (
    <ErrorBoundary
      level="section"
      fallback={<ErrorFallback variant="detailed" />}
      context={{ component: 'StoreManagement', page: 'admin-dashboard' }}
    >
      <div className="space-y-6">
        {/* Main Store List */}
        <StoreList
          stores={stores}
          isLoading={isLoading}
          error={error}
          onCreateStore={handleCreateStore}
          onEditStore={handleEditStore}
          onDeleteStore={handleDeleteStore}
        />

        {/* Create Store Form */}
        <StoreForm
          isOpen={state.showCreateForm}
          onClose={handleCreateCancel}
          onSubmit={handleCreateSubmit}
          mode="create"
          isSubmitting={isCreating}
        />

        {/* Edit Store Form */}
        <StoreForm
          isOpen={state.showEditForm}
          onClose={handleEditCancel}
          onSubmit={handleEditSubmit}
          initialData={state.selectedStore || undefined}
          mode="edit"
          isSubmitting={isUpdating}
        />

        {/* Delete Confirmation */}
        <StoreDeleteConfirmation
          isOpen={state.showDeleteConfirmation}
          onClose={handleDeleteCancel}
          onConfirm={handleDeleteConfirm}
          store={state.selectedStore}
          isDeleting={isDeleting}
        />
      </div>
    </ErrorBoundary>
  );
}
