'use client';

import React from 'react';
import { Store, Edit3, Trash2, Plus, MapPin, Building2 } from 'lucide-react';
import { LoadingState } from '@/components/ui/LoadingState';
import { ErrorState } from '@/components/ui/ErrorState';
import { EmptyState } from '@/components/ui/EmptyState';
import type { Store as StoreType } from '@/types/stores';
import { cn } from '@/lib/utils';

interface StoreListProps {
  stores: StoreType[];
  isLoading: boolean;
  error: Error | null;
  onCreateStore: () => void;
  onEditStore: (store: StoreType) => void;
  onDeleteStore: (store: StoreType) => void;
  className?: string;
}

/**
 * Store list component with proper loading states and error handling
 */
export function StoreList({
  stores,
  isLoading,
  error,
  onCreateStore,
  onEditStore,
  onDeleteStore,
  className
}: StoreListProps) {
  if (isLoading) {
    return (
      <div className={cn('space-y-4', className)}>
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold text-white">Gestione Negozi</h2>
          <button
            disabled
            className="px-4 py-2 bg-blue-600/50 text-white rounded-lg opacity-50 cursor-not-allowed"
          >
            <Plus size={16} className="mr-2 inline" />
            Nuovo Negozio
          </button>
        </div>
        <LoadingState text="Caricamento negozi..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn('space-y-4', className)}>
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold text-white">Gestione Negozi</h2>
          <button
            onClick={onCreateStore}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            <Plus size={16} className="mr-2 inline" />
            Nuovo Negozio
          </button>
        </div>
        <ErrorState
          type="error"
          title="Errore nel caricamento"
          message={error.message}
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  if (!stores || stores.length === 0) {
    return (
      <div className={cn('space-y-4', className)}>
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold text-white">Gestione Negozi</h2>
          <button
            onClick={onCreateStore}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            <Plus size={16} className="mr-2 inline" />
            Nuovo Negozio
          </button>
        </div>
        <EmptyState
          icon={Store}
          title="Nessun negozio trovato"
          description="Inizia creando il primo negozio per la tua lega."
          action={
            <button
              onClick={onCreateStore}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center gap-2"
            >
              <Plus size={16} />
              Crea Primo Negozio
            </button>
          }
        />
      </div>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-white">
          Gestione Negozi ({stores.length})
        </h2>
        <button
          onClick={onCreateStore}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center gap-2"
        >
          <Plus size={16} />
          Nuovo Negozio
        </button>
      </div>

      {/* Store Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {stores.map((store) => (
          <StoreCard
            key={store.id}
            store={store}
            onEdit={() => onEditStore(store)}
            onDelete={() => onDeleteStore(store)}
          />
        ))}
      </div>
    </div>
  );
}

interface StoreCardProps {
  store: StoreType;
  onEdit: () => void;
  onDelete: () => void;
}

/**
 * Individual store card component
 */
function StoreCard({ store, onEdit, onDelete }: StoreCardProps) {
  const getColorClass = (color?: string | null) => {
    if (!color || color === 'circolo') return 'bg-blue-500';
    return `bg-${color}`;
  };

  return (
    <div className="bg-black/20 backdrop-blur-sm rounded-lg border border-blue-500/30 p-4 hover:border-blue-400/50 transition-colors">
      {/* Color indicator */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          <div 
            className={cn(
              'w-3 h-3 rounded-full',
              getColorClass(store.color)
            )}
          />
          <Building2 size={16} className="text-blue-300" />
        </div>
        <div className="flex gap-1">
          <button
            onClick={onEdit}
            className="p-1 hover:bg-blue-900/50 rounded transition-colors"
            title="Modifica negozio"
          >
            <Edit3 size={14} className="text-blue-300 hover:text-white" />
          </button>
          <button
            onClick={onDelete}
            className="p-1 hover:bg-red-900/50 rounded transition-colors"
            title="Elimina negozio"
          >
            <Trash2 size={14} className="text-red-400 hover:text-red-300" />
          </button>
        </div>
      </div>

      {/* Store info */}
      <div className="space-y-2">
        <h3 className="font-semibold text-white text-sm line-clamp-2">
          {store.name}
        </h3>
        
        <div className="flex items-start gap-1 text-xs text-blue-300">
          <MapPin size={12} className="mt-0.5 flex-shrink-0" />
          <div className="space-y-1">
            <p className="line-clamp-2">{store.address}</p>
            <p className="font-medium">{store.city}</p>
          </div>
        </div>

        {/* Created date */}
        <p className="text-xs text-gray-400 mt-2">
          Creato: {new Date(store.created_at).toLocaleDateString('it-IT')}
        </p>
      </div>
    </div>
  );
}
