'use client';

import React, { useState } from 'react';
import { Trash2, AlertTriangle, X, Loader2 } from 'lucide-react';
import { Modal } from '@/components/ui/Modal';
import type { Store } from '@/types/stores';
import { cn } from '@/lib/utils';

interface StoreDeleteConfirmationProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  store: Store | null;
  isDeleting?: boolean;
}

/**
 * Store delete confirmation dialog with safety checks
 */
export function StoreDeleteConfirmation({
  isOpen,
  onClose,
  onConfirm,
  store,
  isDeleting = false
}: StoreDeleteConfirmationProps) {
  const [confirmationText, setConfirmationText] = useState('');
  const [hasTypedCorrectly, setHasTypedCorrectly] = useState(false);

  // Reset state when modal opens/closes
  React.useEffect(() => {
    if (isOpen) {
      setConfirmationText('');
      setHasTypedCorrectly(false);
    }
  }, [isOpen]);

  // Check if user has typed the store name correctly
  React.useEffect(() => {
    if (store && confirmationText.trim().toLowerCase() === store.name.toLowerCase()) {
      setHasTypedCorrectly(true);
    } else {
      setHasTypedCorrectly(false);
    }
  }, [confirmationText, store]);

  const handleConfirm = async () => {
    if (!hasTypedCorrectly || !store) return;
    
    try {
      await onConfirm();
      onClose();
    } catch (error) {
      // Error handling is done by the parent component
      console.error('Delete confirmation error:', error);
    }
  };

  const handleClose = () => {
    if (!isDeleting) {
      onClose();
    }
  };

  if (!store) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Conferma Eliminazione"
      size="md"
      closeOnOverlayClick={!isDeleting}
      closeOnEscape={!isDeleting}
    >
      <div className="p-6 space-y-6">
        {/* Warning Header */}
        <div className="flex items-center gap-3 p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
          <AlertTriangle size={24} className="text-red-400 flex-shrink-0" />
          <div>
            <h3 className="font-semibold text-red-400">Attenzione: Azione Irreversibile</h3>
            <p className="text-sm text-red-300 mt-1">
              Questa azione non può essere annullata.
            </p>
          </div>
        </div>

        {/* Store Information */}
        <div className="space-y-3">
          <p className="text-white">
            Stai per eliminare il seguente negozio:
          </p>
          
          <div className="p-4 bg-black/20 border border-blue-500/30 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <div 
                className={cn(
                  'w-3 h-3 rounded-full',
                  store.color === 'circolo' || !store.color ? 'bg-blue-500' : `bg-${store.color}`
                )}
              />
              <h4 className="font-semibold text-white">{store.name}</h4>
            </div>
            <p className="text-sm text-blue-300">{store.address}</p>
            <p className="text-sm text-blue-300">{store.city}</p>
          </div>
        </div>

        {/* Consequences Warning */}
        <div className="space-y-2">
          <h4 className="font-medium text-yellow-400">Conseguenze dell&apos;eliminazione:</h4>
          <ul className="text-sm text-yellow-300 space-y-1 ml-4">
            <li>• Il negozio sarà rimosso permanentemente dal sistema</li>
            <li>• I tornei associati a questo negozio potrebbero essere interessati</li>
            <li>• Le statistiche storiche rimarranno, ma senza riferimento al negozio</li>
            <li>• Questa azione non può essere annullata</li>
          </ul>
        </div>

        {/* Confirmation Input */}
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Per confermare, digita il nome del negozio: <span className="font-bold text-red-400">{store.name}</span>
            </label>
            <input
              type="text"
              value={confirmationText}
              onChange={(e) => setConfirmationText(e.target.value)}
              placeholder={`Digita "${store.name}" per confermare`}
              disabled={isDeleting}
              className={cn(
                'w-full px-3 py-2 bg-black/30 border rounded-lg text-sm',
                'focus:outline-none focus:ring-2 transition-colors',
                'placeholder:text-gray-400',
                hasTypedCorrectly 
                  ? 'border-green-500 focus:ring-green-500/50' 
                  : 'border-red-500 focus:ring-red-500/50'
              )}
            />
          </div>
          
          {confirmationText && !hasTypedCorrectly && (
            <p className="text-xs text-red-400">
              Il nome non corrisponde. Digita esattamente: {store.name}
            </p>
          )}
          
          {hasTypedCorrectly && (
            <p className="text-xs text-green-400 flex items-center gap-1">
              ✓ Nome confermato
            </p>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4 border-t border-blue-500/20">
          <button
            type="button"
            onClick={handleClose}
            disabled={isDeleting}
            className="flex-1 px-4 py-2 bg-black/30 hover:bg-blue-900/50 border border-blue-500/30 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <X size={16} className="mr-2 inline" />
            Annulla
          </button>
          
          <button
            type="button"
            onClick={handleConfirm}
            disabled={!hasTypedCorrectly || isDeleting}
            className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {isDeleting ? (
              <>
                <Loader2 size={16} className="mr-2 animate-spin" />
                Eliminazione...
              </>
            ) : (
              <>
                <Trash2 size={16} className="mr-2" />
                Elimina Definitivamente
              </>
            )}
          </button>
        </div>

        {/* Final Warning */}
        <div className="text-xs text-gray-400 text-center pt-2 border-t border-gray-700">
          Assicurati di aver fatto un backup dei dati importanti prima di procedere.
        </div>
      </div>
    </Modal>
  );
}
