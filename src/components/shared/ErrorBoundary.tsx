"use client";

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { logger } from '@/lib/utils/logger';
import { ErrorState } from '@/components/ui/ErrorState';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  isolate?: boolean;
  showDetails?: boolean;
  resetKeys?: Array<string | number>;
  resetOnPropsChange?: boolean;
  level?: 'page' | 'section' | 'component';
  context?: Record<string, unknown>;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  errorCount: number;
}

/**
 * Enhanced Error Boundary component for catching and handling React errors
 * Provides error logging, recovery mechanisms, and customizable fallback UI
 */
export class ErrorBoundary extends Component<Props, State> {
  private resetTimeoutId: NodeJS.Timeout | null = null;
  private previousResetKeys: Array<string | number> = [];

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      errorCount: 0
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const { onError, level = 'component', context } = this.props;
    const { errorCount } = this.state;

    // Log error with context
    logger.error(`Error caught by ${level} boundary`, error, {
      component: 'ErrorBoundary',
      level,
      errorInfo: errorInfo.componentStack,
      errorCount: errorCount + 1,
      source: error.stack,
      ...context
    });

    // Call custom error handler if provided
    if (onError) {
      onError(error, errorInfo);
    }

    // Update state with error details
    this.setState({
      errorInfo,
      errorCount: errorCount + 1
    });

    // Auto-reset after 3 errors to prevent infinite loops
    if (errorCount >= 2) {
      logger.warn('Multiple errors detected, scheduling auto-reset', {
        component: 'ErrorBoundary',
        errorCount: errorCount + 1
      });
      
      this.scheduleReset(5000);
    }
  }

  componentDidUpdate(prevProps: Props) {
    const { resetKeys, resetOnPropsChange } = this.props;
    const { hasError } = this.state;

    // Reset on prop changes if enabled
    if (hasError && resetOnPropsChange && prevProps.children !== this.props.children) {
      this.resetErrorBoundary();
    }

    // Reset when resetKeys change
    if (resetKeys && hasError) {
      const hasResetKeyChanged = resetKeys.some(
        (key, index) => key !== this.previousResetKeys[index]
      );

      if (hasResetKeyChanged) {
        this.resetErrorBoundary();
      }
    }

    this.previousResetKeys = resetKeys || [];
  }

  componentWillUnmount() {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  scheduleReset = (delay: number) => {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }

    this.resetTimeoutId = setTimeout(() => {
      this.resetErrorBoundary();
    }, delay);
  };

  resetErrorBoundary = () => {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
      this.resetTimeoutId = null;
    }

    logger.info('Resetting error boundary', {
      component: 'ErrorBoundary',
      level: this.props.level
    });

    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      errorCount: 0
    });
  };

  handleRetry = () => {
    this.resetErrorBoundary();
  };

  render() {
    const { hasError, error, errorInfo, errorCount } = this.state;
    const { children, fallback, isolate, showDetails = false, level = 'component' } = this.props;

    if (hasError && error) {
      // Use custom fallback if provided
      if (fallback) {
        return <>{fallback}</>;
      }

      // Determine error message based on level
      const titles = {
        page: 'Errore della pagina',
        section: 'Errore della sezione',
        component: 'Errore del componente'
      };

      const messages = {
        page: 'Si è verificato un errore nel caricamento della pagina.',
        section: 'Si è verificato un errore in questa sezione.',
        component: 'Si è verificato un errore in questo componente.'
      };

      // Show isolated error for component-level boundaries
      if (isolate && level === 'component') {
        return (
          <div className="p-4 border border-red-500/30 rounded-lg bg-red-900/10">
            <ErrorState
              type="error"
              title={titles[level]}
              message={messages[level]}
              error={error}
              showDetails={showDetails}
              onRetry={this.handleRetry}
            />
          </div>
        );
      }

      // Full error display for page/section level
      return (
        <div className="min-h-[400px] flex items-center justify-center p-6">
          <div className="max-w-2xl w-full">
            <ErrorState
              type="error"
              title={titles[level]}
              message={messages[level]}
              error={error}
              showDetails={showDetails || level === 'page'}
              onRetry={this.handleRetry}
            />
            
            {errorCount > 1 && (
              <div className="mt-4 p-3 bg-yellow-900/20 border border-yellow-500/30 rounded-lg">
                <p className="text-sm text-yellow-300">
                  Questo errore si è verificato {errorCount} volte. 
                  Il componente verrà resettato automaticamente se continua.
                </p>
              </div>
            )}

            {showDetails && errorInfo && (
              <details className="mt-4">
                <summary className="cursor-pointer text-sm text-blue-400 hover:text-blue-300">
                  Mostra stack del componente
                </summary>
                <pre className="mt-2 p-3 bg-black/30 rounded text-xs text-gray-400 overflow-x-auto">
                  {errorInfo.componentStack}
                </pre>
              </details>
            )}
          </div>
        </div>
      );
    }

    return children;
  }
}

// Higher-order component for wrapping components with error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
}
