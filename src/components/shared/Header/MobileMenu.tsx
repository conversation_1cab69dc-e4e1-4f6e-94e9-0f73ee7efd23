import React from 'react';
import { NavLink } from './NavLink';
// import { DropdownLink } from './DropdownLink'; // TEMPORANEAMENTE COMMENTATO
import { NAV_LINKS, /* TOOLS_LINKS, */ TOOLS_INSERT_INDEX, CurrentPage } from './constants';
import { UserMenu } from '../UserMenu';

interface MobileMenuProps {
  isOpen: boolean;
  currentPage?: CurrentPage;
  onClose: () => void;
}

/**
 * Mobile menu component with vertical navigation and collapsible Tools section
 */
export const MobileMenu: React.FC<MobileMenuProps> = ({
  isOpen,
  currentPage,
  onClose
}) => {
  // TEMPORANEAMENTE COMMENTATO - Tools dropdown state
  // const [toolsDropdownOpen, setToolsDropdownOpen] = useState(false);

  // const toggleToolsDropdown = () => {
  //   setToolsDropdownOpen(!toolsDropdownOpen);
  // };

  if (!isOpen) return null;

  return (
    <div className="sm:hidden py-2 px-4 sm:px-6 border-t border-blue-500/30">
      <div className="flex flex-col space-y-2">
        {/* Links before Tools */}
        {NAV_LINKS.slice(0, TOOLS_INSERT_INDEX + 1).map((link) => (
          <NavLink
            key={link.id}
            href={link.href}
            label={link.label}
            isActive={currentPage === link.id}
            onClick={onClose}
            isMobile={true}
            isExternal={link.isExternal}
          />
        ))}
        
        {/* Tools Dropdown - Mobile - TEMPORANEAMENTE NASCOSTO
        <div className="relative">
          <DropdownLink
            label="Tools"
            isActive={currentPage === "tools"}
            onClick={toggleToolsDropdown}
            isMobile={true}
          />

          {toolsDropdownOpen && (
            <div className="mt-1 bg-black/20 backdrop-blur-sm border border-blue-500/30 rounded-lg shadow-lg z-10">
              <div className="py-1">
                {TOOLS_LINKS.map((tool) => (
                  <a
                    key={tool.id}
                    href={tool.href}
                    className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm hover:bg-blue-900/50 transition-colors"
                    onClick={onClose}
                  >
                    {tool.label}
                  </a>
                ))}
              </div>
            </div>
          )}
        </div>
        */}
        
        {/* Links after Tools */}
        {NAV_LINKS.slice(TOOLS_INSERT_INDEX + 1).map((link) => (
          <NavLink
            key={link.id}
            href={link.href}
            label={link.label}
            isActive={currentPage === link.id}
            onClick={onClose}
            isMobile={true}
            isExternal={link.isExternal}
          />
        ))}
        
        {/* Separator for mobile menu */}
        <div className="border-t border-blue-500/30 my-2"></div>
        
        {/* Area Riservata - Mobile */}
        <div className="py-1">
          <UserMenu />
        </div>
      </div>
    </div>
  );
};
