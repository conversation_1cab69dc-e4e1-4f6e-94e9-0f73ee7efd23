import React from 'react';
import { NavLink } from './NavLink';
// import { DropdownLink } from './DropdownLink'; // TEMPORANEAMENTE COMMENTATO
import { NAV_LINKS, /* TOOLS_LINKS, */ TOOLS_INSERT_INDEX, CurrentPage } from './constants';
import { UserMenu } from '../UserMenu';

interface DesktopNavigationProps {
  currentPage?: CurrentPage;
}

/**
 * Desktop navigation component with horizontal menu and Tools dropdown
 */
export const DesktopNavigation: React.FC<DesktopNavigationProps> = ({ currentPage }) => {
  // TEMPORANEAMENTE COMMENTATO - Tools dropdown state
  // const [toolsDropdownOpen, setToolsDropdownOpen] = useState(false);

  // const toggleToolsDropdown = () => {
  //   setToolsDropdownOpen(!toolsDropdownOpen);
  // };

  return (
    <>
      {/* Desktop Navigation - Centered */}
      <div className="hidden sm:flex sm:items-center sm:space-x-1 lg:space-x-2 absolute left-1/2 transform -translate-x-1/2">
        {/* Links before Tools dropdown */}
        {NAV_LINKS.slice(0, TOOLS_INSERT_INDEX + 1).map((link) => (
          <NavLink
            key={link.id}
            href={link.href}
            label={link.label}
            isActive={currentPage === link.id}
            isExternal={link.isExternal}
          />
        ))}
        
        {/* Tools Dropdown - TEMPORANEAMENTE NASCOSTO
        <div className="relative">
          <DropdownLink
            label="Tools"
            isActive={currentPage === "tools"}
            onClick={toggleToolsDropdown}
          />

          {toolsDropdownOpen && (
            <div className="absolute left-0 mt-1 w-48 bg-black/20 backdrop-blur-sm border border-blue-500/30 rounded-lg shadow-lg z-10">
              <div className="py-1">
                {TOOLS_LINKS.map((tool) => (
                  <a
                    key={tool.id}
                    href={tool.href}
                    className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm hover:bg-blue-900/50 transition-colors"
                    role="menuitem"
                  >
                    {tool.label}
                  </a>
                ))}
              </div>
            </div>
          )}
        </div>
        */}
        
        {/* Links after Tools dropdown */}
        {NAV_LINKS.slice(TOOLS_INSERT_INDEX + 1).map((link) => (
          <NavLink
            key={link.id}
            href={link.href}
            label={link.label}
            isActive={currentPage === link.id}
            isExternal={link.isExternal}
          />
        ))}
      </div>

      {/* Area Riservata - Desktop - Right side */}
      <div className="hidden sm:flex sm:items-center">
        <UserMenu />
      </div>
    </>
  );
};
