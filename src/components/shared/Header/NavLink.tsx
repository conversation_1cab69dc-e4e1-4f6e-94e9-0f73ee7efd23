import React, { memo } from 'react';
import Link from 'next/link';
import { usePrefetchOnHover } from '@/lib/utils/navigation';

export interface NavLinkProps {
  href: string;
  label: string;
  isActive: boolean;
  onClick?: () => void;
  isMobile?: boolean;
  isExternal?: boolean;
}

/**
 * Reusable navigation link component
 * Handles both internal and external links with appropriate styling
 */
export const NavLink = memo<NavLinkProps>(function NavLink({
  href, 
  label, 
  isActive, 
  onClick, 
  isMobile = false,
  isExternal = false 
}) {
  const handlePrefetch = usePrefetchOnHover(href);
  
  const baseClasses = "text-sm font-medium transition-colors duration-200 relative py-2 rounded-md whitespace-nowrap";
  
  const sizeClasses = isMobile 
    ? "px-1" 
    : "px-2 lg:px-4 hover:bg-blue-900/20";
  
  const activeClasses = isActive
    ? "text-white bg-blue-800/30"
    : "text-blue-200 hover:text-white";
  
  const className = `${baseClasses} ${sizeClasses} ${activeClasses}`;
  
  // Use Next.js Link for internal navigation with prefetching
  if (!isExternal) {
    return (
      <Link 
        href={href} 
        className={className}
        onClick={onClick}
        onMouseEnter={handlePrefetch}
        prefetch={false} // We handle prefetch manually on hover
      >
        {label}
      </Link>
    );
  }
  
  // Use regular anchor for external links
  const linkProps = {
    href,
    className,
    onClick,
    target: "_blank",
    rel: "noopener noreferrer"
  };
  
  return <a {...linkProps}>{label}</a>;
});
