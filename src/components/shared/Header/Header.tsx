"use client";

import React from 'react';
import { Menu, X } from "lucide-react";
import { useMenuState } from "@/hooks/useMenuState";
import { DesktopNavigation } from "./DesktopNavigation";
import { MobileMenu } from "./MobileMenu";
import { CurrentPage } from "./constants";
import Image from "next/image";
import Link from "next/link";

interface HeaderProps {
  currentPage?: CurrentPage;
}

/**
 * Main header component with responsive navigation
 * Refactored to use smaller, reusable components
 */
export function Header({ currentPage }: HeaderProps) {
  const { mobileMenuOpen, toggleMenu, closeMenu } = useMenuState();

  return (
    <header className="sticky top-0 z-50 px-4 sm:px-6 lg:px-8 pt-4">
      <nav 
        className="max-w-7xl xl:max-w-full xl:mx-8 2xl:mx-16 mx-auto bg-black/20 backdrop-blur-md border border-blue-500/30 rounded-2xl shadow-lg" 
        aria-label="Top"
      >
        <div className="flex items-center justify-between h-14 px-4 sm:px-6">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <Image 
                src="/logo.png" 
                alt="Lega Pauper Adriatica" 
                width={120} 
                height={40} 
                className="h-10 sm:h-12 w-auto" 
                priority
              />
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="sm:hidden flex items-center">
            <button
              type="button"
              className="text-blue-300 hover:text-white p-2 rounded-md hover:bg-blue-900/20 transition-colors duration-200 flex items-center justify-center"
              onClick={toggleMenu}
              aria-label={mobileMenuOpen ? "Chiudi menu" : "Apri menu"}
              aria-expanded={mobileMenuOpen}
            >
              {mobileMenuOpen ? <X size={28} /> : <Menu size={28} />}
            </button>
          </div>

          {/* Desktop Navigation */}
          <DesktopNavigation currentPage={currentPage} />
        </div>

        {/* Mobile Menu */}
        <MobileMenu 
          isOpen={mobileMenuOpen}
          currentPage={currentPage}
          onClose={closeMenu}
        />
      </nav>
    </header>
  );
}
