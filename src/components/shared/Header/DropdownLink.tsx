import React from 'react';
import { ChevronDown } from 'lucide-react';

export interface DropdownLinkProps {
  label: string;
  isActive: boolean;
  onClick?: () => void;
  isMobile?: boolean;
}

/**
 * Dropdown link component for expandable menu items (e.g., Tools)
 */
export const DropdownLink: React.FC<DropdownLinkProps> = ({ 
  label, 
  isActive, 
  onClick, 
  isMobile = false 
}) => {
  const baseClasses = "text-sm font-medium transition-colors duration-200 relative py-2 rounded-md flex items-center";
  
  const sizeClasses = isMobile 
    ? "px-1" 
    : "px-4 hover:bg-blue-900/20";
  
  const activeClasses = isActive
    ? "text-white bg-blue-800/30"
    : "text-blue-200 hover:text-white";
  
  const className = `${baseClasses} ${sizeClasses} ${activeClasses}`;
  
  return (
    <button
      className={className}
      onClick={onClick}
      type="button"
      aria-expanded={false}
      aria-haspopup="true"
    >
      {label}
      <ChevronDown size={16} className="ml-1" />
    </button>
  );
};
