export function Footer() {
  return (
    <footer className="p-4 sm:p-6 text-center sm:text-left text-blue-300/70 text-xs sm:text-sm mt-8 sm:mt-12 border-t border-blue-500/20">
      <div className="sm:flex sm:justify-between sm:items-center">
        <div>
          <p>© {new Date().getFullYear()} Lega Pauper Adriatica. Tutti i diritti riservati.</p>
          <p className="mt-1">
            <span className="text-xs">Magic: The Gathering e tutti i nomi associati sono proprietà di Wizards of the Coast.</span>
          </p>
        </div>
        
        <div className="mt-3 sm:mt-0 flex justify-center sm:justify-end space-x-4">
          <a 
            href="https://www.twitch.tv/legapauperadriatica" 
            target="_blank" 
            rel="noopener noreferrer" 
            className="text-blue-300 hover:text-blue-100 transition-colors"
            aria-label="Twitch"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M21 2H3v16h5v4l4-4h5l4-4V2zm-10 9V7m5 4V7"></path>
            </svg>
          </a>
          <a 
            href="https://www.facebook.com/profile.php?id=100091855143724" 
            target="_blank" 
            rel="noopener noreferrer" 
            className="text-blue-300 hover:text-blue-100 transition-colors"
            aria-label="Facebook"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
            </svg>
          </a>
          <a 
            href="https://www.instagram.com/lega_pauper_adriatica" 
            target="_blank" 
            rel="noopener noreferrer" 
            className="text-blue-300 hover:text-blue-100 transition-colors"
            aria-label="Instagram"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
              <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
              <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
            </svg>
          </a>
        </div>
      </div>
    </footer>
  );
} 