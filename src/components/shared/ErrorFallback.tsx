"use client";

import React from 'react';
import { AlertTriangle, RefreshCw, Home, ChevronDown, ChevronUp } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Card } from '@/components/ui/Card';
import { useRouter } from 'next/navigation';

interface ErrorFallbackProps {
  error?: Error;
  resetError?: () => void;
  variant?: 'minimal' | 'detailed' | 'fullscreen';
  showHomeButton?: boolean;
  className?: string;
  onRetry?: () => void;
  message?: string;
}

/**
 * ErrorFallback component for displaying user-friendly error messages
 * Can be used as a fallback UI in error boundaries
 */
export function ErrorFallback({
  error,
  resetError,
  variant = 'detailed',
  showHomeButton = true,
  className,
  onRetry,
  message
}: ErrorFallbackProps) {
  const router = useRouter();
  const [showDetails, setShowDetails] = React.useState(false);

  const handleGoHome = () => {
    router.push('/');
  };

  if (variant === 'minimal') {
    return (
      <div className={cn('p-4 text-center', className)}>
        <AlertTriangle className="mx-auto mb-2 text-yellow-500" size={24} />
        <p className="text-sm text-gray-300">
          {message || 'Qualcosa è andato storto'}
        </p>
        {(resetError || onRetry) && (
          <button
            onClick={resetError || onRetry}
            className="mt-2 text-xs text-blue-400 hover:text-blue-300"
          >
            Riprova
          </button>
        )}
      </div>
    );
  }

  if (variant === 'fullscreen') {
    return (
      <div className={cn(
        'min-h-screen flex items-center justify-center p-6',
        'bg-gradient-to-br from-gray-900 via-blue-900 to-gray-900',
        className
      )}>
        <div className="max-w-md w-full text-center">
          <div className="mb-8">
            <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-red-900/30 border border-red-500/30">
              <AlertTriangle className="text-red-400" size={40} />
            </div>
          </div>

          <h1 className="text-3xl font-bold text-white mb-4">
            Ops! Qualcosa è andato storto
          </h1>
          
          <p className="text-lg text-gray-300 mb-8">
            Si è verificato un errore imprevisto. 
            Ci scusiamo per l&apos;inconveniente.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            {(resetError || onRetry) && (
              <button
                onClick={resetError || onRetry}
                className="inline-flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                <RefreshCw size={18} />
                Riprova
              </button>
            )}
            
            {showHomeButton && (
              <button
                onClick={handleGoHome}
                className="inline-flex items-center justify-center gap-2 px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
              >
                <Home size={18} />
                Torna alla Home
              </button>
            )}
          </div>

          {error && (
            <div className="mt-8">
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="inline-flex items-center gap-2 text-sm text-gray-400 hover:text-gray-300"
              >
                {showDetails ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
                {showDetails ? 'Nascondi' : 'Mostra'} dettagli tecnici
              </button>
              
              {showDetails && (
                <div className="mt-4 p-4 bg-black/50 rounded-lg text-left">
                  <p className="text-sm font-mono text-red-400 mb-2">
                    {error.message}
                  </p>
                  {error.stack && (
                    <pre className="text-xs text-gray-500 overflow-x-auto">
                      {error.stack}
                    </pre>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }

  // Default detailed variant
  return (
    <Card variant="bordered" className={cn('max-w-lg mx-auto', className)}>
      <Card.Body>
        <div className="flex items-start gap-4">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 rounded-full bg-red-900/30 border border-red-500/30 flex items-center justify-center">
              <AlertTriangle className="text-red-400" size={24} />
            </div>
          </div>
          
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-white mb-2">
              Si è verificato un errore
            </h3>
            
            <p className="text-sm text-gray-300 mb-4">
              {message || "L'applicazione ha riscontrato un problema imprevisto. Puoi provare a ricaricare la pagina o tornare alla home."}
            </p>

            {error && (
              <div className="mb-4">
                <button
                  onClick={() => setShowDetails(!showDetails)}
                  className="inline-flex items-center gap-1 text-xs text-blue-400 hover:text-blue-300"
                >
                  {showDetails ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
                  {showDetails ? 'Nascondi' : 'Mostra'} dettagli
                </button>
                
                {showDetails && (
                  <div className="mt-2 p-3 bg-black/30 rounded text-xs">
                    <code className="text-red-400 block mb-1">
                      {error.message}
                    </code>
                    {error.stack && (
                      <pre className="text-gray-500 text-[10px] overflow-x-auto mt-2">
                        {error.stack}
                      </pre>
                    )}
                  </div>
                )}
              </div>
            )}

            <div className="flex gap-3">
              {(resetError || onRetry) && (
                <button
                  onClick={resetError || onRetry}
                  className="inline-flex items-center gap-1.5 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors"
                >
                  <RefreshCw size={16} />
                  Riprova
                </button>
              )}
              
              {showHomeButton && (
                <button
                  onClick={handleGoHome}
                  className="inline-flex items-center gap-1.5 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white text-sm rounded-lg transition-colors"
                >
                  <Home size={16} />
                  Home
                </button>
              )}
            </div>
          </div>
        </div>
      </Card.Body>
    </Card>
  );
}

// Pre-configured error fallbacks for common scenarios
ErrorFallback.Page = function PageErrorFallback(props: Omit<ErrorFallbackProps, 'variant'>) {
  return <ErrorFallback variant="fullscreen" {...props} />;
};

ErrorFallback.Section = function SectionErrorFallback(props: Omit<ErrorFallbackProps, 'variant'>) {
  return <ErrorFallback variant="detailed" {...props} />;
};

ErrorFallback.Component = function ComponentErrorFallback(props: Omit<ErrorFallbackProps, 'variant'>) {
  return <ErrorFallback variant="minimal" {...props} />;
};
