'use client';

import { useAuthContext } from '@/lib/providers/AuthProvider';
import { ReactNode } from 'react';

interface AuthStateLoaderProps {
  children: ReactNode;
}

export function AuthStateLoader({ children }: AuthStateLoaderProps) {
  const { loading } = useAuthContext();

  if (loading) {
    // Ritorna null durante il caricamento per evitare il "glitch"
    return null;
  }

  return <>{children}</>;
} 