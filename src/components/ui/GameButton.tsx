import React from 'react';
import { cn } from '@/lib/utils';

interface GameButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger' | 'success';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  rounded?: boolean;
  children: React.ReactNode;
}

/**
 * GameButton component for game controls like life counters
 * Provides consistent styling for game-related buttons
 */
export function GameButton({
  variant = 'primary',
  size = 'md',
  rounded = false,
  className,
  children,
  ...props
}: GameButtonProps) {
  const variants = {
    primary: 'bg-blue-900/70 hover:bg-blue-800 text-white',
    secondary: 'bg-gray-900/70 hover:bg-gray-800 text-white',
    danger: 'bg-red-900/70 hover:bg-red-800 text-white',
    success: 'bg-green-900/70 hover:bg-green-800 text-white'
  };

  const sizes = {
    sm: rounded ? 'w-12 h-12 text-xl' : 'px-3 py-1.5 text-sm',
    md: rounded ? 'w-16 h-16 text-3xl' : 'px-4 py-2 text-base',
    lg: rounded ? 'w-20 h-20 text-4xl' : 'px-6 py-3 text-lg',
    xl: rounded ? 'w-24 h-24 text-5xl' : 'px-8 py-4 text-xl'
  };

  return (
    <button
      className={cn(
        variants[variant],
        sizes[size],
        'shadow-lg transform transition-all duration-200',
        'hover:scale-105 active:scale-95',
        'disabled:opacity-50 disabled:cursor-not-allowed',
        'focus:outline-none focus:ring-2 focus:ring-blue-500/50',
        rounded ? 'rounded-full' : 'rounded-md',
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
}

// Compound components for common use cases
GameButton.Plus = function PlusButton(props: Omit<GameButtonProps, 'children'>) {
  return <GameButton {...props}>+</GameButton>;
};

GameButton.Minus = function MinusButton(props: Omit<GameButtonProps, 'children'>) {
  return <GameButton {...props}>-</GameButton>;
};

GameButton.Reset = function ResetButton(props: Omit<GameButtonProps, 'children'>) {
  return <GameButton variant="secondary" {...props}>Reset</GameButton>;
};
