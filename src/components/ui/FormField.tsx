import React from 'react';
import { cn } from '@/lib/utils';

interface FormFieldProps {
  label?: string;
  error?: string | boolean;
  required?: boolean;
  hint?: string;
  className?: string;
  children: React.ReactNode;
}

/**
 * FormField wrapper component for consistent form field styling
 * Handles labels, errors, hints, and required indicators
 */
export function FormField({
  label,
  error,
  required,
  hint,
  className,
  children
}: FormFieldProps) {
  const errorMessage = typeof error === 'string' ? error : error ? 'Campo obbligatorio' : undefined;

  return (
    <div className={cn('space-y-1', className)}>
      {label && (
        <label className="block text-sm font-medium text-white">
          {label}
          {required && <span className="text-red-400 ml-1">*</span>}
        </label>
      )}
      
      {children}
      
      {hint && !errorMessage && (
        <p className="text-xs text-blue-300 mt-1">{hint}</p>
      )}
      
      {errorMessage && (
        <p className="text-xs text-red-400 mt-1">{errorMessage}</p>
      )}
    </div>
  );
}

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean;
}

/**
 * Styled input component that works well with FormField
 */
export function Input({ error, className, ...props }: InputProps) {
  return (
    <input
      className={cn(
        'w-full px-3 py-2 bg-black/30',
        'border rounded-lg text-sm',
        'focus:outline-none focus:ring-2',
        'transition-colors duration-200',
        'placeholder:text-gray-400',
        error 
          ? 'border-red-500 focus:ring-red-500/50' 
          : 'border-blue-500/30 focus:ring-blue-500/50',
        className
      )}
      {...props}
    />
  );
}

interface TextAreaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  error?: boolean;
}

/**
 * Styled textarea component that works well with FormField
 */
export function TextArea({ error, className, ...props }: TextAreaProps) {
  return (
    <textarea
      className={cn(
        'w-full px-3 py-2 bg-black/30',
        'border rounded-lg text-sm',
        'focus:outline-none focus:ring-2',
        'transition-colors duration-200',
        'placeholder:text-gray-400',
        'resize-y min-h-[100px]',
        error 
          ? 'border-red-500 focus:ring-red-500/50' 
          : 'border-blue-500/30 focus:ring-blue-500/50',
        className
      )}
      {...props}
    />
  );
}

interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  error?: boolean;
}

/**
 * Styled select component that works well with FormField
 */
export function Select({ error, className, children, ...props }: SelectProps) {
  return (
    <select
      className={cn(
        'w-full px-3 py-2 bg-black/30',
        'border rounded-lg text-sm',
        'focus:outline-none focus:ring-2',
        'transition-colors duration-200',
        'appearance-none cursor-pointer',
        error 
          ? 'border-red-500 focus:ring-red-500/50' 
          : 'border-blue-500/30 focus:ring-blue-500/50',
        className
      )}
      {...props}
    >
      {children}
    </select>
  );
}
