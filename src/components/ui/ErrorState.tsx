import React from 'react';
import { AlertCircle, XCircle, WifiOff, ServerCrash, ShieldAlert } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ErrorStateProps {
  type?: 'error' | 'warning' | 'network' | 'server' | 'permission';
  title?: string;
  message?: string;
  error?: Error | unknown;
  onRetry?: () => void;
  showDetails?: boolean;
  className?: string;
}

/**
 * Reusable ErrorState component for consistent error displays
 */
export function ErrorState({
  type = 'error',
  title,
  message,
  error,
  onRetry,
  showDetails = false,
  className
}: ErrorStateProps) {
  const [detailsOpen, setDetailsOpen] = React.useState(false);

  const types = {
    error: {
      icon: XCircle,
      color: 'text-red-400',
      bgColor: 'bg-red-900/20',
      borderColor: 'border-red-500/30',
      defaultTitle: 'Si è verificato un errore',
      defaultMessage: 'Qualcosa è andato storto. R<PERSON>rova più tardi.'
    },
    warning: {
      icon: AlertCircle,
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-900/20',
      borderColor: 'border-yellow-500/30',
      defaultTitle: 'Attenzione',
      defaultMessage: 'Si è verificato un problema che richiede la tua attenzione.'
    },
    network: {
      icon: WifiOff,
      color: 'text-blue-400',
      bgColor: 'bg-blue-900/20',
      borderColor: 'border-blue-500/30',
      defaultTitle: 'Errore di connessione',
      defaultMessage: 'Impossibile connettersi al server. Controlla la tua connessione internet.'
    },
    server: {
      icon: ServerCrash,
      color: 'text-purple-400',
      bgColor: 'bg-purple-900/20',
      borderColor: 'border-purple-500/30',
      defaultTitle: 'Errore del server',
      defaultMessage: 'Il server non risponde. Riprova tra qualche momento.'
    },
    permission: {
      icon: ShieldAlert,
      color: 'text-orange-400',
      bgColor: 'bg-orange-900/20',
      borderColor: 'border-orange-500/30',
      defaultTitle: 'Accesso negato',
      defaultMessage: 'Non hai i permessi necessari per questa operazione.'
    }
  };

  const config = types[type];
  const Icon = config.icon;

  const errorMessage = error instanceof Error ? error.message : String(error || '');
  const errorStack = error instanceof Error ? error.stack : undefined;

  return (
    <div className={cn(
      'rounded-lg p-4',
      config.bgColor,
      'border',
      config.borderColor,
      className
    )}>
      <div className="flex items-start gap-3">
        <Icon className={cn('flex-shrink-0 mt-0.5', config.color)} size={20} />
        
        <div className="flex-1 space-y-2">
          <h3 className="font-semibold text-white">
            {title || config.defaultTitle}
          </h3>
          
          <p className="text-sm text-gray-300">
            {message || config.defaultMessage}
          </p>

          {error && showDetails ? (
            <div className="mt-3">
              <button
                onClick={() => setDetailsOpen(!detailsOpen)}
                className="text-xs text-blue-400 hover:text-blue-300 transition-colors"
              >
                {detailsOpen ? 'Nascondi dettagli' : 'Mostra dettagli'}
              </button>
              
              {detailsOpen && (
                <div className="mt-2 p-2 bg-black/30 rounded text-xs font-mono overflow-x-auto">
                  <div className="text-red-300">{errorMessage}</div>
                  {errorStack && (
                    <pre className="mt-2 text-gray-400 whitespace-pre-wrap">
                      {errorStack}
                    </pre>
                  )}
                </div>
              )}
            </div>
          ) : null}

          {onRetry && (
            <button
              onClick={onRetry}
              className="mt-3 px-3 py-1.5 bg-blue-600 hover:bg-blue-700 rounded text-sm text-white transition-colors"
            >
              Riprova
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

// Pre-configured error states for common scenarios
ErrorState.Network = function NetworkError(props: Omit<ErrorStateProps, 'type'>) {
  return <ErrorState type="network" {...props} />;
};

ErrorState.Server = function ServerError(props: Omit<ErrorStateProps, 'type'>) {
  return <ErrorState type="server" {...props} />;
};

ErrorState.Permission = function PermissionError(props: Omit<ErrorStateProps, 'type'>) {
  return <ErrorState type="permission" {...props} />;
};

ErrorState.NotFound = function NotFoundError(props: Omit<ErrorStateProps, 'type' | 'title' | 'message'>) {
  return (
    <ErrorState
      type="error"
      title="Non trovato"
      message="La risorsa che stai cercando non esiste o è stata rimossa."
      {...props}
    />
  );
};

// Error boundary component for catching React errors
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ReactNode },
  ErrorBoundaryState
> {
  constructor(props: { children: React.ReactNode; fallback?: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        this.props.fallback || (
          <ErrorState
            type="error"
            title="Errore dell'applicazione"
            message="Si è verificato un errore imprevisto nell'applicazione."
            error={this.state.error}
            showDetails={true}
            onRetry={() => this.setState({ hasError: false, error: undefined })}
          />
        )
      );
    }

    return this.props.children;
  }
}
