import Image, { ImageProps } from 'next/image';
import { useState, memo } from 'react';
import { cn } from '@/lib/utils';

interface OptimizedImageProps extends Omit<ImageProps, 'onLoad' | 'onError'> {
  fallbackSrc?: string;
  showLoader?: boolean;
  blur?: boolean;
}

/**
 * Optimized image component with automatic loading states and error handling
 * Wraps Next.js Image component with common optimizations
 */
export const OptimizedImage = memo(function OptimizedImage({
  src,
  alt,
  className,
  fallbackSrc = '/placeholder.png',
  showLoader = true,
  blur = true,
  priority = false,
  quality = 75,
  ...props
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleLoad = () => {
    setIsLoading(false);
  };

  const handleError = () => {
    setHasError(true);
    setIsLoading(false);
  };

  // Use fallback image if error occurred
  const imageSrc = hasError ? fallbackSrc : src;

  return (
    <div className={cn('relative', className)}>
      {showLoader && isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-blue-900/20 animate-pulse rounded">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
        </div>
      )}
      <Image
        src={imageSrc}
        alt={alt}
        className={cn(
          'transition-opacity duration-300',
          isLoading ? 'opacity-0' : 'opacity-100',
          className
        )}
        onLoad={handleLoad}
        onError={handleError}
        quality={quality}
        priority={priority}
        placeholder={blur ? 'blur' : 'empty'}
        {...props}
      />
    </div>
  );
});
