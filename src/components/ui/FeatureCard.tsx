import { memo } from 'react';

interface FeatureCardProps {
  title: string;
  description: string;
  icon: string;
}

export const FeatureCard = memo(function FeatureCard({ title, description, icon }: FeatureCardProps) {
  return (
    <div className="bg-black/20 backdrop-blur-sm p-4 sm:p-6 rounded-xl border border-blue-500/30 hover:border-blue-400/50 transition-all hover:translate-y-[-2px]">
      <div className="text-2xl sm:text-3xl mb-2 sm:mb-3">{icon}</div>
      <h3 className="text-lg sm:text-xl font-bold mb-1 sm:mb-2">{title}</h3>
      <p className="text-blue-200/80 text-xs sm:text-sm">{description}</p>
    </div>
  );
});
