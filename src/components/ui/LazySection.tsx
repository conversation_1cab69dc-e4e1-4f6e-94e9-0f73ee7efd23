import { memo, ReactNode } from 'react';
import { useLazyLoad } from '@/hooks/useIntersectionObserver';
import { cn } from '@/lib/utils';

interface LazySectionProps {
  children: ReactNode;
  className?: string;
  placeholder?: ReactNode;
  rootMargin?: string;
  threshold?: number;
}

/**
 * Lazy load sections of the page as they come into viewport
 * Improves initial page load performance
 */
export const LazySection = memo(function LazySection({
  children,
  className,
  placeholder,
  rootMargin = '100px',
  threshold = 0.1
}: LazySectionProps) {
  const { ref, shouldLoad } = useLazyLoad({
    rootMargin,
    threshold
  });

  return (
    <div ref={ref} className={cn('min-h-[100px]', className)}>
      {shouldLoad ? (
        children
      ) : (
        placeholder || (
          <div className="flex items-center justify-center py-8">
            <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
          </div>
        )
      )}
    </div>
  );
});
