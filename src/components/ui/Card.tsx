import React from 'react';
import { cn } from '@/lib/utils';

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'bordered' | 'gradient' | 'solid';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  hover?: boolean;
  children: React.ReactNode;
}

/**
 * Reusable Card component for consistent card styling
 */
export function Card({
  variant = 'default',
  padding = 'md',
  hover = false,
  className,
  children,
  ...props
}: CardProps) {
  const variants = {
    default: 'bg-black/20 backdrop-blur-sm border border-blue-500/30',
    bordered: 'bg-transparent border-2 border-blue-500/50',
    gradient: 'bg-gradient-to-br from-blue-900/30 to-sky-900/30 border border-blue-500/20',
    solid: 'bg-blue-900/50 border border-blue-600/30'
  };

  const paddings = {
    none: '',
    sm: 'p-3',
    md: 'p-4 sm:p-6',
    lg: 'p-6 sm:p-8'
  };

  return (
    <div
      className={cn(
        variants[variant],
        paddings[padding],
        'rounded-xl',
        'transition-all duration-200',
        hover && 'hover:shadow-lg hover:shadow-blue-500/20 hover:border-blue-400/50',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

// Card sub-components for better composition
Card.Header = function CardHeader({
  className,
  children,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div 
      className={cn(
        'pb-4 mb-4 border-b border-blue-500/20',
        className
      )} 
      {...props}
    >
      {children}
    </div>
  );
};

Card.Title = function CardTitle({
  className,
  children,
  ...props
}: React.HTMLAttributes<HTMLHeadingElement>) {
  return (
    <h3 
      className={cn(
        'text-lg sm:text-xl font-bold text-white',
        className
      )} 
      {...props}
    >
      {children}
    </h3>
  );
};

Card.Body = function CardBody({
  className,
  children,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div className={cn('text-blue-100', className)} {...props}>
      {children}
    </div>
  );
};

Card.Footer = function CardFooter({
  className,
  children,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div 
      className={cn(
        'pt-4 mt-4 border-t border-blue-500/20',
        className
      )} 
      {...props}
    >
      {children}
    </div>
  );
};
