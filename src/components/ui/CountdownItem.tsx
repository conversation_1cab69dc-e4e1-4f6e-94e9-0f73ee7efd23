import { memo } from 'react';

interface CountdownItemProps {
  value: string;
  label: string;
}

export const CountdownItem = memo(function CountdownItem({ value, label }: CountdownItemProps) {
  return (
    <div className="flex flex-col items-center">
      <div className="bg-black/40 w-full py-2 sm:py-3 rounded-lg text-xl sm:text-2xl font-bold mb-1">
        {value}
      </div>
      <span className="text-[10px] sm:text-xs text-blue-300/70">{label}</span>
    </div>
  );
});
