import React from 'react';
import { ChevronDown } from 'lucide-react';

interface SelectProps {
  value: string;
  onChange: (value: string) => void;
  options: Array<{ value: string; label: string; description?: string }>;
  placeholder?: string;
  className?: string;
  hasError?: boolean;
  disabled?: boolean;
  allowEmpty?: boolean; // Permette di selezionare un valore vuoto
  emptyLabel?: string; // Etichetta per l'opzione vuota
}

export function Select({
  value,
  onChange,
  options,
  placeholder = "Seleziona un'opzione",
  className = "",
  hasError = false,
  disabled = false,
  allowEmpty = false,
  emptyLabel = "Nessuna selezione"
}: SelectProps) {
  return (
    <div className={`relative ${className}`}>
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className={`
          w-full px-3 py-2 bg-black/30 border rounded-lg text-sm
          focus:outline-none focus:ring-2 focus:ring-blue-500/50
          appearance-none cursor-pointer
          ${hasError ? 'border-red-500' : 'border-blue-500/30'}
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}
        disabled={disabled}
      >
        {!value && <option value="" disabled>{placeholder}</option>}
        {allowEmpty && (
          <option value="" className="bg-gray-800 text-white">
            {emptyLabel}
          </option>
        )}
        {options.map((option) => (
          <option
            key={option.value}
            value={option.value}
            className="bg-gray-800 text-white"
          >
            {option.label}
          </option>
        ))}
      </select>
      <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
        <ChevronDown 
          size={16} 
          className={`text-blue-300 ${disabled ? 'opacity-50' : ''}`} 
        />
      </div>
    </div>
  );
}
