import React from 'react';
import { cn } from '@/lib/utils';

interface LoadingStateProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  fullScreen?: boolean;
  overlay?: boolean;
  className?: string;
}

/**
 * Reusable LoadingState component for consistent loading indicators
 */
export function LoadingState({
  size = 'md',
  text,
  fullScreen = false,
  overlay = false,
  className
}: LoadingStateProps) {
  const sizes = {
    sm: 'h-32',
    md: 'h-48',
    lg: 'h-64'
  };

  const spinnerSizes = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  const content = (
    <div className="flex flex-col items-center justify-center gap-3">
      <div className={cn(
        'animate-spin rounded-full border-t-2 border-b-2 border-blue-500',
        spinnerSizes[size]
      )} />
      {text && (
        <p className="text-sm text-blue-300 animate-pulse">{text}</p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className={cn(
        'fixed inset-0 z-50 flex items-center justify-center',
        'bg-black/80 backdrop-blur-sm',
        className
      )}>
        {content}
      </div>
    );
  }

  if (overlay) {
    return (
      <div className={cn(
        'absolute inset-0 z-10 flex items-center justify-center',
        'bg-black/50 backdrop-blur-sm rounded-lg',
        className
      )}>
        {content}
      </div>
    );
  }

  return (
    <div className={cn(
      'flex items-center justify-center',
      !fullScreen && !overlay && sizes[size],
      className
    )}>
      {content}
    </div>
  );
}

// Skeleton loading component for content placeholders
interface SkeletonProps {
  className?: string;
  variant?: 'text' | 'circular' | 'rectangular';
  animation?: 'pulse' | 'wave' | 'none';
}

export function Skeleton({
  className,
  variant = 'text',
  animation = 'pulse'
}: SkeletonProps) {
  const variants = {
    text: 'h-4 rounded',
    circular: 'rounded-full',
    rectangular: 'rounded-lg'
  };

  const animations = {
    pulse: 'animate-pulse',
    wave: 'animate-shimmer',
    none: ''
  };

  return (
    <div
      className={cn(
        'bg-blue-900/30',
        variants[variant],
        animations[animation],
        variant === 'circular' && 'aspect-square',
        className
      )}
    />
  );
}

// Compound component for common skeleton layouts
Skeleton.Card = function SkeletonCard({ className }: { className?: string }) {
  return (
    <div className={cn('space-y-3', className)}>
      <Skeleton className="h-6 w-3/4" />
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-4 w-2/3" />
    </div>
  );
};

Skeleton.List = function SkeletonList({ 
  items = 3,
  className 
}: { 
  items?: number;
  className?: string;
}) {
  return (
    <div className={cn('space-y-3', className)}>
      {Array.from({ length: items }).map((_, i) => (
        <div key={i} className="flex items-center gap-3">
          <Skeleton variant="circular" className="w-10 h-10" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-1/3" />
            <Skeleton className="h-3 w-2/3" />
          </div>
        </div>
      ))}
    </div>
  );
};
