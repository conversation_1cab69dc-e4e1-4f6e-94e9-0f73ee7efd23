import React from 'react';
import { LucideIcon, Inbox, Calendar, Users, Trophy, Search, FileX } from 'lucide-react';
import { cn } from '@/lib/utils';

interface EmptyStateProps {
  icon?: LucideIcon;
  iconType?: 'inbox' | 'calendar' | 'users' | 'trophy' | 'search' | 'file';
  title: string;
  description?: string;
  action?: React.ReactNode;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * Reusable EmptyState component for consistent empty state displays
 */
export function EmptyState({
  icon: CustomIcon,
  iconType = 'inbox',
  title,
  description,
  action,
  size = 'md',
  className
}: EmptyStateProps) {
  const icons = {
    inbox: Inbox,
    calendar: Calendar,
    users: Users,
    trophy: Trophy,
    search: Search,
    file: FileX
  };

  const Icon = CustomIcon || icons[iconType];

  const sizes = {
    sm: {
      container: 'py-8',
      icon: 'w-12 h-12',
      title: 'text-base',
      description: 'text-sm'
    },
    md: {
      container: 'py-12',
      icon: 'w-16 h-16',
      title: 'text-lg',
      description: 'text-base'
    },
    lg: {
      container: 'py-16',
      icon: 'w-20 h-20',
      title: 'text-xl',
      description: 'text-lg'
    }
  };

  const currentSize = sizes[size];

  return (
    <div className={cn(
      'flex flex-col items-center justify-center text-center',
      currentSize.container,
      className
    )}>
      <div className={cn(
        'rounded-full bg-blue-900/30 p-4 mb-4',
        'border border-blue-500/20'
      )}>
        <Icon className={cn(
          'text-blue-400',
          currentSize.icon
        )} />
      </div>

      <h3 className={cn(
        'font-semibold text-white mb-2',
        currentSize.title
      )}>
        {title}
      </h3>

      {description && (
        <p className={cn(
          'text-blue-300 max-w-md mb-6',
          currentSize.description
        )}>
          {description}
        </p>
      )}

      {action && (
        <div className="mt-4">
          {action}
        </div>
      )}
    </div>
  );
}

// Pre-configured empty states for common scenarios
EmptyState.NoResults = function NoResultsEmptyState({
  searchTerm,
  onClear,
  ...props
}: Omit<EmptyStateProps, 'icon' | 'iconType' | 'title'> & {
  searchTerm?: string;
  onClear?: () => void;
}) {
  return (
    <EmptyState
      iconType="search"
      title="Nessun risultato trovato"
      description={
        searchTerm 
          ? `Nessun risultato per "${searchTerm}". Prova con termini diversi.`
          : "Prova a modificare i criteri di ricerca."
      }
      action={
        onClear && (
          <button
            onClick={onClear}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-white transition-colors"
          >
            Cancella ricerca
          </button>
        )
      }
      {...props}
    />
  );
};

EmptyState.NoEvents = function NoEventsEmptyState(props: Omit<EmptyStateProps, 'icon' | 'iconType' | 'title'>) {
  return (
    <EmptyState
      iconType="calendar"
      title="Nessun evento programmato"
      description="Non ci sono eventi in programma per questa data."
      {...props}
    />
  );
};

EmptyState.NoData = function NoDataEmptyState(props: Omit<EmptyStateProps, 'icon' | 'iconType' | 'title'>) {
  return (
    <EmptyState
      iconType="inbox"
      title="Nessun dato disponibile"
      description="Non ci sono ancora dati da visualizzare."
      {...props}
    />
  );
};
