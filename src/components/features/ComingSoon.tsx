import { Countdown } from './Countdown';

export function ComingSoon() {
  return (
    <div className="bg-black/20 backdrop-blur-sm p-4 sm:p-8 rounded-xl border border-blue-500/30">
      <h3 className="text-2xl sm:text-3xl font-bold mb-3 sm:mb-4">In Arrivo</h3>
      <p className="text-blue-200 mb-6 sm:mb-8 text-sm sm:text-base">
        Stiamo lavorando per creare la migliore esperienza per i giocatori di Pauper.
        Presto potrai registrarti ai tornei, gestire il tuo profilo e seguire i risultati della lega.
      </p>
      
      <Countdown />
      
      {/* Newsletter Signup */}
      <div className="max-w-md mx-auto">
        <h4 className="text-xs sm:text-sm font-medium mb-2">Ricevi aggiornamenti sul lancio</h4>
        <div className="flex gap-2">
          <input 
            type="email" 
            placeholder="La tua email" 
            className="flex-1 px-3 sm:px-4 py-2 rounded-lg bg-black/30 border border-blue-500/30 focus:outline-none focus:ring-2 focus:ring-blue-500/50 text-sm"
          />
          <button className="px-3 sm:px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg font-medium transition-colors text-sm whitespace-nowrap">
            Iscriviti
          </button>
        </div>
      </div>
    </div>
  );
} 