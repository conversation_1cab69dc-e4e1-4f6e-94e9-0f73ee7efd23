"use client";

import { Suspense } from 'react';
import { useCountdown } from '@/hooks/useCountdown';
import { CountdownItem } from '@/components/ui/CountdownItem';

function CountdownContent() {
  const timeLeft = useCountdown();

  return (
    <div className="grid grid-cols-4 gap-2 sm:gap-4 max-w-md mx-auto mb-8">
      <CountdownItem value={timeLeft.days} label="<PERSON>iorni" />
      <CountdownItem value={timeLeft.hours} label="Ore" />
      <CountdownItem value={timeLeft.minutes} label="Minuti" />
      <CountdownItem value={timeLeft.seconds} label="Secondi" />
    </div>
  );
}

export function Countdown() {
  return (
    <Suspense fallback={
      <div className="grid grid-cols-4 gap-2 sm:gap-4 max-w-md mx-auto mb-8">
        <CountdownItem value="00" label="<PERSON>ior<PERSON>" />
        <CountdownItem value="00" label="Ore" />
        <CountdownItem value="00" label="Minuti" />
        <CountdownItem value="00" label="Secondi" />
      </div>
    }>
      <CountdownContent />
    </Suspense>
  );
} 