import Link from 'next/link';
import { <PERSON><PERSON><PERSON>, <PERSON>rk<PERSON>, <PERSON>, Users } from 'lucide-react';

export function CTASection() {
  return (
    <section className="py-20 sm:py-32 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-950/50 via-sky-900/30 to-blue-900/50"></div>
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-sky-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-blue-500/5 to-sky-500/5 rounded-full blur-3xl"></div>
      </div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          {/* Main CTA */}
          <div className="bg-gradient-to-br from-black/40 to-black/20 backdrop-blur-lg p-12 sm:p-16 rounded-3xl border border-blue-500/30 shadow-2xl">
            {/* Icon */}
            <div className="flex justify-center mb-8">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-sky-500 rounded-full blur-xl opacity-50 animate-pulse"></div>
                <div className="relative bg-gradient-to-r from-blue-600 to-sky-600 p-4 rounded-full">
                  <Sparkles className="w-8 h-8 text-white" />
                </div>
              </div>
            </div>

            {/* Heading */}
            <h2 className="text-4xl sm:text-6xl font-extrabold mb-6">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-300 via-sky-300 to-blue-400">
                Pronto per la sfida?
              </span>
            </h2>
            
            <p className="text-xl sm:text-2xl text-blue-200 mb-12 max-w-4xl mx-auto leading-relaxed">
              Unisciti alla community più attiva del Pauper italiano. 
              Compete, migliora, e diventa una leggenda del formato più amato di Magic.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12">
              <Link 
                href="/login"
                className="group w-full sm:w-auto px-10 py-5 bg-gradient-to-r from-blue-600 to-sky-600 hover:from-blue-700 hover:to-sky-700 rounded-2xl font-bold text-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-2xl"
              >
                <span className="flex items-center justify-center gap-3">
                  Inizia la Tua Avventura
                  <ArrowRight className="w-6 h-6 transition-transform group-hover:translate-x-1" />
                </span>
              </Link>
              
              <Link 
                href="/calendar"
                className="w-full sm:w-auto px-10 py-5 bg-black/30 backdrop-blur-sm border-2 border-blue-500/50 hover:bg-black/40 hover:border-blue-400 rounded-2xl font-bold text-xl transition-all duration-300 text-blue-100 hover:text-white"
              >
                Esplora i Tornei
              </Link>
            </div>

            {/* Features Highlight */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <div className="bg-black/20 backdrop-blur-sm p-6 rounded-xl border border-blue-500/20">
                <Trophy className="w-8 h-8 text-yellow-400 mx-auto mb-3" />
                <h3 className="font-bold text-white mb-2">Compete</h3>
                <p className="text-blue-200 text-sm">
                  Partecipa a tornei settimanali e eventi speciali
                </p>
              </div>
              
              <div className="bg-black/20 backdrop-blur-sm p-6 rounded-xl border border-blue-500/20">
                <Users className="w-8 h-8 text-blue-400 mx-auto mb-3" />
                <h3 className="font-bold text-white mb-2">Connettiti</h3>
                <p className="text-blue-200 text-sm">
                  Fai parte di una community appassionata
                </p>
              </div>
              
              <div className="bg-black/20 backdrop-blur-sm p-6 rounded-xl border border-blue-500/20">
                <Sparkles className="w-8 h-8 text-purple-400 mx-auto mb-3" />
                <h3 className="font-bold text-white mb-2">Migliora</h3>
                <p className="text-blue-200 text-sm">
                  Analizza le tue performance e cresci
                </p>
              </div>
            </div>
          </div>

          {/* Secondary Info */}
          <div className="mt-16 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-black/20 backdrop-blur-sm p-6 rounded-xl border border-blue-500/30">
              <div className="text-3xl font-bold text-white mb-2">Gratuito</div>
              <div className="text-blue-200">Registrazione sempre gratuita</div>
            </div>
            
            <div className="bg-black/20 backdrop-blur-sm p-6 rounded-xl border border-blue-500/30">
              <div className="text-3xl font-bold text-white mb-2">24/7</div>
              <div className="text-blue-200">Piattaforma sempre disponibile</div>
            </div>
            
            <div className="bg-black/20 backdrop-blur-sm p-6 rounded-xl border border-blue-500/30">
              <div className="text-3xl font-bold text-white mb-2">Fair Play</div>
              <div className="text-blue-200">Ambiente di gioco equo</div>
            </div>
            
            <div className="bg-black/20 backdrop-blur-sm p-6 rounded-xl border border-blue-500/30">
              <div className="text-3xl font-bold text-white mb-2">Premi</div>
              <div className="text-blue-200">Riconoscimenti per i migliori</div>
            </div>
          </div>

          {/* Final Message */}
          <div className="mt-16">
            <p className="text-lg text-blue-300 max-w-2xl mx-auto">
              La Lega Pauper Adriatica ti aspetta. Ogni grande giocatore ha iniziato con il primo torneo.
              <br />
              <span className="text-white font-semibold">Il tuo momento è adesso.</span>
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
