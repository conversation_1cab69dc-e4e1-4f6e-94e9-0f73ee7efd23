"use client";

import Link from 'next/link';
import { Calendar, MapPin, Users, Trophy, Clock, ArrowRight, Loader2 } from 'lucide-react';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';
import { useUpcomingTournaments } from '@/lib/hooks/useUpcomingTournaments';
import type { Tournament } from '@/types/calendar';

function EventCard({ event }: { event: Tournament }) {
  const eventDate = new Date(event.date);
  const currentPlayers = event.tournament_registrations?.[0]?.count || 0;
  const maxPlayers = event.max_players;
  const isAlmostFull = (currentPlayers / maxPlayers) > 0.8;
  const isFull = currentPlayers >= maxPlayers;

  return (
    <div className="group bg-black/20 backdrop-blur-sm p-6 rounded-2xl border border-blue-500/30 hover:border-blue-400/50 transition-all duration-300 hover:transform hover:scale-105">
      {/* Header */}
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-xl font-bold text-white group-hover:text-blue-100 transition-colors">
            {event.title}
          </h3>
          <div className="flex items-center gap-2 text-blue-300 mt-1">
            <Calendar className="w-4 h-4" />
            <span className="text-sm">
              {format(eventDate, "EEEE d MMMM yyyy", { locale: it })}
            </span>
          </div>
        </div>
        <div className={`px-3 py-1 rounded-full text-xs font-medium ${
          isFull 
            ? 'bg-red-500/20 text-red-300 border border-red-500/30' 
            : isAlmostFull 
              ? 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30'
              : 'bg-green-500/20 text-green-300 border border-green-500/30'
        }`}>
          {isFull ? 'Completo' : isAlmostFull ? 'Quasi Pieno' : 'Aperto'}
        </div>
      </div>

      {/* Event Details */}
      <div className="space-y-3 mb-6">
        <div className="flex items-center gap-3 text-blue-200">
          <Clock className="w-4 h-4 text-blue-400" />
          <span className="text-sm">
            {format(new Date(`2000-01-01T${event.time_start}`), "HH:mm")}
          </span>
        </div>

        <div className="flex items-center gap-3 text-blue-200">
          <MapPin className="w-4 h-4 text-blue-400" />
          <span className="text-sm">
            {event.store?.name}, {event.store?.city}
          </span>
        </div>

        <div className="flex items-center gap-3 text-blue-200">
          <Users className="w-4 h-4 text-blue-400" />
          <span className="text-sm">
            {currentPlayers}/{maxPlayers} giocatori
          </span>
          <div className="flex-1 bg-blue-950/50 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${
                isFull
                  ? 'bg-red-500'
                  : isAlmostFull
                    ? 'bg-yellow-500'
                    : 'bg-green-500'
              }`}
              style={{ width: `${(currentPlayers / maxPlayers) * 100}%` }}
            ></div>
          </div>
        </div>

        <div className="flex items-center gap-3 text-blue-200">
          <Trophy className="w-4 h-4 text-yellow-400" />
          <span className="text-sm">
            Prize Pool: {event.prize_pool || `€${event.price ? Number(event.price) * 20 : 100}`}
          </span>
        </div>
      </div>

      {/* Format Badge */}
      <div className="mb-4">
        <span className="inline-block px-3 py-1 bg-blue-600/20 text-blue-300 text-xs font-medium rounded-full border border-blue-500/30">
          {event.format || 'Pauper'}
        </span>
      </div>

      {/* Action Button */}
      <Link 
        href={`/calendar`}
        className={`w-full inline-flex items-center justify-center gap-2 px-4 py-3 rounded-xl font-medium transition-all duration-300 ${
          isFull
            ? 'bg-gray-600/20 text-gray-400 cursor-not-allowed border border-gray-500/30'
            : 'bg-gradient-to-r from-blue-600 to-sky-600 hover:from-blue-700 hover:to-sky-700 text-white transform hover:scale-105'
        }`}
      >
        {isFull ? 'Evento Completo' : 'Registrati Ora'}
        {!isFull && <ArrowRight className="w-4 h-4" />}
      </Link>
    </div>
  );
}

export function UpcomingEventsSection() {
  const { data: upcomingEvents, isLoading, error } = useUpcomingTournaments(3);

  return (
    <section className="py-20 sm:py-32 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-5xl font-bold mb-6">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-300 to-sky-300">
              Prossimi Eventi
            </span>
          </h2>
          <p className="text-xl text-blue-200 max-w-3xl mx-auto">
            Non perdere i prossimi tornei! Registrati in anticipo per assicurarti il posto
            e competere per fantastici premi.
          </p>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-blue-400" />
            <span className="ml-3 text-blue-200">Caricamento eventi...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-center py-12">
            <p className="text-red-400 mb-4">Errore nel caricamento degli eventi</p>
            <Link
              href="/calendar"
              className="inline-flex items-center gap-2 px-6 py-3 bg-blue-600/20 text-blue-300 rounded-xl hover:bg-blue-600/30 transition-colors"
            >
              Vai al Calendario
              <ArrowRight className="w-4 h-4" />
            </Link>
          </div>
        )}

        {/* Events Grid */}
        {!isLoading && !error && upcomingEvents && upcomingEvents.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {upcomingEvents.map((event) => (
              <EventCard key={event.id} event={event} />
            ))}
          </div>
        )}

        {/* No Events State */}
        {!isLoading && !error && (!upcomingEvents || upcomingEvents.length === 0) && (
          <div className="text-center py-12">
            <p className="text-blue-200 mb-4">Nessun evento programmato al momento</p>
            <Link
              href="/calendar"
              className="inline-flex items-center gap-2 px-6 py-3 bg-blue-600/20 text-blue-300 rounded-xl hover:bg-blue-600/30 transition-colors"
            >
              Controlla il Calendario
              <ArrowRight className="w-4 h-4" />
            </Link>
          </div>
        )}

        {/* View All Events CTA */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-blue-600/10 to-sky-600/10 backdrop-blur-sm p-8 rounded-2xl border border-blue-500/20">
            <h3 className="text-2xl font-bold text-white mb-4">
              Vuoi vedere tutti gli eventi?
            </h3>
            <p className="text-blue-200 mb-6">
              Consulta il calendario completo con tutti i tornei, eventi speciali e appuntamenti della lega.
            </p>
            <Link 
              href="/calendar"
              className="inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-blue-600 to-sky-600 hover:from-blue-700 hover:to-sky-700 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105"
            >
              Calendario Completo
              <Calendar className="w-5 h-5" />
            </Link>
          </div>
        </div>

        {/* Quick Info */}
        <div className="mt-16 grid grid-cols-1 sm:grid-cols-3 gap-6">
          <div className="text-center bg-black/20 backdrop-blur-sm p-6 rounded-xl border border-blue-500/30">
            <div className="text-2xl font-bold text-white mb-2">Ogni Settimana</div>
            <div className="text-blue-200">Tornei regolari</div>
          </div>
          <div className="text-center bg-black/20 backdrop-blur-sm p-6 rounded-xl border border-blue-500/30">
            <div className="text-2xl font-bold text-white mb-2">Ogni Mese</div>
            <div className="text-blue-200">Eventi speciali</div>
          </div>
          <div className="text-center bg-black/20 backdrop-blur-sm p-6 rounded-xl border border-blue-500/30">
            <div className="text-2xl font-bold text-white mb-2">Ogni Stagione</div>
            <div className="text-blue-200">Championship</div>
          </div>
        </div>
      </div>
    </section>
  );
}
