"use client";

import { useEffect, useState } from 'react';
import { TrendingUp, Users, Trophy, Calendar, Target, Zap } from 'lucide-react';

interface StatItem {
  icon: React.ElementType;
  value: string;
  label: string;
  description: string;
  color: string;
}

const stats: StatItem[] = [
  {
    icon: Users,
    value: "150+",
    label: "Giocatori Attivi",
    description: "Una community in crescita costante",
    color: "from-blue-500 to-sky-500"
  },
  {
    icon: Trophy,
    value: "50+",
    label: "Tornei Completati",
    description: "Eventi organizzati con successo",
    color: "from-yellow-500 to-orange-500"
  },
  {
    icon: Calendar,
    value: "12",
    label: "Eventi al Mese",
    description: "Calendario sempre ricco di appuntamenti",
    color: "from-green-500 to-emerald-500"
  },
  {
    icon: Target,
    value: "95%",
    label: "Soddisfazione",
    description: "Feedback positivo dai partecipanti",
    color: "from-purple-500 to-pink-500"
  },
  {
    icon: TrendingUp,
    value: "+25%",
    label: "Crescita Mensile",
    description: "Nuovi giocatori ogni mese",
    color: "from-red-500 to-rose-500"
  },
  {
    icon: Zap,
    value: "< 2s",
    label: "Tempo di Caricamento",
    description: "Piattaforma veloce e reattiva",
    color: "from-cyan-500 to-blue-500"
  }
];

function AnimatedCounter({ value, duration = 2000 }: { value: string; duration?: number }) {
  const [displayValue, setDisplayValue] = useState("0");
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    const element = document.getElementById(`stat-${value}`);
    if (element) {
      observer.observe(element);
    }

    return () => observer.disconnect();
  }, [value]);

  useEffect(() => {
    if (!isVisible) return;

    // Extract numeric part from value
    const numericMatch = value.match(/\d+/);
    if (!numericMatch) {
      setDisplayValue(value);
      return;
    }

    const targetNumber = parseInt(numericMatch[0]);
    const prefix = value.substring(0, value.indexOf(numericMatch[0]));
    const suffix = value.substring(value.indexOf(numericMatch[0]) + numericMatch[0].length);

    let current = 0;
    const increment = targetNumber / (duration / 50);
    
    const timer = setInterval(() => {
      current += increment;
      if (current >= targetNumber) {
        setDisplayValue(`${prefix}${targetNumber}${suffix}`);
        clearInterval(timer);
      } else {
        setDisplayValue(`${prefix}${Math.floor(current)}${suffix}`);
      }
    }, 50);

    return () => clearInterval(timer);
  }, [isVisible, value, duration]);

  return (
    <span id={`stat-${value}`} className="text-3xl sm:text-4xl font-bold text-white">
      {displayValue}
    </span>
  );
}

export function StatsSection() {
  return (
    <section className="py-20 sm:py-32 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-950/30 to-sky-950/30"></div>
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-5xl font-bold mb-6">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-300 to-sky-300">
              I numeri parlano chiaro
            </span>
          </h2>
          <p className="text-xl text-blue-200 max-w-3xl mx-auto">
            La Lega Pauper Adriatica è la community di riferimento per il formato Pauper. 
            Ecco alcuni dati che dimostrano la nostra crescita e il nostro impegno.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {stats.map((stat, index) => (
            <div 
              key={index}
              className="group relative bg-black/20 backdrop-blur-sm p-8 rounded-2xl border border-blue-500/30 hover:border-blue-400/50 transition-all duration-500 hover:transform hover:scale-105"
            >
              {/* Background Gradient */}
              <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-0 group-hover:opacity-10 rounded-2xl transition-opacity duration-500`}></div>
              
              {/* Icon */}
              <div className="relative mb-6">
                <div className={`inline-flex p-4 rounded-xl bg-gradient-to-br ${stat.color} shadow-lg group-hover:shadow-xl transition-shadow duration-300`}>
                  <stat.icon className="w-8 h-8 text-white" />
                </div>
              </div>

              {/* Content */}
              <div className="relative">
                <div className="mb-2">
                  <AnimatedCounter value={stat.value} />
                </div>
                <h3 className="text-lg font-semibold text-blue-100 mb-2">
                  {stat.label}
                </h3>
                <p className="text-blue-300 text-sm leading-relaxed">
                  {stat.description}
                </p>
              </div>

              {/* Hover Effect */}
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
            </div>
          ))}
        </div>

        {/* Bottom Section */}
        <div className="mt-20 text-center">
          <div className="bg-gradient-to-r from-blue-600/10 to-sky-600/10 backdrop-blur-sm p-8 sm:p-12 rounded-3xl border border-blue-500/20">
            <h3 className="text-2xl sm:text-3xl font-bold text-white mb-4">
              Fai parte della storia
            </h3>
            <p className="text-blue-200 text-lg mb-8 max-w-2xl mx-auto">
              Ogni torneo, ogni partita, ogni vittoria contribuisce a rendere la nostra community 
              sempre più forte e competitiva. Unisciti a noi e scrivi il tuo capitolo nella storia del Pauper Adriatico.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <div className="bg-black/30 backdrop-blur-sm px-6 py-3 rounded-xl border border-blue-500/30">
                <div className="text-2xl font-bold text-white">Prossimo Obiettivo</div>
                <div className="text-blue-200">200 Giocatori Attivi</div>
              </div>
              <div className="bg-black/30 backdrop-blur-sm px-6 py-3 rounded-xl border border-blue-500/30">
                <div className="text-2xl font-bold text-white">Meta Attuale</div>
                <div className="text-blue-200">Burn vs Control</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
