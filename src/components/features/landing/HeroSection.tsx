"use client";

import Image from 'next/image';
import Link from 'next/link';
import { Calendar, Trophy, Users, TrendingUp } from 'lucide-react';

export function HeroSection() {
  return (
    <section className="relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-950/50 via-sky-900/30 to-blue-900/50"></div>
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-sky-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 sm:py-32">
        <div className="text-center space-y-8">
          {/* Logo */}
          <div className="flex justify-center mb-8">
            <div className="relative">
              <div className="absolute inset-0 bg-blue-500/20 rounded-full blur-xl animate-pulse"></div>
              <div className="relative bg-black/20 backdrop-blur-sm p-6 rounded-full border border-blue-500/30">
                <Image 
                  src="/logo.png" 
                  alt="Logo Lega Pauper Adriatica" 
                  width={120} 
                  height={120}
                  className="w-24 h-24 sm:w-32 sm:h-32"
                  priority
                />
              </div>
            </div>
          </div>

          {/* Main Heading */}
          <div className="space-y-4">
            <h1 className="text-4xl sm:text-6xl lg:text-7xl font-extrabold tracking-tight">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-300 via-sky-300 to-blue-400">
                Lega Pauper
              </span>
              <br />
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-sky-300 via-blue-300 to-sky-400">
                Adriatica
              </span>
            </h1>
            <p className="text-xl sm:text-2xl text-blue-200 max-w-3xl mx-auto leading-relaxed">
              La piattaforma ufficiale per i tornei di <span className="text-white font-semibold">Magic: The Gathering Pauper</span> nella regione Adriatica
            </p>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-8 max-w-4xl mx-auto">
            <div className="bg-black/20 backdrop-blur-sm p-4 rounded-xl border border-blue-500/30">
              <div className="flex items-center justify-center mb-2">
                <Users className="w-6 h-6 text-blue-300" />
              </div>
              <div className="text-2xl font-bold text-white">150+</div>
              <div className="text-sm text-blue-200">Giocatori</div>
            </div>
            <div className="bg-black/20 backdrop-blur-sm p-4 rounded-xl border border-blue-500/30">
              <div className="flex items-center justify-center mb-2">
                <Trophy className="w-6 h-6 text-yellow-400" />
              </div>
              <div className="text-2xl font-bold text-white">50+</div>
              <div className="text-sm text-blue-200">Tornei</div>
            </div>
            <div className="bg-black/20 backdrop-blur-sm p-4 rounded-xl border border-blue-500/30">
              <div className="flex items-center justify-center mb-2">
                <Calendar className="w-6 h-6 text-green-400" />
              </div>
              <div className="text-2xl font-bold text-white">12</div>
              <div className="text-sm text-blue-200">Eventi/Mese</div>
            </div>
            <div className="bg-black/20 backdrop-blur-sm p-4 rounded-xl border border-blue-500/30">
              <div className="flex items-center justify-center mb-2">
                <TrendingUp className="w-6 h-6 text-purple-400" />
              </div>
              <div className="text-2xl font-bold text-white">95%</div>
              <div className="text-sm text-blue-200">Soddisfazione</div>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-8">
            <Link 
              href="/calendar"
              className="w-full sm:w-auto px-8 py-4 bg-gradient-to-r from-blue-600 to-sky-600 hover:from-blue-700 hover:to-sky-700 rounded-xl font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              Vedi Prossimi Tornei
            </Link>
            <Link 
              href="/login"
              className="w-full sm:w-auto px-8 py-4 bg-black/30 backdrop-blur-sm border border-blue-500/50 hover:bg-black/40 hover:border-blue-400 rounded-xl font-semibold text-lg transition-all duration-300"
            >
              Accedi al Tuo Profilo
            </Link>
          </div>

          {/* Scroll Indicator */}
          <div className="pt-12">
            <div className="animate-bounce">
              <div className="w-6 h-10 border-2 border-blue-300/50 rounded-full mx-auto">
                <div className="w-1 h-3 bg-blue-300 rounded-full mx-auto mt-2 animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
