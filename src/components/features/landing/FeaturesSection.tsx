import Link from 'next/link';
import { 
  Calendar, 
  Trophy, 
  BarChart3, 
  Users, 
  Shield, 
  Zap,
  ArrowRight 
} from 'lucide-react';

const features = [
  {
    icon: Calendar,
    title: "Gestione Tornei",
    description: "Sistema completo per la registrazione e gestione dei tornei. Iscrizioni online, bracket automatici e risultati in tempo reale.",
    link: "/calendar",
    linkText: "Vedi Calendario",
    color: "from-blue-500 to-sky-500"
  },
  {
    icon: Trophy,
    title: "Classifiche Live",
    description: "Segui le classifiche della lega in tempo reale. Punti, ranking e statistiche sempre aggiornate per tutti i giocatori.",
    link: "/classifica",
    linkText: "Vedi Classifica",
    color: "from-yellow-500 to-orange-500"
  },
  {
    icon: BarChart3,
    title: "Statistiche Avanzate",
    description: "Analizza le tue performance con statistiche dettagliate. Winrate, archetipi preferiti e trend di miglioramento.",
    link: "/profile",
    linkText: "Il Tuo Profilo",
    color: "from-purple-500 to-pink-500"
  },
  {
    icon: Users,
    title: "Comunità Attiva",
    description: "Connettiti con centinaia di giocatori Pauper. Condividi strategie, deck tech e partecipa alle discussioni.",
    link: "#community",
    linkText: "Unisciti a Noi",
    color: "from-green-500 to-emerald-500"
  },
  {
    icon: Shield,
    title: "Fair Play",
    description: "Sistema di arbitraggio professionale e regolamento chiaro. Garantiamo un ambiente di gioco equo e divertente.",
    link: "/regolamento",
    linkText: "Leggi Regolamento",
    color: "from-red-500 to-rose-500"
  },
  {
    icon: Zap,
    title: "Tecnologia Moderna",
    description: "Piattaforma veloce e responsive. Accedi da qualsiasi dispositivo e resta sempre connesso con la lega.",
    link: "#tech",
    linkText: "Scopri di Più",
    color: "from-cyan-500 to-blue-500"
  }
];

export function FeaturesSection() {
  return (
    <section className="py-20 sm:py-32 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-5xl font-bold mb-6">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-300 to-sky-300">
              Tutto quello che serve
            </span>
            <br />
            <span className="text-white">per dominare il Pauper</span>
          </h2>
          <p className="text-xl text-blue-200 max-w-3xl mx-auto">
            Una piattaforma completa progettata dai giocatori, per i giocatori. 
            Ogni funzionalità è pensata per migliorare la tua esperienza competitiva.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div 
              key={index}
              className="group relative bg-black/20 backdrop-blur-sm p-8 rounded-2xl border border-blue-500/30 hover:border-blue-400/50 transition-all duration-300 hover:transform hover:scale-105"
            >
              {/* Background Gradient */}
              <div className={`absolute inset-0 bg-gradient-to-br ${feature.color} opacity-0 group-hover:opacity-10 rounded-2xl transition-opacity duration-300`}></div>
              
              {/* Icon */}
              <div className="relative mb-6">
                <div className={`inline-flex p-3 rounded-xl bg-gradient-to-br ${feature.color} shadow-lg`}>
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
              </div>

              {/* Content */}
              <div className="relative">
                <h3 className="text-xl font-bold text-white mb-4 group-hover:text-blue-100 transition-colors">
                  {feature.title}
                </h3>
                <p className="text-blue-200 mb-6 leading-relaxed">
                  {feature.description}
                </p>

                {/* Link */}
                <Link 
                  href={feature.link}
                  className="inline-flex items-center gap-2 text-blue-300 hover:text-white font-medium transition-colors group/link"
                >
                  {feature.linkText}
                  <ArrowRight className="w-4 h-4 transition-transform group-hover/link:translate-x-1" />
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-blue-600/20 to-sky-600/20 backdrop-blur-sm p-8 rounded-2xl border border-blue-500/30">
            <h3 className="text-2xl font-bold text-white mb-4">
              Pronto a entrare in azione?
            </h3>
            <p className="text-blue-200 mb-6">
              Registrati oggi e inizia la tua avventura nel mondo competitivo del Pauper
            </p>
            <Link 
              href="/login"
              className="inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-blue-600 to-sky-600 hover:from-blue-700 hover:to-sky-700 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105"
            >
              Inizia Ora
              <ArrowRight className="w-5 h-5" />
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
