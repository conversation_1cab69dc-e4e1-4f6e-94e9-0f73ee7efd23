import { useEffect, useState } from 'react';
import { useAuthContext } from '@/lib/providers/AuthProvider';
import { logger } from '@/lib/utils/logger';

// In-memory cache per userId (value + timestamp)
const memoryCache = new Map<string, { value: boolean; ts: number }>();
// Inflight requests per userId to coalesce concurrent calls
const inflightByUser = new Map<string, Promise<boolean>>();
const TTL_MS = 60_000; // 60s cache window
const STORAGE_KEY_PREFIX = 'lpa:isAdmin:';

function readStorage(userId: string): { value: boolean; ts: number } | null {
  try {
    const raw = sessionStorage.getItem(STORAGE_KEY_PREFIX + userId);
    if (!raw) return null;
    const parsed = JSON.parse(raw) as { value: boolean; ts: number };
    return { value: parsed.value, ts: parsed.ts };
  } catch {
    return null;
  }
}

function writeStorage(userId: string, value: boolean) {
  try {
    sessionStorage.setItem(STORAGE_KEY_PREFIX + userId, JSON.stringify({ value, ts: Date.now() }));
  } catch {}
}

export function invalidateIsAdminCache(userId?: string) {
  if (userId) {
    memoryCache.delete(userId);
    try { sessionStorage.removeItem(STORAGE_KEY_PREFIX + userId); } catch {}
    inflightByUser.delete(userId);
  } else {
    memoryCache.clear();
    inflightByUser.clear();
    try {
      // Best-effort: remove all keys for this prefix
      const keys = Object.keys(sessionStorage);
      keys.forEach(k => { if (k.startsWith(STORAGE_KEY_PREFIX)) sessionStorage.removeItem(k); });
    } catch {}
  }
}

export function useIsAdmin(): { isAdmin: boolean; loading: boolean } {
  const { user, loading: authLoading } = useAuthContext();
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAdminRole = async () => {
      if (!user) {
        logger.debug('Nessun utente autenticato', { component: 'useIsAdmin' });
        setIsAdmin(false);
        setLoading(false);
        return;
      }

      const now = Date.now();
      // 1) In-memory cache per user
      const mem = memoryCache.get(user.id);
      if (mem && (now - mem.ts) < TTL_MS) {
        setIsAdmin(mem.value);
        setLoading(false);
        return;
      }

      // 2) sessionStorage cache per user
      const stored = readStorage(user.id);
      if (stored && (now - stored.ts) < TTL_MS) {
        memoryCache.set(user.id, { value: stored.value, ts: stored.ts });
        setIsAdmin(stored.value);
        setLoading(false);
        return;
      }

      // 3) Coalesce concurrent fetches per user
      let inflight = inflightByUser.get(user.id);
      if (!inflight) {
        inflight = (async () => {
          try {
            const res = await fetch('/api/auth/is-admin', { credentials: 'include', cache: 'no-store' });
            const data = await res.json();
            const val = Boolean(data.isAdmin);
            const ts = Date.now();
            memoryCache.set(user.id, { value: val, ts });
            writeStorage(user.id, val);
            return val;
          } catch (error) {
            logger.error('Errore durante la verifica del ruolo admin (API)', error, { component: 'useIsAdmin' });
            const ts = Date.now();
            memoryCache.set(user.id, { value: false, ts });
            writeStorage(user.id, false);
            return false;
          } finally {
            // Clean inflight entry for this userId
            inflightByUser.delete(user.id);
          }
        })();
        inflightByUser.set(user.id, inflight);
      }

      const val = await inflight;
      setIsAdmin(val);
      setLoading(false);
    };

    if (!authLoading) {
      checkAdminRole();
    }
  }, [user?.id, authLoading, user]);

  return { isAdmin, loading: loading || authLoading };
}
