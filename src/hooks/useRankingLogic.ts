import { useState, useMemo, useCallback } from "react";
import { Player } from "@/types/ranking";

type SortConfig = {
  key: "name" | "totalPoints";
  direction: "asc" | "desc";
};

export function useRankingLogic(initialPlayers: Player[]) {
  const [searchQuery, setSearchQuery] = useState("");
  const [sortConfig, setSortConfig] = useState<SortConfig>({ 
    key: "totalPoints", 
    direction: "desc" 
  });

  const filteredPlayers = useMemo(() => {
    return initialPlayers
      .filter((player) => {
        const matchesSearch = player.name
          .toLowerCase()
          .includes(searchQuery.toLowerCase());
        return matchesSearch;
      })
      .sort((a, b) => {
        if (sortConfig.key === "name") {
          return sortConfig.direction === "asc"
            ? a.name.localeCompare(b.name)
            : b.name.localeCompare(a.name);
        } else {
          const aPoints = a.totalPoints || 0;
          const bPoints = b.totalPoints || 0;
          return sortConfig.direction === "asc"
            ? aPoints - bPoints
            : bPoints - aPoints;
        }
      });
  }, [initialPlayers, searchQuery, sortConfig]);

  const handleSort = useCallback((key: "name" | "totalPoints") => {
    setSortConfig((current) => ({
      key,
      direction:
        current.key === key && current.direction === "asc" ? "desc" : "asc",
    }));
  }, []);

  return {
    searchQuery,
    setSearchQuery,
    sortConfig,
    handleSort,
    filteredPlayers
  };
} 