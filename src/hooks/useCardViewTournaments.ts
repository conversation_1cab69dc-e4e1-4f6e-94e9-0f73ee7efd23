import { useState, useCallback, useMemo, useEffect } from 'react';
import { useTournamentsPaginated } from '@/lib/hooks/useTournaments';
import { Tournament, TournamentFilters } from '@/types/calendar';
import { applyTournamentFilters } from '@/lib/utils/tournamentFilters';

const TOURNAMENTS_PER_PAGE = 12;

export function useCardViewTournaments(filters?: TournamentFilters) {
  const [currentPage, setCurrentPage] = useState(0);
  
  // Fetch paginated tournaments
  const { 
    data: paginatedData, 
    isLoading, 
    error 
  } = useTournamentsPaginated(currentPage, TOURNAMENTS_PER_PAGE);

  // Accumulate all loaded tournaments across pages
  const [allLoadedTournaments, setAllLoadedTournaments] = useState<Tournament[]>([]);

  // Update accumulated tournaments when new page data arrives
  useEffect(() => {
    if (paginatedData?.data) {
      if (currentPage === 0) {
        // Reset for first page
        setAllLoadedTournaments(paginatedData.data);
      } else {
        // Append new tournaments for subsequent pages
        setAllLoadedTournaments(prev => [...prev, ...paginatedData.data]);
      }
    }
  }, [paginatedData, currentPage]);

  const hasMore = paginatedData?.hasMore ?? false;
  const total = paginatedData?.total ?? 0;

  const loadMore = useCallback(() => {
    if (hasMore && !isLoading) {
      setCurrentPage(prev => prev + 1);
    }
  }, [hasMore, isLoading]);

  const reset = useCallback(() => {
    setCurrentPage(0);
    setAllLoadedTournaments([]);
  }, []);
  
  // Reset when filters change (deep comparison to avoid infinite loops)
  const filtersString = JSON.stringify(filters);
  useEffect(() => {
    reset();
  }, [filtersString, reset]);

  const tournaments = useMemo(() => {
    const enhanced = allLoadedTournaments.map(tournament => ({
      ...tournament,
      currentPlayers: tournament.tournament_registrations?.[0]?.count || 0
    }));
    
    // Apply filters if provided
    return filters ? applyTournamentFilters(enhanced, filters) : enhanced;
  }, [allLoadedTournaments, filters]);

  return {
    tournaments,
    isLoading,
    error,
    hasMore,
    total,
    loadMore,
    reset,
    currentPage
  };
}
