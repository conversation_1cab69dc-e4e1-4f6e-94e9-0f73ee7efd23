import { useState, useEffect, useMemo } from 'react';

interface TimeLeft {
  days: string;
  hours: string;
  minutes: string;
  seconds: string;
}

interface CountdownOptions {
  targetDate?: Date;
  defaultMonthsAhead?: number;
}

/**
 * Hook personalizzato per gestire un countdown verso una data specifica
 * @param options Opzioni di configurazione del countdown
 * @returns Oggetto con i valori formattati del tempo rimanente
 */
export const useCountdown = (options: CountdownOptions = {}) => {
  const { targetDate, defaultMonthsAhead = 1 } = options;
  
  // Se non viene fornita una data target, imposta una data predefinita
  const launchDate = useMemo(() => {
    if (targetDate) return targetDate;
    
    const date = new Date();
    date.setMonth(date.getMonth() + defaultMonthsAhead);
    return date;
  }, [targetDate, defaultMonthsAhead]);
  
  const [timeLeft, setTimeLeft] = useState<TimeLeft>({
    days: "00",
    hours: "00",
    minutes: "00",
    seconds: "00"
  });

  useEffect(() => {
    const formatTimeUnit = (value: number): string => {
      return value.toString().padStart(2, '0');
    };
    
    const calculateTimeLeft = () => {
      const difference = launchDate.getTime() - new Date().getTime();
      
      if (difference > 0) {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setTimeLeft({
          days: formatTimeUnit(days),
          hours: formatTimeUnit(hours),
          minutes: formatTimeUnit(minutes),
          seconds: formatTimeUnit(seconds)
        });
      }
    };

    // Calcola immediatamente il tempo rimanente
    calculateTimeLeft();
    
    // Imposta un intervallo per aggiornare il countdown ogni secondo
    const timer = setInterval(calculateTimeLeft, 1000);

    // Pulisci l'intervallo quando il componente viene smontato
    return () => clearInterval(timer);
  }, [launchDate]);

  return timeLeft;
}; 