import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import type { Session, User } from '@supabase/supabase-js';
import { logger } from '@/lib/utils/logger';

interface UseAuthReturn {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signOut: () => Promise<void>;
}

export function useAuth(): UseAuthReturn {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const load = async () => {
      try {
        const res = await fetch('/api/auth/session', { credentials: 'include' });
        const data = await res.json();
        setUser(data.user);
        setSession(data.session);
      } catch (error) {
        logger.error('Errore caricando sessione server', error, { component: 'useAuth' });
      } finally {
        setLoading(false);
      }
    };
    load();
  }, []);

  const signOut = async () => {
    try {
      setLoading(true);
      // Recupera l'URL di reindirizzamento post-logout prima del logout
      let returnUrl = '/';
      if (typeof window !== 'undefined') {
        returnUrl = localStorage.getItem('logoutReturnUrl') || '/';
        localStorage.removeItem('logoutReturnUrl');
      }

      const response = await fetch('/api/auth/signout', { method: 'POST', credentials: 'include' });
      if (!response.ok) {
        throw new Error(`Logout failed: ${response.status} ${response.statusText}`);
      }
      
      setUser(null);
      setSession(null);
      try { sessionStorage.removeItem('lpa:isAdmin'); } catch {}
      router.push(returnUrl);
    } catch (error) {
      // Even if the API call fails, clear the local state and redirect
      setUser(null);
      setSession(null);
      try { sessionStorage.removeItem('lpa:isAdmin'); } catch {}
      router.push('/');
      logger.error('Errore critico durante il logout', error, { component: 'useAuth' });
    } finally {
      setLoading(false);
    }
  };

  return {
    user,
    session,
    loading,
    signOut,
  };
}
