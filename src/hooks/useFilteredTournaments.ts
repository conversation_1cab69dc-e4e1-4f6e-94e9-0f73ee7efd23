import { useMemo } from 'react';
import { useTournaments } from '@/lib/hooks/useTournaments';
import { Tournament, TournamentFilters } from '@/types/calendar';
import { applyTournamentFilters } from '@/lib/utils/tournamentFilters';

export function useFilteredTournaments(filters?: TournamentFilters) {
  const { data: allTournaments = [], isLoading, error } = useTournaments();

  const filteredTournaments = useMemo(() => {
    if (!allTournaments.length) return [];
    
    // Enhance tournaments with currentPlayers
    const enhanced = allTournaments.map(tournament => ({
      ...tournament,
      currentPlayers: tournament.tournament_registrations?.[0]?.count || 0
    } as Tournament));
    
    // Apply filters if provided
    return filters ? applyTournamentFilters(enhanced, filters) : enhanced;
  }, [allTournaments, filters]);

  return {
    tournaments: filteredTournaments,
    isLoading,
    error,
    total: filteredTournaments.length
  };
}
