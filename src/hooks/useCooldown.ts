import { useEffect, useRef, useState } from 'react';

export function useCooldown(key: string, ms: number) {
  const storageKey = `cooldown:${key}`;
  const [remaining, setRemaining] = useState<number>(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const now = Date.now();
    const until = Number(localStorage.getItem(storageKey) || 0);
    const diff = Math.max(0, until - now);
    setRemaining(diff);

    if (diff > 0) {
      intervalRef.current = setInterval(() => {
        const left = Math.max(0, until - Date.now());
        setRemaining(left);
        if (left <= 0 && intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      }, 250);
    }

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [storageKey]);

  const start = () => {
    const until = Date.now() + ms;
    localStorage.setItem(storageKey, String(until));
    setRemaining(ms);
    if (intervalRef.current) clearInterval(intervalRef.current);
    intervalRef.current = setInterval(() => {
      const left = Math.max(0, until - Date.now());
      setRemaining(left);
      if (left <= 0 && intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }, 250);
  };

  return {
    remaining,
    isCoolingDown: remaining > 0,
    start,
  };
}
