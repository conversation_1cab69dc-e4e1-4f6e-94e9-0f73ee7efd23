import { useState, useMemo, useCallback, useEffect } from "react";
import { Tournament, ViewMode, VIEW_MODE_KEY, TournamentFilters } from "@/types/calendar";
import { getDaysInMonth, getFirstDayOfMonth } from "@/lib/utils/calendar";
import { useTournamentsByMonth } from "@/lib/hooks/useTournaments";
import { applyTournamentFilters } from "@/lib/utils/tournamentFilters";

export function useCalendarLogic(filters?: TournamentFilters) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedTournament, setSelectedTournament] = useState<Tournament | null>(null);
  const [isRegistrationModalOpen, setIsRegistrationModalOpen] = useState(false);
  
  // View mode state with localStorage persistence
  // Always start with 'calendar' to avoid hydration mismatch
  const [viewMode, setViewMode] = useState<ViewMode>('calendar');
  const [isViewModeInitialized, setIsViewModeInitialized] = useState(false);
  
  // Load saved view mode from localStorage after hydration
  useEffect(() => {
    const saved = localStorage.getItem(VIEW_MODE_KEY) as ViewMode;
    if (saved === 'cards' || saved === 'calendar') {
      setViewMode(saved);
    }
    setIsViewModeInitialized(true);
  }, []);
  
  // Persist view mode changes to localStorage
  useEffect(() => {
    if (isViewModeInitialized) {
      localStorage.setItem(VIEW_MODE_KEY, viewMode);
    }
  }, [viewMode, isViewModeInitialized]);
  
  // Gestione del calendario
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();
  const daysInMonth = getDaysInMonth(currentYear, currentMonth);
  const firstDayOfMonth = getFirstDayOfMonth(currentYear, currentMonth);

  // Recupero i tornei del mese corrente da Supabase
  const { data: tournamentsInCurrentMonth = [], isLoading } = useTournamentsByMonth(currentYear, currentMonth);

  const goToPreviousMonth = useCallback(() => {
    setCurrentDate(new Date(currentYear, currentMonth - 1, 1));
    setSelectedDate(null);
    setSelectedTournament(null);
  }, [currentYear, currentMonth]);

  const goToNextMonth = useCallback(() => {
    setCurrentDate(new Date(currentYear, currentMonth + 1, 1));
    setSelectedDate(null);
    setSelectedTournament(null);
  }, [currentYear, currentMonth]);

  // Apply filters to tournaments and organize by day
  const tournamentDays = useMemo(() => {
    const days: Record<number, Tournament[]> = {};
    
    if (Array.isArray(tournamentsInCurrentMonth)) {
      // Enhance tournaments with currentPlayers
      const enhancedTournaments = tournamentsInCurrentMonth.map(tournament => ({
        ...tournament,
        currentPlayers: tournament.tournament_registrations?.[0]?.count || 0
      } as Tournament));
      
      // Apply filters if provided
      const filtered = filters 
        ? applyTournamentFilters(enhancedTournaments, filters)
        : enhancedTournaments;
      
      // Organize filtered tournaments by day
      filtered.forEach(tournament => {
        const day = new Date(tournament.date).getDate();
        if (!days[day]) {
          days[day] = [];
        }
        days[day].push(tournament);
      });
    }
    
    return days;
  }, [tournamentsInCurrentMonth, filters]);

  // Gestione selezione
  const handleDayClick = useCallback((day: number) => {
    const newSelectedDate = new Date(currentYear, currentMonth, day);
    setSelectedDate(newSelectedDate);
    
    const tournamentsOnDay = tournamentDays[day] || [];
    if (tournamentsOnDay.length === 1) {
      setSelectedTournament(tournamentsOnDay[0]);
    } else if (tournamentsOnDay.length === 0) {
      setSelectedTournament(null);
    }
  }, [currentYear, currentMonth, tournamentDays]);

  const handleTournamentClick = useCallback((tournament: Tournament) => {
    setSelectedTournament(tournament);
  }, []);
  
  const toggleViewMode = useCallback((mode: ViewMode) => {
    setViewMode(mode);
  }, []);
  

  return {
    currentMonth,
    currentYear,
    daysInMonth,
    firstDayOfMonth,
    selectedDate,
    selectedTournament,
    tournamentDays,
    isRegistrationModalOpen,
    isLoading,
    goToPreviousMonth,
    goToNextMonth,
    handleDayClick,
    handleTournamentClick,
    setSelectedTournament,
    setIsRegistrationModalOpen,
    // View mode functionality
    viewMode,
    toggleViewMode
  };
} 