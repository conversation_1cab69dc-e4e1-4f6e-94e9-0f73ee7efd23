import { useState, useCallback } from "react";

/**
 * Interface per il risultato dell'hook useMenuState
 */
interface MenuState {
  /** Stato corrente del menu mobile (aperto/chiuso) */
  mobileMenuOpen: boolean;
  /** Funzione per alternare lo stato del menu */
  toggleMenu: () => void;
  /** Funzione per chiudere il menu */
  closeMenu: () => void;
  /** Funzione per aprire il menu */
  openMenu: () => void;
}

/**
 * Hook personalizzato per gestire lo stato del menu mobile
 * @returns Oggetto con lo stato e le funzioni per manipolarlo
 */
export function useMenuState(): MenuState {
  const [mobileMenuOpen, setMobileMenuOpen] = useState<boolean>(false);

  // Utilizzo useCallback per evitare ricreazioni inutili delle funzioni
  const toggleMenu = useCallback(() => setMobileMenuOpen(prev => !prev), []);
  const closeMenu = useCallback(() => setMobileMenuOpen(false), []);
  const openMenu = useCallback(() => setMobileMenuOpen(true), []);

  return {
    mobileMenuOpen,
    toggleMenu,
    closeMenu,
    openMenu
  };
} 