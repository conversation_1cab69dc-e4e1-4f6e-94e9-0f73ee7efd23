import { useState, useEffect, useCallback } from 'react';
import { TournamentFilters, DEFAULT_FILTERS, FILTERS_KEY } from '@/types/calendar';

export function useFilters() {
  // Always start with default filters to avoid hydration mismatch
  const [filters, setFilters] = useState<TournamentFilters>(DEFAULT_FILTERS);
  const [isFiltersInitialized, setIsFiltersInitialized] = useState(false);

  // Load saved filters from localStorage after hydration
  useEffect(() => {
    const savedFilters = localStorage.getItem(FILTERS_KEY);
    if (savedFilters) {
      try {
        const parsed = JSON.parse(savedFilters) as TournamentFilters;
        // Validate the parsed data has the expected structure
        if (parsed && typeof parsed === 'object') {
          setFilters({
            sortOrder: parsed.sortOrder || DEFAULT_FILTERS.sortOrder,
            startDate: parsed.startDate || null,
            endDate: parsed.endDate || null,
            storeId: parsed.storeId || null,
          });
        }
      } catch (error) {
        console.warn('Failed to parse saved filters:', error);
      }
    }
    setIsFiltersInitialized(true);
  }, []);

  // Persist filter changes to localStorage
  useEffect(() => {
    if (isFiltersInitialized) {
      localStorage.setItem(FILTERS_KEY, JSON.stringify(filters));
    }
  }, [filters, isFiltersInitialized]);

  // Update individual filter properties
  const updateFilter = useCallback(<K extends keyof TournamentFilters>(
    key: K,
    value: TournamentFilters[K]
  ) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  }, []);

  // Reset all filters to default
  const resetFilters = useCallback(() => {
    setFilters(DEFAULT_FILTERS);
  }, []);

  // Check if any filters are active (different from default)
  const hasActiveFilters = useCallback(() => {
    return (
      filters.sortOrder !== DEFAULT_FILTERS.sortOrder ||
      filters.startDate !== null ||
      filters.endDate !== null ||
      filters.storeId !== null
    );
  }, [filters]);

  return {
    filters,
    updateFilter,
    resetFilters,
    hasActiveFilters: hasActiveFilters(),
    isFiltersInitialized,
  };
}
