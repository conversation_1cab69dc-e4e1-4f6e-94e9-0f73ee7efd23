import { NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

export async function GET() {
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    return NextResponse.json({ error: 'Missing Supabase env vars' }, { status: 500 });
  }

  const cookieStore = await cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            cookieStore.set(name, value, options);
          });
        },
      },
    }
  );

  const { data: userRes } = await supabase.auth.getUser();
  if (!userRes.user) {
    return NextResponse.json({ isAdmin: false }, { status: 200, headers: { 'Cache-Control': 'private, max-age=60' } });
  }

  try {
    const { data } = await supabase.rpc('is_admin');
    return NextResponse.json(
      { isAdmin: Boolean(data) },
      { status: 200, headers: { 'Cache-Control': 'no-store', 'Vary': 'Cookie' } }
    );
  } catch {
    return NextResponse.json(
      { isAdmin: false },
      { status: 200, headers: { 'Cache-Control': 'no-store', 'Vary': 'Cookie' } }
    );
  }
}
