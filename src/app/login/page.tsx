'use client';

import { Suspense, useEffect, useMemo, useState } from 'react';
import { Header } from '@/components/shared/Header';
import { supabase } from '@/lib/supabase/client';
import { LogIn, UserPlus, MailCheck } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useCooldown } from '@/hooks/useCooldown';
import { useAuthContext } from '@/lib/providers/AuthProvider';
import { getSiteUrl } from '@/lib/utils/site';

function LoginContent() {
  const router = useRouter();
  const { user, loading } = useAuthContext();
  const [email, setEmail] = useState('');
  const [status, setStatus] = useState<'idle' | 'sending' | 'sent' | 'system_error'>('idle');
  const [message, setMessage] = useState<string | null>(null);
  const searchParams = useSearchParams();

  // Determine target redirect (next)
  const nextTarget = useMemo(() => {
    if (typeof window === 'undefined') return '/';
    const nextFromQuery = searchParams.get('next');
    if (nextFromQuery) return nextFromQuery;
    const stored = sessionStorage.getItem('returnUrl');
    return stored || '/profile';
  }, [searchParams]);

  // If already logged in, redirect to nextTarget
  useEffect(() => {
    if (!loading && user) {
      router.replace(nextTarget);
    }
  }, [user, loading, nextTarget, router]);

  // Cooldown: 60s per email to mitigate abuse
  const cooldownKey = email ? `magic:${email.toLowerCase()}` : 'magic:anon';
  const { remaining, isCoolingDown, start } = useCooldown(cooldownKey, 60_000);

  useEffect(() => {
    // Prefill email from query string if provided (optional)
    const prefill = searchParams.get('email');
    if (prefill) setEmail(prefill);
  }, [searchParams]);

  const sendMagicLink = async (shouldCreateUser: boolean) => {
    if (!email) return;
    if (isCoolingDown) return;

    try {
      setStatus('sending');
      setMessage(null);

      // Always use the current browser origin to avoid host mismatches (e.g., LAN IP)
      const baseUrl = typeof window !== 'undefined' ? window.location.origin : getSiteUrl();
      // Redirect to internal confirm route; Supabase will append token_hash and type
      const emailRedirectTo = `${baseUrl}/auth/confirm`;

      // Use OTP Magic Link
      const { error } = await supabase.auth.signInWithOtp({
        email: email.trim().toLowerCase(),
        options: {
          emailRedirectTo,
          shouldCreateUser,
        },
      });

      // Always show success message to prevent email enumeration
      // Whether the email exists or not, show the same positive response
      setStatus('sent');
      setMessage("Se l'indirizzo è valido, ti abbiamo inviato un link di accesso. Controlla la tua email.");
      start();
      
      // Log actual errors for debugging (but don't show to user)
      if (error) {
        console.warn('Magic link error (hidden from user):', error);
      }
    } catch (err) {
      // Only show system errors for genuine technical problems
      console.error('Magic link system error:', err);
      setStatus('system_error');
      setMessage('Si è verificato un errore di sistema. Riprova più tardi.');
    }
  };

  const disabled = isCoolingDown || status === 'sending' || !email;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 text-white">
      <Header />

      <main className="container mx-auto px-4 py-12">
        <div className="max-w-md mx-auto bg-sky-900/30 backdrop-blur-sm p-8 rounded-lg border border-sky-500/30 shadow-lg">
          <h1 className="text-2xl font-bold text-center mb-6">Area Riservata</h1>

          <div className="space-y-6">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-sky-300 mb-1">
                Email
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-3 py-2 bg-blue-900/50 border border-blue-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="<EMAIL>"
                autoComplete="email"
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-3">
              <button
                type="button"
                onClick={() => sendMagicLink(false)}
                disabled={disabled}
                className="flex items-center justify-center gap-2 bg-sky-600 hover:bg-sky-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-medium py-2 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-sky-500"
              >
                <LogIn size={18} />
                Accedi
              </button>

              <button
                type="button"
                onClick={() => sendMagicLink(true)}
                disabled={disabled}
                className="flex items-center justify-center gap-2 bg-emerald-600 hover:bg-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-medium py-2 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-emerald-500"
              >
                <UserPlus size={18} />
                Registrati
              </button>
            </div>

            {isCoolingDown && (
              <p className="text-xs text-sky-300 text-center">
                Attendi {Math.ceil(remaining / 1000)} secondi prima di richiedere un nuovo link.
              </p>
            )}

            {status === 'sent' && (
              <div className="p-4 bg-emerald-500/15 border border-emerald-500/30 rounded-lg">
                <div className="flex flex-col items-center text-center space-y-3">
                  <div className="p-3 bg-emerald-500/20 rounded-full">
                    <MailCheck size={32} className="text-emerald-300" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-emerald-200 mb-1">Email inviata!</h3>
                    <p className="text-sm text-emerald-200/90">{message}</p>
                  </div>
                </div>
              </div>
            )}
            {status === 'system_error' && message && (
              <div className="p-3 bg-red-500/15 border border-red-500/30 rounded text-red-200">
                {message}
              </div>
            )}

            <p className="text-xs text-sky-300">
              Per sicurezza, il link scade automaticamente e può essere utilizzato una sola volta.
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}

export default function LoginPage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 text-white">
          <Header />
          <main className="container mx-auto px-4 py-12">
            <div className="max-w-md mx-auto bg-sky-900/30 backdrop-blur-sm p-8 rounded-lg border border-sky-500/30 shadow-lg text-center">
              <div className="animate-pulse text-sky-300">Caricamento...</div>
            </div>
          </main>
        </div>
      }
    >
      <LoginContent />
    </Suspense>
  );
}
