"use client";

import { useState, lazy, Suspense } from "react";
import { Player } from "@/types/ranking";
import { Header } from "@/components/shared/Header";
import { Footer } from "@/components/shared/Footer";
import { RankingContainer } from "@/components/ranking/RankingContainer";
import { useRankingLogic } from "@/hooks/useRankingLogic";
import { usePlayersRanking } from "@/lib/hooks/usePlayers";
import { PostgrestError } from "@supabase/supabase-js";
import { logger } from '@/lib/utils/logger';
import { ErrorBoundary } from "@/components/shared/ErrorBoundary";
import { ErrorFallback } from "@/components/shared/ErrorFallback";
import { LoadingState } from "@/components/ui/LoadingState";

// Dynamic import for PlayerHistoryModal to reduce initial bundle size
const PlayerHistoryModal = lazy(() => 
  import("@/components/ranking/PlayerHistoryModal").then(mod => ({ 
    default: mod.PlayerHistoryModal 
  }))
);

export default function RankingPage() {
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);
  const { data: players, isLoading, error } = usePlayersRanking();
  
  const {
    searchQuery,
    setSearchQuery,
    sortConfig,
    handleSort,
    filteredPlayers
  } = useRankingLogic((players as Player[] | undefined) ?? []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 text-white">
        <Header currentPage="ranking" />
        <main className="container mx-auto p-4 sm:p-6">
          <h1 className="text-2xl sm:text-3xl font-bold mb-4 sm:mb-8 sm:text-center">
            Classifica Giocatori
          </h1>
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (error) {
    const errorMessage = (error as PostgrestError).message || 'Errore sconosciuto';
    logger.error('Errore nella pagina della classifica', error, { component: 'RankingPage' });
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 text-white">
        <Header currentPage="ranking" />
        <main className="container mx-auto p-4 sm:p-6">
          <h1 className="text-2xl sm:text-3xl font-bold mb-4 sm:mb-8 sm:text-center">
            Classifica Giocatori
          </h1>
          <div className="flex flex-col items-center justify-center h-64">
            <p className="text-red-400 mb-2">Si è verificato un errore nel caricamento della classifica.</p>
            <p className="text-red-400/75 text-sm text-center max-w-md">
              {errorMessage}
            </p>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 text-white">
      <Header currentPage="ranking" />

      <main className="container mx-auto p-4 sm:p-6">
        <h1 className="text-2xl sm:text-3xl font-bold mb-4 sm:mb-8 sm:text-center">
          Classifica Giocatori
        </h1>

        <ErrorBoundary
          level="section"
          fallback={<ErrorFallback variant="detailed" />}
          context={{ component: 'RankingContainer', page: 'classifica' }}
        >
          <RankingContainer
            players={filteredPlayers}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            sortConfig={sortConfig}
            onSort={handleSort}
            onPlayerClick={setSelectedPlayer}
            isLoading={isLoading}
            error={error ? (error as PostgrestError).message : undefined}
          />
        </ErrorBoundary>
      </main>
      
      <Footer />

      <ErrorBoundary
        level="component"
        fallback={<ErrorFallback variant="minimal" />}
        context={{ component: 'PlayerHistoryModal', page: 'classifica' }}
      >
        <Suspense fallback={<LoadingState text="Caricamento..." />}>
          <PlayerHistoryModal
            player={selectedPlayer}
            isOpen={selectedPlayer !== null}
            onClose={() => setSelectedPlayer(null)}
          />
        </Suspense>
      </ErrorBoundary>
    </div>
  );
} 