'use client';

import { lazy, Suspense, useState } from 'react';
import { AdminGuard } from '@/components/admin/AdminGuard';
import { Header } from '@/components/shared/Header';
import { ErrorBoundary } from '@/components/shared/ErrorBoundary';
import { ErrorFallback } from '@/components/shared/ErrorFallback';
import { LoadingState } from '@/components/ui/LoadingState';
import { User, Store } from 'lucide-react';
import { cn } from '@/lib/utils';

// Dynamic imports to reduce initial bundle size
const ProfileForm = lazy(() =>
  import('@/components/admin/ProfileForm').then(mod => ({
    default: mod.ProfileForm
  }))
);

const StoreManagement = lazy(() =>
  import('@/components/admin/stores/StoreManagement').then(mod => ({
    default: mod.StoreManagement
  }))
);

type AdminSection = 'profile' | 'stores';

export default function AdminDashboardPage() {
  const [activeSection, setActiveSection] = useState<AdminSection>('profile');

  const sections = [
    {
      id: 'profile' as AdminSection,
      label: 'Profilo',
      icon: User,
      description: 'Gestisci le tue impostazioni account'
    },
    {
      id: 'stores' as AdminSection,
      label: 'Negozi',
      icon: Store,
      description: 'Gestisci i negozi della lega'
    }
  ];

  const renderActiveSection = () => {
    switch (activeSection) {
      case 'profile':
        return (
          <ErrorBoundary
            level="section"
            fallback={<ErrorFallback variant="detailed" />}
            context={{ component: 'ProfileForm', page: 'admin-dashboard' }}
          >
            <Suspense fallback={<LoadingState text="Caricamento profilo..." />}>
              <ProfileForm />
            </Suspense>
          </ErrorBoundary>
        );

      case 'stores':
        return (
          <ErrorBoundary
            level="section"
            fallback={<ErrorFallback variant="detailed" />}
            context={{ component: 'StoreManagement', page: 'admin-dashboard' }}
          >
            <Suspense fallback={<LoadingState text="Caricamento gestione negozi..." />}>
              <StoreManagement />
            </Suspense>
          </ErrorBoundary>
        );

      default:
        return null;
    }
  };

  return (
    <AdminGuard>
      <div className="min-h-screen bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 text-white">
        <Header currentPage="home" />

        <main className="container mx-auto px-4 py-12">
          <div className="max-w-4xl mx-auto">
            <div className="mb-8">
              <h1 className="text-2xl font-bold mb-2">Pannello Amministratore</h1>
              <p className="text-blue-300">
                Gestisci il sistema e le configurazioni della lega
              </p>
            </div>

            <div className="mb-8">
              <nav className="flex justify-center md:justify-start space-x-1 bg-black/20 backdrop-blur-sm rounded-lg p-1 border border-blue-500/30 w-fit md:w-auto mx-auto md:mx-0">
                {sections.map((section) => {
                  const Icon = section.icon;
                  const isActive = activeSection === section.id;

                  return (
                    <button
                      key={section.id}
                      onClick={() => setActiveSection(section.id)}
                      className={cn(
                        'flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200',
                        'text-sm font-medium',
                        isActive
                          ? 'bg-blue-600 text-white shadow-lg'
                          : 'text-blue-300 hover:text-white hover:bg-blue-900/50'
                      )}
                    >
                      <Icon size={16} />
                      {section.label}
                    </button>
                  );
                })}
              </nav>

              {/* Section Description */}
              <p className="text-sm text-blue-300 mt-2 ml-1 text-center md:text-left">
                {sections.find(s => s.id === activeSection)?.description}
              </p>
            </div>

            {/* Active Section Content */}
            <div className="space-y-6">
              {renderActiveSection()}
            </div>
          </div>
        </main>
      </div>
    </AdminGuard>
  );
}