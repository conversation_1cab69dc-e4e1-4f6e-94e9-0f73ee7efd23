'use client';

import { Header } from '@/components/shared/Header';
import { Footer } from '@/components/shared/Footer';
import { useAuthContext } from '@/lib/providers/AuthProvider';
import { useEffect, useState } from 'react';
import { playersService } from '@/lib/services/players';
import { LoadingState } from '@/components/ui/LoadingState';
import { getDisplayName } from '@/lib/utils';
import { Edit3, Save, X, CheckCircle, Loader2 } from 'lucide-react';
import { z } from 'zod';
import type { Database } from '@/lib/supabase/types';
import { UserEventsSection } from '@/components/profile/UserEvents';

type Player = Database['public']['Tables']['players']['Row'];

// Validation schema
const nameEditSchema = z.object({
  firstName: z
    .string()
    .min(2, 'Il nome deve contenere almeno 2 caratteri')
    .max(40, 'Il nome non può superare 40 caratteri')
    .regex(
      /^[A-Za-zÀ-ÿ'\s-]+$/,
      'Il nome può contenere solo lettere, spazi, apostrofi e trattini'
    ),
  lastName: z
    .string()
    .min(2, 'Il cognome deve contenere almeno 2 caratteri')
    .max(40, 'Il cognome non può superare 40 caratteri')
    .regex(
      /^[A-Za-zÀ-ÿ'\s-]+$/,
      'Il cognome può contenere solo lettere, spazi, apostrofi e trattini'
    ),
});

type NameEditFormData = z.infer<typeof nameEditSchema>;

export default function ProfilePage() {
  const { user, player: currentPlayer, loading, completeOnboarding } = useAuthContext();
  const [player, setPlayer] = useState<Player | null>(null);
  const [fetching, setFetching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Name editing state
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [editForm, setEditForm] = useState<NameEditFormData>({
    firstName: '',
    lastName: ''
  });
  const [validationErrors, setValidationErrors] = useState<
    Partial<Record<keyof NameEditFormData, string>>
  >({});
  const [saveSuccess, setSaveSuccess] = useState(false);

  useEffect(() => {
    const load = async () => {
      if (!user) return;
      setFetching(true);
      setError(null);
      try {
        const p = await playersService.getByAuthUserId(user.id);
        setPlayer(p);
        // Initialize edit form with current values
        setEditForm({
          firstName: p.first_name || '',
          lastName: p.last_name || ''
        });
      } catch {
        setError('Impossibile caricare il profilo utente.');
      } finally {
        setFetching(false);
      }
    };
    if (user) load();
  }, [user]);

  // Initialize edit form when currentPlayer changes
  useEffect(() => {
    if (currentPlayer?.first_name && currentPlayer?.last_name) {
      setEditForm({
        firstName: currentPlayer.first_name,
        lastName: currentPlayer.last_name
      });
    }
  }, [currentPlayer]);

  // Clear success message after delay
  useEffect(() => {
    if (saveSuccess) {
      const timer = setTimeout(() => setSaveSuccess(false), 3000);
      return () => clearTimeout(timer);
    }
  }, [saveSuccess]);

  const handleEditClick = () => {
    setIsEditing(true);
    setError(null);
    setSaveSuccess(false);
    setValidationErrors({});
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setValidationErrors({});
    // Reset form to current values
    const current = currentPlayer || player;
    setEditForm({
      firstName: current?.first_name || '',
      lastName: current?.last_name || ''
    });
  };

  const handleInputChange = (field: keyof NameEditFormData, value: string) => {
    setEditForm(prev => ({ ...prev, [field]: value }));
    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      setError(null);
      setValidationErrors({});

      // Validate form
      const result = nameEditSchema.safeParse(editForm);
      if (!result.success) {
        const errors: Partial<Record<keyof NameEditFormData, string>> = {};
        result.error.errors.forEach((err) => {
          if (err.path[0]) {
            errors[err.path[0] as keyof NameEditFormData] = err.message;
          }
        });
        setValidationErrors(errors);
        return;
      }

      // Save using the completeOnboarding function (works for updates too)
      await completeOnboarding(result.data.firstName.trim(), result.data.lastName.trim());
      
      // Update local state
      if (player) {
        setPlayer(prev => prev ? {
          ...prev,
          first_name: result.data.firstName.trim(),
          last_name: result.data.lastName.trim(),
          name: `${result.data.firstName.trim()} ${result.data.lastName.trim()}`
        } : null);
      }

      setIsEditing(false);
      setSaveSuccess(true);
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Errore durante il salvataggio. Riprova.';
      setError(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  if (loading || fetching) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 text-white">
        <Header />
        <main className="container mx-auto px-4 py-12">
          <LoadingState text="Caricamento profilo..." />
        </main>
      </div>
    );
  }

  if (!user && !loading) {
    if (typeof window !== 'undefined') {
      const currentPath = window.location.pathname + window.location.search;
      sessionStorage.setItem('returnUrl', currentPath);
      window.location.href = `/login?next=${encodeURIComponent(currentPath)}`;
    }
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 text-white">
      <Header />
      <main className="container mx-auto px-4 py-12">
        <div className="max-w-3xl mx-auto bg-sky-900/30 backdrop-blur-sm p-8 rounded-lg border border-sky-500/30 shadow-lg">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold">Profilo utente</h1>
            {!isEditing && (
              <button
                onClick={handleEditClick}
                className="flex items-center gap-2 px-3 py-1.5 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
              >
                <Edit3 className="w-4 h-4" />
                Modifica
              </button>
            )}
          </div>

          {error && (
            <div className="mb-6 p-3 bg-red-500/15 border border-red-500/30 rounded text-red-200">
              {error}
            </div>
          )}

          {/* Success Message */}
          {saveSuccess && (
            <div className="flex items-center gap-2 p-3 mb-6 bg-emerald-500/15 border border-emerald-500/30 rounded text-emerald-200">
              <CheckCircle className="w-5 h-5" />
              <span>Nome e cognome aggiornati con successo!</span>
            </div>
          )}

          {/* Basic Info with Inline Editing */}
          <div className="space-y-4">
            {/* Email (always read-only) */}
            <p className="text-blue-200">
              <span className="text-blue-300">Email:</span> {user?.email}
            </p>

            {/* Name - conditional rendering based on edit mode */}
            {!isEditing ? (
              <p className="text-blue-200">
                <span className="text-blue-300">Nome completo:</span> {getDisplayName(currentPlayer || player, user)}
              </p>
            ) : (
              <div className="space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-blue-300 mb-2">
                      Nome *
                    </label>
                    <input
                      id="firstName"
                      type="text"
                      value={editForm.firstName}
                      onChange={(e) => handleInputChange('firstName', e.target.value)}
                      disabled={isSaving}
                      className="w-full px-3 py-2 bg-blue-900/50 border border-blue-700 rounded text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
                      placeholder="Mario"
                      autoComplete="given-name"
                    />
                    {validationErrors.firstName && (
                      <p className="mt-1 text-sm text-red-400">
                        {validationErrors.firstName}
                      </p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-blue-300 mb-2">
                      Cognome *
                    </label>
                    <input
                      id="lastName"
                      type="text"
                      value={editForm.lastName}
                      onChange={(e) => handleInputChange('lastName', e.target.value)}
                      disabled={isSaving}
                      className="w-full px-3 py-2 bg-blue-900/50 border border-blue-700 rounded text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
                      placeholder="Rossi"
                      autoComplete="family-name"
                    />
                    {validationErrors.lastName && (
                      <p className="mt-1 text-sm text-red-400">
                        {validationErrors.lastName}
                      </p>
                    )}
                  </div>
                </div>

                {/* Action buttons */}
                <div className="flex gap-3">
                  <button
                    onClick={handleSave}
                    disabled={isSaving || !editForm.firstName.trim() || !editForm.lastName.trim()}
                    className="flex items-center gap-2 px-4 py-2 bg-emerald-600 hover:bg-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-md transition-colors"
                  >
                    {isSaving ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Save className="w-4 h-4" />
                    )}
                    {isSaving ? 'Salvataggio...' : 'Salva'}
                  </button>
                  <button
                    onClick={handleCancelEdit}
                    disabled={isSaving}
                    className="flex items-center gap-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 disabled:opacity-50 text-white rounded-md transition-colors"
                  >
                    <X className="w-4 h-4" />
                    Annulla
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* User Events Section */}
        <div className="max-w-3xl mx-auto mt-8">
          <UserEventsSection />
        </div>
      </main>
      <Footer />
    </div>
  );
}
