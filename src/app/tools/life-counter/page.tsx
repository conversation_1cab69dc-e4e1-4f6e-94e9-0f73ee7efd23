import { Metadata } from "next";
import LifeCounter from "@/components/tools/LifeCounter";
import { MainLayout } from "@/components/layout/MainLayout";

export const metadata: Metadata = {
  title: "Life Counter | Lega Pauper Adriatica",
  description: "Contatore di punti vita per i giocatori di Magic: The Gathering",
};

export default function LifeCounterPage() {
  return (
    <MainLayout currentPage="tools">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl md:text-3xl font-bold text-center mb-6 text-white">
          Life Counter
        </h1>
        <p className="text-blue-200 text-center mb-8">
          Usa questo strumento per tenere traccia dei punti vita durante le tue partite di Magic: The Gathering
        </p>
        <div className="bg-black/20 backdrop-blur-sm rounded-lg border border-blue-500/30 overflow-hidden">
          <div className="h-[calc(100vh-300px)] min-h-[500px]">
            <LifeCounter />
          </div>
        </div>
      </div>
    </MainLayout>
  );
} 