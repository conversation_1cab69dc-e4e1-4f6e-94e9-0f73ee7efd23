import fs from "fs";
import path from "path";
import React from "react";
import { Header } from "@/components/shared/Header";
import { Footer } from "@/components/shared/Footer";
import { Download } from "lucide-react";

function sanitizeInline(s: string): string {
  if (!s) return s;
  let out = s;
  // Remove markdown emphasis and underline attributes
  out = out.replace(/\*\*(.+?)\*\*/g, '$1');
  out = out.replace(/__(.+?)__/g, '$1');
  out = out.replace(/\*(.+?)\*/g, '$1');
  out = out.replace(/_(.+?)_/g, '$1');
  // Remove attribute blocks like {.underline}
  out = out.replace(/\{[^}]*\}/g, '');
  // Replace markdown links [text](url) with just text
  out = out.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '$1');
  // Remove leftover single bracket wrappers [text]
  out = out.replace(/\[([^\]]+)\]/g, '$1');
  // Unescape common sequences
  out = out.replace(/\\'/g, "'");
  return out;
}

function renderMarkdown(md: string) {
  const lines = md.split("\n");
  const elements: React.ReactNode[] = [];
  let i = 0;
  let inCode = false;

  while (i < lines.length) {
    const line = lines[i];
    if (!line && line !== "") { i++; continue; }
    const raw = line;
    const trimmed = raw.trim();

    // Handle fenced code blocks (skip)
    if (trimmed.startsWith("```")) {
      inCode = !inCode;
      i++;
      continue;
    }
    if (inCode) { i++; continue; }

    // Skip HTML comments placeholders
    if (trimmed === "<!-- -->") { i++; continue; }

    // Headings
    const hMatch = trimmed.match(/^(#{1,6})\s+(.*)$/);
    if (hMatch) {
      const level = hMatch[1].length;
      const text = sanitizeInline(hMatch[2]);
      const common = "mt-4 mb-2";
      if (level === 1) {
        elements.push(
          <h1 key={`h1-${i}`} className="text-2xl sm:text-3xl font-bold mb-4 sm:mb-6 text-center">
            {text}
          </h1>
        );
      } else if (level === 2) {
        elements.push(
          <h2 key={`h2-${i}`} className={`text-xl sm:text-2xl font-semibold text-blue-300 ${common}`}>
            {text}
          </h2>
        );
      } else {
        elements.push(
          <h3 key={`h3-${i}`} className={`text-lg font-semibold text-blue-300 ${common}`}>
            {text}
          </h3>
        );
      }
      i++;
      continue;
    }

    // Ordered list
    const olMatch = trimmed.match(/^\d+\.\s+(.*)$/);
    if (olMatch) {
      const items: string[] = [];
      while (i < lines.length) {
        const t = lines[i].trim();
        const m = t.match(/^\d+\.\s+(.*)$/);
        if (!m) break;
        items.push(sanitizeInline(m[1]));
        i++;
      }
      elements.push(
        <ol key={`ol-${i}`} className="list-decimal list-inside pl-4 space-y-1 text-blue-100">
          {items.map((it, idx) => (
            <li key={idx}>{it}</li>
          ))}
        </ol>
      );
      continue;
    }

    // Unordered list (handle patterns like "-   text" or "- text")
    const isUlItem = /^-\s+/.test(trimmed) || /^-\s{2,}/.test(trimmed) || /^\*\s+/.test(trimmed);
    if (isUlItem) {
      const items: string[] = [];
      while (i < lines.length) {
        const t = lines[i].trim();
        if (/^(-|\*)\s+/.test(t) || /^-\s{2,}/.test(t)) {
          const content = t.replace(/^(-|\*)\s+/, "").replace(/^-\s{2,}/, "").replace(/^>\s?/, "");
          items.push(sanitizeInline(content));
          i++;
        } else {
          break;
        }
      }
      elements.push(
        <ul key={`ul-${i}`} className="list-disc list-inside pl-4 space-y-1 text-blue-100">
          {items.map((it, idx) => (
            <li key={idx}>{it}</li>
          ))}
        </ul>
      );
      continue;
    }

    // Paragraph (collect contiguous non-empty, non-list, non-heading lines)
    if (trimmed.length > 0) {
      const parts: string[] = [];
      while (i < lines.length) {
        let t = lines[i];
        const tt = t.trim();
        if (tt === "" || tt.startsWith("#") || /^\d+\./.test(tt) || /^(-|\*)\s+/.test(tt) || /^-\s{2,}/.test(tt) || tt === "<!-- -->" || tt.startsWith("```") ) {
          break;
        }
        // Strip leading blockquote markers
        t = t.replace(/^\s*>\s?/, "");
        parts.push(t.trim());
        i++;
      }
      const text = sanitizeInline(parts.join(" "));
      elements.push(
        <p key={`p-${i}`} className="text-blue-100">
          {text}
        </p>
      );
      continue;
    }

    // Empty line
    i++;
  }

  return <div className="space-y-3 sm:space-y-4">{elements}</div>;
}

export default function RegolamentoPage() {
  const mdPath = path.join(process.cwd(), "src/content/regolamento.md");
  let md = "";
  try {
    md = fs.readFileSync(mdPath, "utf8");
  } catch {
    md = "";
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 text-white">
      <Header currentPage="regolamento" />

      <main className="container mx-auto p-4 sm:p-6">
        <div className="max-w-4xl mx-auto">
          <div className="flex justify-end mb-3 sm:mb-4">
            <a
              href="/docs/regolamento.pdf"
              download="regolamento.pdf"
              className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg font-medium transition-colors text-sm border border-blue-500/30"
              aria-label="Scarica il regolamento in PDF"
            >
              <Download size={16} />
              Scarica PDF
            </a>
          </div>
          <div className="bg-black/20 backdrop-blur-sm rounded-xl border border-blue-500/30 p-6">
            {renderMarkdown(md)}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
