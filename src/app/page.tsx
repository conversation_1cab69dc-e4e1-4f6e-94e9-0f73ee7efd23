"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function Home() {
  const router = useRouter();

  useEffect(() => {
    // Redirect immediato al calendario
    router.replace('/calendar');
  }, [router]);

  // Mostra un loading spinner durante il redirect
  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 text-white">
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-400 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-blue-200">Reindirizzamento al calendario...</p>
        </div>
      </div>
    </div>
  );
}

