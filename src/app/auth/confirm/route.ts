import { NextRequest, NextResponse } from 'next/server';
import type { EmailOtpType } from '@supabase/supabase-js';
import { createServerClient } from '@supabase/ssr';

function sanitizeNext(next: string | null, origin: string, envOrigin?: string): string {
  if (!next || typeof next !== 'string') return '/';
  try {
    // Allow relative path starting with '/'
    if (next.startsWith('/')) {
      if (next.startsWith('//')) return '/';
      return next;
    }
    const nextUrl = new URL(next);
    const base = new URL(origin);
    const envBase = envOrigin ? new URL(envOrigin) : null;
    const isSameAsRequest = nextUrl.origin === base.origin;
    const isSameAsEnv = envBase ? nextUrl.origin === envBase.origin : false;
    if (isSameAsRequest || isSameAsEnv) {
      const path = nextUrl.pathname + (nextUrl.search ?? '');
      return path || '/';
    }
    return '/';
  } catch {
    return '/';
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const token_hash = searchParams.get('token_hash');
  const type = searchParams.get('type') as EmailOtpType | null;
  const rawNext = searchParams.get('next');
  const origin = request.nextUrl.origin;
  const envSiteUrl = process.env.NEXT_PUBLIC_SITE_URL;
  let next = sanitizeNext(rawNext, origin, envSiteUrl);

  if (token_hash && type) {
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      throw new Error('Missing Supabase env vars');
    }

    // Capture cookies Supabase wants to set, so we can attach them to the redirect response
const toSet: { name: string; value: string; options?: Record<string, unknown> }[] = [];
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              toSet.push({ name, value, options });
            });
          },
        },
      }
    );

    const { error } = await supabase.auth.verifyOtp({
      type,
      token_hash,
    });

    if (!error) {
      try {
        await supabase.rpc('ensure_player_for_current_user');
      } catch {
        // Non-fatal
      }

      if (!rawNext) {
        try {
          const { data: isAdminRes } = await supabase.rpc('is_admin');
          const isAdmin = Boolean(isAdminRes);
          next = isAdmin ? '/admin/dashboard' : '/profile';
        } catch {
          next = '/profile';
        }
      }

      if (next.startsWith('/admin')) {
        try {
          const { data: isAdminRes } = await supabase.rpc('is_admin');
          const isAdmin = Boolean(isAdminRes);
          if (!isAdmin) {
            next = '/';
          }
        } catch {
          next = '/';
        }
      }

      const baseOrigin = envSiteUrl ? new URL(envSiteUrl).origin : origin;
      const resolved = new URL(next, baseOrigin);
      const finalRes = NextResponse.redirect(resolved, { status: 303 });
      // Apply all cookies captured during verifyOtp to the redirect response
      toSet.forEach(({ name, value, options }) => {
        try {
          finalRes.cookies.set(name, value, options);
        } catch {
          // ignore cookie set errors
        }
      });
      return finalRes;
    } else {
      const errorUrl = new URL('/auth/error', request.nextUrl.origin);
      errorUrl.searchParams.set('error_description', 'Could not authenticate user');
      errorUrl.searchParams.set('error', error.message);
      const finalRes = NextResponse.redirect(errorUrl, { status: 303 });
      toSet.forEach(({ name, value, options }) => {
        try {
          finalRes.cookies.set(name, value, options);
        } catch {}
      });
      return finalRes;
    }
  } else {
    // Fallback flow: some email templates route to Supabase verify endpoint first, which
    // sets the session and redirects here without token params. In that case, try to
    // read the session from cookies and proceed.
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      throw new Error('Missing Supabase env vars');
    }

    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          // setAll is not needed here; we don't expect Supabase to set cookies in this branch
          setAll() {},
        },
      }
    );

    const { data: userRes } = await supabase.auth.getUser();
    if (userRes.user) {
      try {
        await supabase.rpc('ensure_player_for_current_user');
      } catch {}

      if (!rawNext) {
        try {
          const { data: isAdminRes } = await supabase.rpc('is_admin');
          const isAdmin = Boolean(isAdminRes);
          next = isAdmin ? '/admin/dashboard' : '/profile';
        } catch {
          next = '/profile';
        }
      }

      if (next.startsWith('/admin')) {
        try {
          const { data: isAdminRes } = await supabase.rpc('is_admin');
          const isAdmin = Boolean(isAdminRes);
          if (!isAdmin) {
            next = '/';
          }
        } catch {
          next = '/';
        }
      }

      const baseOrigin = envSiteUrl ? new URL(envSiteUrl).origin : origin;
      const resolved = new URL(next, baseOrigin);
      return NextResponse.redirect(resolved, { status: 303 });
    }

    const errorUrl = new URL('/auth/error', request.nextUrl.origin);
    errorUrl.searchParams.set('error_description', 'Missing token or type');
    return NextResponse.redirect(errorUrl);
  }
}
