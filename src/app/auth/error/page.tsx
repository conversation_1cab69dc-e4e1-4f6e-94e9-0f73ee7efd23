'use client';

import { useSearchParams } from 'next/navigation';
import { useEffect, useState, Suspense } from 'react';

function AuthErrorContent() {
  const searchParams = useSearchParams();
  const [error, setError] = useState<string | null>(null);
  const [errorDescription, setErrorDescription] = useState<string | null>(null);

  useEffect(() => {
    setError(searchParams.get('error'));
    setErrorDescription(searchParams.get('error_description'));
  }, [searchParams]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-sky-900 to-blue-900 p-4">
      <div className="max-w-md w-full bg-white/10 backdrop-blur-md rounded-lg p-6 shadow-xl border border-white/20">
        <div className="text-center">
          <div className="mb-4">
            <div className="mx-auto w-12 h-12 bg-red-500/20 rounded-full flex items-center justify-center">
              <svg
                className="w-6 h-6 text-red-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
          </div>
          
          <h1 className="text-xl font-semibold text-white mb-2">
            Errore di Autenticazione
          </h1>
          
          <p className="text-sky-200 mb-4">
            {errorDescription || 'Si è verificato un errore durante il processo di autenticazione.'}
          </p>

          {error && (
            <div className="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
              <p className="text-sm text-red-200 font-mono">
                {error}
              </p>
            </div>
          )}

          <div className="space-y-3">
            <a
              href="/login"
              className="block w-full bg-sky-600 hover:bg-sky-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
            >
              Torna al Login
            </a>
            
            <p className="text-sm text-sky-300">
              Se il problema persiste, contatta l&apos;amministratore del sistema.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function AuthError() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-sky-900 to-blue-900 p-4">
        <div className="max-w-md w-full bg-white/10 backdrop-blur-md rounded-lg p-6 shadow-xl border border-white/20">
          <div className="text-center">
            <div className="animate-pulse text-sky-300">Caricamento...</div>
          </div>
        </div>
      </div>
    }>
      <AuthErrorContent />
    </Suspense>
  );
}
