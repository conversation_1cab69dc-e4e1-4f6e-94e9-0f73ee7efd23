Classifica:
    [X] aggiungere indicativo dei primi 8 giocatori, tipo un piccolo highlight
    [ ] calcolare punteggio sulla base delle migliori 7 tappe
    [ ] a parimerito, va su chi ha più punti nella classifica generale
    [ ] tenere nella UI sia punteggio delle migliori 7 che generale
    [X] migliorare visualizzazione da mobile, vedere da cellulare
    [ ] scaricare la classifica in formato csv o pdf (non urgente)
    [ ] poter modificare le tappe passate ed i result spostando le persone col drag and drop 
        su o giu

Homepage:
    [ ] ristrutturare la homepage per mostrare anteprima lega e anteprima pagine essenziali

Calendario:
    [X] fare in modo che non ci si possa iscrivere ai tornei passati
    [X] i tornei passati devono presentare un link al resoconto dell'evento in se
    [ ] attivare logica modale preiscrizioni eventi con link temporaneo mandato per email
        che permetta di modificare la decklist fino al momento dello start della tappa
    [ ] aggiungere visualizzazione eventi a schede tipo lpversilia.it
    [X] se l'utente admin (quindi accesso CRUD ai tornei) ha cliccato su una casella e sceglie
        di creare un nuovo evento, il nuovo evento deve avere già quella data lì della casella
        selezionata
    [X] per creare un evento basta anche solo poter fare doppio click (o doppio tap su mobile)
        sulla casella relativa e si apre il modale di creazione
    [ ] i report degli eventi precedenti devono avere i dati reali e non mock, e devono essere
        modificabili come gli eventi in se
    [ ] inserire possibilità di filtrare sul calendario solo eventi di un certo store
    [ ] migliorare i parametri di default nel modale creazione di un nuovo evento

Header:
    [X] inserire logo lega al posto del nome
    [X] inserire all'estrema destra il pulsante area riservata per login admin (su mobile 
        si trova sotto i link rapidi, sotto una barretta a dividere)

Footer:
    [X] inserire le social icons

Generale:
    [X] aggiungere autenticazione supabase (per ora solo admin)
    [X] aggiungere pannello admin per creare e modificare eventi (CRUD)
    [ ] aggiungere pagina negozi
    [ ] valutare aggiunta icone ai link rapidi dell'header
    [ ] modificare colore tema ui/ux con colori lega
    [ ] aggiungere temi chiaro e scuro (default quello coi colori della lega)
    [ ] impostare color picker per eventi o cose così (non urgente)
    [X] quando ci si logga si deve rimanere sulla pagina corrente, non andare alla homepage
    [X] creare pannello admin accessibile da menu a tendina post login
    [ ] raggruppare i risultati nelle varie seasons, creare una season, eliminarla eccetera
 
Pannello Admin:
    [X] inserire possibilità di cambiare password
    [ ] inserire logs di ciò che avviene, chi si iscrive/disiscrive dagli eventi e tutte
        le operazioni CRUD relative agli eventi tessi o ai risultati degli stessi

Tools:
    [X] creare sezione dropdown tools con vari tools
    [ ] tool: tier list creator - classico video tier list mazzi pauper
    [X] tool: life counter app per vite, veleni, mana pool ecc

Life Counter:
    [ ] eliminare i +5/-5
    [ ] aggiungere log sessione
    [ ] settare localstorage o cache per salvare il risultato
    [ ] rendere la pagina accessibile anche offline
    [ ] inserire contatore piccolo vite sopra vite generali per tenere il contatore
    [ ] attivare pulsanti + e - premendo ovunque nella colonna relativa
    [ ] aggiungere poison ed energy con swipe in alto oppure a destra
    [ ] collassare barra centrale che aperta mostra icone che fanno cose tipo reset vite
    [ ] allungare un po il pannello vite

Mana Counter:
    [ ] aggiungere piccole icone di reset su ogni card
    [ ] togliere il cerchi colorati
    [ ] impostare uno sfondo a tema col colore del mana
    [ ] ingrandire un po gli elementi
    [ ] migliorare grafica generale tipo attaccare di più i vari box del mana