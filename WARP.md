# WARP.md

This file provides guidance to WA<PERSON> (warp.dev) when working with code in this repository.

## Project Overview

This is a Next.js 15 application for Lega Pauper Adriatica, a Magic: The Gathering Pauper tournament platform. It uses Supabase for backend services, React Query for state management, and Tailwind CSS for styling.

## Common Development Commands

### Development
```bash
npm run dev              # Start development server on localhost:3000
npm run build           # Build for production
npm run start           # Start production server
npm run lint            # Run ESLint
npm run create-admin    # Create admin user (requires ts-node)
```

### Supabase Operations
```bash
# Apply migrations (using Supabase MCP tools)
# Use the apply_migration MCP tool for DDL operations
# Use execute_sql MCP tool for data queries

# Generate TypeScript types from database schema
# Use the generate_typescript_types MCP tool

# Recent onboarding system migrations:
# - add_onboarding_system_fixed: Adds first_name, last_name, onboarding_completed fields
# - update_auth_display_name_on_onboarding: Updates auth.users display name
```

### Testing Single Components
```bash
# Run development server and navigate to specific routes:
# /calendar - Tournament calendar
# /classifica - Player rankings
# /regolamento - Rules
# /admin/dashboard - Admin dashboard
# /tools/life-counter - Life counter tool
# /tools/mana-counter - Mana counter tool
```

## Architecture Overview

### Directory Structure
```
src/
├── app/                    # Next.js App Router pages
│   ├── admin/             # Admin routes (protected)
│   ├── auth/              # Authentication routes
│   ├── calendar/          # Tournament calendar
│   ├── classifica/        # Player rankings
│   ├── regolamento/       # Rules page
│   └── tools/             # Game tools (life/mana counters)
├── components/            # React components
│   ├── admin/            # Admin-specific components
│   ├── auth/             # Authentication components (OnboardingModal)
│   ├── calendar/         # Calendar components (EventFormModal, TournamentDetails)
│   ├── features/         # Feature components (Hero, Countdown, etc.)
│   ├── layout/           # Layout components (ClientLayout, MainLayout)
│   ├── ranking/          # Ranking components (VirtualizedPlayersTable)
│   ├── shared/           # Shared components (Header, Footer, UserMenu)
│   ├── tools/            # Tool components
│   └── ui/               # UI primitives (Modal, Card, Spinner, etc.)
├── lib/                   # Core application logic
│   ├── supabase/         # Supabase client configuration
│   ├── services/         # API service layer (tournaments, players, stores)
│   ├── hooks/            # Custom React hooks
│   ├── providers/        # React context providers
│   └── utils/            # Utility functions
└── types/                # TypeScript type definitions
```

### Key Architectural Patterns

#### 1. Data Fetching Layer
- **Service Pattern**: All Supabase queries are centralized in `src/lib/services/`
- **React Query**: Data fetching hooks in `src/lib/hooks/` use React Query for caching
- **Type Safety**: Database types are generated from Supabase schema in `src/lib/supabase/types.ts`

#### 2. Authentication & Authorization
- **Supabase Auth**: Managed through `AuthProvider` context with onboarding integration
- **Player Onboarding**: Automatic first/last name collection via `OnboardingModal`
- **Admin Protection**: `AdminGuard` component and `useIsAdmin` hook
- **RLS Policies**: Database-level security through Supabase Row Level Security
- **Display Names**: Synchronized between `players` table and `auth.users` metadata

#### 3. Component Architecture
- **Virtualization**: Large lists use `react-window` for performance (VirtualizedPlayersTable, VirtualizedResultsTable)
- **Modular Forms**: Complex forms split into smaller components (EventFormModal/)
- **Error Boundaries**: Global error handling with ErrorBoundary component

#### 4. State Management
- **React Query**: Server state management and caching
- **Local State**: Component-level state for UI interactions
- **URL State**: Calendar month/year stored in URL params for shareable links

### Database Schema

Key tables:
- `tournaments`: Tournament events with store references
- `players`: Player profiles with onboarding fields (first_name, last_name, onboarding_completed)
- `stores`: Tournament locations with color schemes
- `tournament_results`: Match results and points
- `tournament_registrations`: Player registrations for tournaments
- `seasons`: Competition seasons (active season filtering)
- `user_roles`: Admin role management
- `audit_log`: Tracks data changes including onboarding completion
- `security_log`: Security events and rate limiting

### Environment Configuration

Required environment variables in `.env.local`:
```
NEXT_PUBLIC_SUPABASE_URL=<your-supabase-url>
NEXT_PUBLIC_SUPABASE_ANON_KEY=<your-anon-key>
# Optional but recommended in dev to avoid host mismatches (e.g., LAN IP)
NEXT_PUBLIC_SITE_URL=http://*************:3000
```

### Supabase Integration

The project uses Supabase MCP tools for database operations:
- `apply_migration`: For DDL operations and schema changes
- `execute_sql`: For data queries and DML operations
- `generate_typescript_types`: To regenerate TypeScript types after schema changes
- `search_docs`: To search Supabase documentation

### Performance Considerations

1. **Virtualization**: Large lists (>50 items) use react-window for efficient rendering
2. **Image Optimization**: OptimizedImage component for lazy loading
3. **Code Splitting**: Lazy loading for heavy components (LazySection)
4. **Query Optimization**: Service layer includes proper joins and indexes

### Development Workflow

1. **Database Changes**: 
   - Create migration in `supabase/migrations/`
   - Apply using `apply_migration` MCP tool
   - Regenerate types with `generate_typescript_types`

2. **Feature Development**:
   - Create service in `src/lib/services/`
   - Add custom hook in `src/lib/hooks/`
   - Build components in appropriate directory
   - Add types in `src/types/`

3. **Admin Features**:
   - Wrap with `AdminGuard` component
   - Check permissions with `useIsAdmin` hook
   - Add audit logging for sensitive operations

4. **Player Onboarding**:
   - New users automatically see `OnboardingModal` after first login
   - Names validated both client-side (Zod) and server-side (SQL regex)
   - Updates both `players` table and `auth.users` display name
   - Rate limited (5 attempts per 10 minutes) with audit logging

### Color System

Store-specific color schemes defined in `src/lib/constants/colors.ts`:
- TRS (The Ragnarok Store)
- FW (Funside Wellness)
- TD (Torre del Drago)
- MC (Mondo Comics)
- Top 8 & Finale events

### Security & Validation

- **Rate Limiting**: Database-level rate limiting for auth and onboarding
- **Input Validation**: Dual-layer validation (client Zod + server SQL regex)
- **SQL Injection Protection**: Parameterized queries with SECURITY DEFINER
- **XSS Protection**: React built-in escaping for all user input
- **Audit Trail**: Complete logging in audit_log and security_log tables
- **RLS Policies**: Row-level security for all sensitive operations

### Styling Guidelines

- Tailwind CSS for styling (v4 with PostCSS)
- Custom animations in `tailwind.config.js`
- Responsive design with mobile-first approach
- Dark mode support through CSS variables

### Key Security Functions

```sql
-- Player creation and management
ensure_player_for_current_user() -- Auto-creates player on first auth
complete_onboarding(p_first, p_last) -- Secure name validation + display name update

-- Rate limiting and security
check_rate_limit(operation, max_requests, window_minutes) -- General rate limiting
magic_link_rate_limit(email) -- Magic link specific rate limiting
log_suspicious_activity(type, details) -- Security event logging
```
