# Project Overview

This is a Next.js project for `legapauperadriatica.com`. It uses Supabase for the backend and TanStack Query for data fetching. The UI is built with Tailwind CSS.

## Building and Running

**Development:**

To run the development server, use the following command:

```bash
npm run dev
```

**Building:**

To build the project for production, use the following command:

```bash
npm run build
```

**Starting:**

To start the production server, use the following command:

```bash
npm run start
```

**Linting:**

To lint the project, use the following command:

```bash
npm run lint
```

## Development Conventions

*   **Framework:** Next.js
*   **Backend:** Supabase
*   **Data Fetching:** TanStack Query
*   **Styling:** Tailwind CSS
*   **UI Components:** Custom components are located in `src/components`.
*   **API Routes:** API routes are located in `src/app/api`.
*   **Database Schema:** The Supabase database schema is defined in `supabase/migrations`.
