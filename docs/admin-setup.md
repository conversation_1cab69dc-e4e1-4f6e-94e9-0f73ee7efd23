# Configurazione Ruolo Amministratore

Questo documento descrive come configurare il ruolo di amministratore per la gestione del calendario tornei.

## Prerequisiti

- Accesso al pannello di amministrazione di Supabase
- Conoscenza dell'ID utente che deve essere promosso ad amministratore

## Passi per la configurazione

### 1. Eseguire le migrazioni SQL

Prima di tutto, assicurati che le migrazioni SQL siano state applicate al database. Queste migrazioni creano:

- La tabella `user_roles` per gestire i ruoli degli utenti
- Le policy RLS (Row Level Security) per proteggere i dati
- Le funzioni per verificare e assegnare i ruoli di amministratore

Le migrazioni si trovano in:
- `supabase/migrations/20240317_add_user_roles.sql`
- `supabase/migrations/20240317_tournament_admin_policies.sql`
- `supabase/migrations/20240317_fix_user_roles_policy.sql` (correzione per le policy RLS)
- `supabase/migrations/20240317_add_admin_check_function.sql` (funzione RPC per verificare il ruolo admin)

Per eseguire le migrazioni:

1. Accedi al pannello di amministrazione di Supabase
2. Vai alla sezione "SQL Editor"
3. Crea una nuova query
4. Copia e incolla il contenuto dei file di migrazione
5. Esegui la query

### 2. Assegnare il ruolo di amministratore

Per assegnare il ruolo di amministratore al primo utente, dovrai eseguire una query SQL direttamente nel pannello di amministrazione di Supabase:

1. Accedi al pannello di amministrazione di Supabase
2. Vai alla sezione "SQL Editor"
3. Crea una nuova query
4. Inserisci il seguente codice SQL, sostituendo `USER_ID` con l'ID dell'utente che vuoi promuovere ad amministratore:

```sql
INSERT INTO public.user_roles (user_id, role)
VALUES ('USER_ID', 'admin');
```

Per ottenere l'ID dell'utente, puoi eseguire questa query:

```sql
SELECT id, email FROM auth.users;
```

### 3. Verificare l'assegnazione del ruolo

Per verificare che il ruolo sia stato assegnato correttamente, puoi eseguire questa query:

```sql
SELECT * FROM public.user_roles WHERE role = 'admin';
```

Oppure utilizzare la funzione RPC:

```sql
SELECT is_user_admin('USER_ID');
```

### 4. Assegnare altri amministratori (opzionale)

Una volta che hai almeno un amministratore, puoi utilizzare l'interfaccia dell'applicazione per gestire gli eventi del calendario.

Per aggiungere altri amministratori in futuro, puoi utilizzare la funzione `add_admin_role` creata durante la migrazione:

```sql
SELECT add_admin_role('NUOVO_USER_ID');
```

Questa funzione può essere eseguita solo da utenti che sono già amministratori.

## Funzionalità disponibili per gli amministratori

Gli amministratori hanno accesso alle seguenti funzionalità nella pagina del calendario:

1. Pulsante "Azioni Calendario" accanto al titolo "Calendario Tornei"
2. Opzione "Aggiungi Evento" per creare nuovi eventi
3. Opzione "Modifica Evento" per modificare eventi esistenti

## Sicurezza

Le policy RLS implementate garantiscono che:

- Solo gli amministratori possono creare, modificare o eliminare eventi
- Tutti gli utenti possono visualizzare gli eventi
- Solo gli amministratori possono gestire i ruoli degli utenti

## Risoluzione dei problemi

### Errore "La tabella user_roles non esiste"

Se vedi un pulsante "Configura Admin" invece del menu "Azioni Calendario", significa che la tabella `user_roles` non è stata creata. Segui questi passaggi:

1. Verifica che le migrazioni SQL siano state eseguite correttamente
2. Esegui manualmente la query per creare la tabella:

```sql
CREATE TABLE IF NOT EXISTS public.user_roles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('admin', 'user')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  UNIQUE (user_id, role)
);

ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;
```

### Errore "Errore durante la verifica del ruolo admin"

Se vedi questo errore nella console del browser:

1. Verifica che l'utente sia autenticato nell'applicazione
2. Controlla che la tabella `user_roles` esista e contenga un record per l'utente corrente
3. Verifica che le policy RLS siano state applicate correttamente
4. Esegui la migrazione `20240317_fix_user_roles_policy.sql` per correggere le policy RLS
5. Esegui la migrazione `20240317_add_admin_check_function.sql` per aggiungere la funzione RPC

### Problemi con le policy RLS

Se le policy RLS non funzionano correttamente, esegui questa query per correggerle:

```sql
-- Rimuovi le policy esistenti sulla tabella user_roles
DROP POLICY IF EXISTS "Users can read their own roles" ON public.user_roles;
DROP POLICY IF EXISTS "Admins can manage all roles" ON public.user_roles;

-- Crea una policy che consenta a tutti gli utenti autenticati di leggere la tabella user_roles
CREATE POLICY "Authenticated users can read all roles" ON public.user_roles
  FOR SELECT
  USING (auth.role() = 'authenticated');

-- Crea una policy che consenta agli amministratori di gestire tutti i ruoli
CREATE POLICY "Admins can manage all roles" ON public.user_roles
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );
```

### Nessun utente admin configurato

Se la tabella esiste ma non ci sono utenti admin:

1. Esegui la query per assegnare il ruolo admin a un utente:

```sql
INSERT INTO public.user_roles (user_id, role)
VALUES ('USER_ID', 'admin');
``` 