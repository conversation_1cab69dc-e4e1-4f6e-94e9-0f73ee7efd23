# 🚀 Code Refactoring Tasks - Lega Pauper Adriatica

## 📊 Project Analysis Summary

**Project:** Lega Pauper Adriatica - Next.js 15 + TypeScript + TailwindCSS + Supabase  
**Total Files:** 72 TypeScript/TSX files  
**Largest Files:** 
- `src/lib/supabase/types.ts` (506 lines)
- `src/components/calendar/EventFormModal.tsx` (484 lines)
- `src/components/calendar/TournamentDetails.tsx` (381 lines)
- `src/components/shared/Header.tsx` (293 lines)

**Review Date:** January 14, 2025  
**Reviewer:** AI Code Reviewer

---

## 🎯 Goals
1. **Improve Code Quality**: Reduce complexity, improve readability
2. **Enhance Maintainability**: Better component organization, reusable code
3. **Optimize Performance**: Code splitting, bundle size reduction
4. **Ensure Scalability**: Proper architecture for future growth

---

## ✅ Task Checklist

### 🔴 Phase 1: Critical Issues (High Priority)

#### 1.1 Remove Console Logs
- [✅] Create logger utility at `src/lib/utils/logger.ts`
- [✅] Replace console.logs in `src/hooks/useIsAdmin.ts` (10 occurrences)
- [✅] Replace console.logs in `src/components/calendar/EventFormModal.tsx` (6 occurrences)
- [✅] Replace console.logs in `src/lib/services/players.ts` (6 occurrences)
- [✅] Replace console.logs in `src/components/calendar/CalendarLegend.tsx` (3 occurrences)
- [✅] Replace console.logs in `src/components/calendar/CalendarActions.tsx` (3 occurrences)
- [✅] Replace console.logs in `src/components/calendar/TournamentDetails.tsx` (1 occurrence)
- [✅] Replace console.logs in `src/components/calendar/CalendarGrid.tsx` (2 occurrences)
- [✅] Replace console.log in `src/components/admin/ProfileForm.tsx` (1 occurrence)
- [✅] Replace console.log in `src/hooks/useAuth.ts` (1 occurrence)
- [✅] Replace console.log in `src/app/classifica/page.tsx` (1 occurrence)
- [✅] Replace console.log in `src/components/calendar/EventSelectionModal.tsx` (1 occurrence)

#### 1.2 Split Large Components

**Header Component (293 lines → ~50 lines each)**
- [✅] Create `src/components/shared/Header/` directory
- [✅] Extract `NavLink.tsx` component
- [✅] Extract `DropdownLink.tsx` component  
- [✅] Extract `MobileMenu.tsx` component
- [✅] Extract `DesktopNavigation.tsx` component
- [✅] Create `constants.ts` for navigation configuration
- [✅] Refactor main `Header.tsx` to use new components
- [✅] Remove old `src/components/shared/Header.tsx`

**EventFormModal Component (484 lines → ~100 lines each)**
- [✅] Create `src/components/calendar/EventFormModal/` directory
- [✅] Extract `EventForm.tsx` component
- [✅] Extract `EventPreview.tsx` component
- [✅] Extract `ConfirmationDialog.tsx` component
- [✅] Create `FormFields/DateTimeFields.tsx`
- [✅] Create `FormFields/StoreSelect.tsx`
- [✅] Create `FormFields/ParticipantFields.tsx`
- [✅] Create `FormFields/PrizeFields.tsx`
- [✅] Create `useEventForm.ts` custom hook
- [✅] Refactor main `EventFormModal.tsx`
- [✅] Remove old component file

**TournamentDetails Component (381 lines → ~150 lines each)**
- [✅] Create `src/components/calendar/TournamentDetails/` directory
- [✅] Extract `TournamentResultsModal.tsx` component
- [✅] Extract `TournamentInfo.tsx` component
- [✅] Extract `TournamentActions.tsx` component
- [✅] Create `useTournamentDetails.ts` hook
- [✅] Refactor main component
- [✅] Remove old component file

#### 1.3 Replace Mock Data
- [✅] Create `src/lib/hooks/useTournamentResults.ts`
- [✅] Create `src/lib/services/tournamentResults.ts`
- [✅] Update Supabase types for tournament results (using existing schema)
- [✅] Replace mock data in `TournamentResultsModal.tsx`
- [✅] Add proper loading and error states
- [✅] Test with real data (build successful)

---

### 🟡 Phase 2: Architecture Improvements (Medium Priority)

#### 2.1 Consolidate Duplicate Utilities
- [✅] Audit duplicate functions in `src/lib/utils.ts` and `src/utils/calendar/helpers.ts`
- [✅] Create `src/lib/utils/calendar.ts` for all calendar utilities
- [✅] Remove `getDaysInMonth` duplicate from `src/lib/utils.ts`
- [✅] Remove `getFirstDayOfMonth` duplicate from `src/lib/utils.ts`
- [✅] Update all imports to use consolidated location
- [✅] Delete redundant files

#### 2.2 Unify Color Management
- [✅] Create `src/lib/constants/colors.ts` with unified color system
- [✅] Create `src/lib/hooks/useStoreColors.ts` hook
- [✅] Migrate STORE_COLORS from `src/lib/utils.ts`
- [✅] Migrate color functions from `src/utils/calendar/constants.ts`
- [✅] Update all color imports throughout the app
- [✅] Remove duplicate color definitions

#### 2.3 Create Shared UI Components
- [✅] Create `src/components/ui/GameButton.tsx` for Life/Mana counters
- [✅] Create `src/components/ui/FormField.tsx` wrapper component
- [✅] Create `src/components/ui/Modal.tsx` base component
- [✅] Create `src/components/ui/Card.tsx` component
- [✅] Create `src/components/ui/LoadingState.tsx` component
- [✅] Create `src/components/ui/EmptyState.tsx` component
- [✅] Create `src/components/ui/ErrorState.tsx` component

#### 2.4 Add Error Boundaries
- [✅] Create `src/components/shared/ErrorBoundary.tsx`
- [✅] Create `src/components/shared/ErrorFallback.tsx`
- [✅] Wrap CalendarContainer with ErrorBoundary
- [✅] Wrap RankingContainer with ErrorBoundary
- [✅] Wrap admin sections with ErrorBoundary
- [✅] Add error logging integration
- [✅] Create `src/lib/utils/error-boundary-utils.tsx` with HOC helpers
- [✅] Add page-level ErrorBoundary to root layout

---

### 🟢 Phase 3: Optimization (Low Priority)

#### 3.1 Improve Type Safety
- [✅] Create `src/types/forms.ts` for form-related types
- [✅] Create `src/types/api.ts` for API response types
- [✅] Add stricter types for event handlers
- [✅] Replace `any` types throughout the codebase
- [✅] Add type guards for runtime checks
- [✅] Create `src/types/database.ts` for database type aliases

#### 3.2 Optimize Bundle Size
- [✅] Implement dynamic import for `EventFormModal`
- [✅] Implement dynamic import for `RegistrationModal`
- [✅] Implement dynamic import for `PlayerHistoryModal`
- [✅] Lazy load admin components
- [✅] Implement dynamic import for `EventSelectionModal`
- [✅] Remove unused dependencies from package.json

#### 3.3 Performance Improvements
- [✅] Add React.memo to pure components
- [✅] Implement useMemo for expensive calculations
- [✅] Add useCallback for event handlers
- [✅] Implement virtual scrolling for large lists
- [✅] Add image optimization with next/image
- [✅] Implement prefetching for navigation

#### 3.4 Style Improvements
- [ ] Extract repeated Tailwind classes to @apply directives
- [ ] Create CSS variables for dynamic styles
- [ ] Remove inline styles from components
- [ ] Create animation utilities
- [ ] Standardize spacing and sizing tokens

---

### 🔵 Phase 4: Testing & Documentation

#### 4.1 Testing
- [ ] Set up Jest and React Testing Library
- [ ] Add unit tests for all utilities
- [ ] Add unit tests for custom hooks
- [ ] Add integration tests for forms
- [ ] Add E2E tests for critical user flows
- [ ] Set up test coverage reporting

#### 4.2 Documentation
- [ ] Add JSDoc comments to all utilities
- [ ] Document component props with interfaces
- [ ] Create README for each major feature
- [ ] Add inline documentation for complex logic
- [ ] Create API documentation
- [ ] Set up Storybook for component library

#### 4.3 Developer Experience
- [ ] Add pre-commit hooks with Husky
- [ ] Configure ESLint rules
- [ ] Add Prettier configuration
- [ ] Create VS Code workspace settings
- [ ] Add GitHub Actions for CI/CD
- [ ] Create development guidelines document

---

## 📈 Progress Tracking

### Overall Progress
- **Total Tasks:** 119
- **Completed:** 89
- **In Progress:** 0
- **Remaining:** 30

### Phase Breakdown
| Phase | Tasks | Completed | Progress |
|-------|-------|-----------|----------|
| Phase 1 (Critical) | 44 | 44 | 100% |
| Phase 2 (Architecture) | 33 | 27 | 82% |
| Phase 3 (Optimization) | 26 | 18 | 69% |
| Phase 4 (Testing & Docs) | 18 | 0 | 0% |

---

## 🚦 Task Status Legend
- [ ] Not Started
- [🔄] In Progress
- [✅] Completed
- [⚠️] Blocked
- [🚫] Cancelled

---

## 📝 Notes & Decisions

### Technical Decisions
1. **Logger**: Use development-only logging to avoid console pollution in production
2. **Component Structure**: Follow feature-based folder structure for better scalability
3. **State Management**: Continue using React Query for server state, consider Zustand for client state if needed
4. **Testing Strategy**: Focus on integration tests over unit tests for better ROI

### Dependencies to Consider
- `react-error-boundary`: For better error handling
- `clsx` + `tailwind-merge`: Already installed, use more extensively
- `@tanstack/react-table`: For better table management if needed
- `react-hook-form` + `zod`: For better form validation

### Performance Targets
- **Lighthouse Score**: Aim for 90+ on all metrics
- **Bundle Size**: Keep main bundle under 200KB
- **Time to Interactive**: Under 3 seconds on 3G
- **Code Coverage**: Aim for 80% test coverage

---

## 🎯 Success Metrics

1. **Code Quality**
   - [ ] No console.logs in production
   - [ ] All components under 200 lines
   - [ ] No duplicate code blocks
   - [ ] TypeScript strict mode enabled

2. **Performance**
   - [ ] 20% reduction in initial bundle size
   - [ ] 30% improvement in Lighthouse score
   - [ ] Under 3s Time to Interactive

3. **Maintainability**
   - [ ] 100% TypeScript coverage
   - [ ] 80% test coverage
   - [ ] All components documented
   - [ ] Consistent code style

---

## 📅 Timeline Estimate

- **Phase 1**: 1 week (High Priority - Start immediately)
- **Phase 2**: 1 week (Can run parallel with Phase 1 completion)
- **Phase 3**: 1 week (After Phase 1 & 2)
- **Phase 4**: Ongoing (Start after Phase 2)

**Total Estimated Time**: 3-4 weeks for full refactoring

---

## 🔄 How to Use This Document

1. **Start with Phase 1** - These are critical issues affecting production
2. **Check off tasks** as you complete them using [✅]
3. **Mark in-progress** tasks with [🔄]
4. **Add notes** for any blockers or decisions made
5. **Update progress** percentages weekly
6. **Review and adjust** priorities based on business needs

---

## 🏁 Getting Started

To begin refactoring:

1. Create a new branch: `git checkout -b refactoring/phase-1`
2. Start with the first task in Phase 1.1
3. Test thoroughly after each change
4. Commit frequently with descriptive messages
5. Create PRs for review after each major section

---

Last Updated: January 14, 2025 (Phase 3.3 Performance Improvements fully completed)
Next Review: January 21, 2025
