import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Allow development from LAN IP (e.g., http://*************:3000)
  // to request internal Next resources without warnings
  allowedDevOrigins: [
    '*************',
    '*************:3000',
    'http://*************:3000',
    'localhost:3000',
    'http://localhost:3000'
  ],
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
};

export default nextConfig;
