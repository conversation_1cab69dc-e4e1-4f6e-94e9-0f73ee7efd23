import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';

export async function middleware(req: NextRequest) {
  // Clone request URL for possible redirect normalization
  const res = NextResponse.next();

  // Ensure Supabase session cookies are refreshed on navigation
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    return res;
  }

  // Only refresh session on idempotent navigations to avoid interfering with auth endpoints
  // and to prevent re-seeding cookies during signout POSTs.
  const method = req.method || 'GET';
  if (method !== 'GET' && method !== 'HEAD') {
    return res;
  }

  const pathname = req.nextUrl.pathname || '';
  // Skip refresh on all auth API endpoints explicitly
  if (pathname.startsWith('/api/auth/')) {
    return res;
  }

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    {
      cookies: {
        get(name: string) {
          return req.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: any) {
          res.cookies.set({ name, value, ...options });
        },
        remove(name: string, options: any) {
          res.cookies.set({ name, value: '', ...options });
        },
      },
    }
  );

  // This will refresh the session if needed (rotating tokens) and set cookies on the response
  await supabase.auth.getUser();

  return res;
}

export const config = {
  matcher: [
    // Run on all application paths to keep session fresh
    '/((?!_next/static|_next/image|favicon.ico|assets/.*).*)',
  ],
};
