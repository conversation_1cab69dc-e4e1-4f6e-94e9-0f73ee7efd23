-- Add color scheme to stores table
ALTER TABLE stores ADD COLUMN color_scheme text DEFAULT 'circolo';

-- Update existing stores with their appropriate color schemes based on names
UPDATE stores 
SET color_scheme = CASE 
    WHEN LOWER(name) LIKE '%fantasy world%' THEN 'fw'
    WHEN LOWER(name) LIKE '%trick room%' OR <PERSON>OWER(name) LIKE '%circolo%' THEN 'circolo'
    WHEN LOWER(name) LIKE '%top%' AND (LOWER(name) LIKE '%8%' OR LOWER(name) LIKE '%deck%') THEN 'top8'
    ELSE 'circolo'
END;

-- Add constraint to ensure only valid color schemes are used
ALTER TABLE stores ADD CONSTRAINT valid_color_scheme 
CHECK (color_scheme IN ('circolo', 'fw', 'top8'));
