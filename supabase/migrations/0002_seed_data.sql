-- Insert stores
insert into stores (id, name, address, city) values
  ('00000000-0000-0000-0000-000000000001', 'Trick Room Store', 'Via Fratelli Bandiera, 74', 'Senigallia'),
  ('00000000-0000-0000-0000-000000000002', 'Top Deck', 'Via S. Giovanni <PERSON>, 32', 'Porto Sant''Elpidio'),
  ('00000000-0000-0000-0000-000000000003', 'Fantasy World', 'Via della Pace, 36', 'Macerata'),
  ('00000000-0000-0000-0000-000000000004', 'Mega Comics', 'Via Indipendenza, 76', 'Civitanova Marche');

-- Insert players
insert into players (id, name) values
  ('00000000-0000-0000-0000-000000000001', '<PERSON>'),
  ('00000000-0000-0000-0000-000000000002', 'Luca Bianchi'),
  ('00000000-0000-0000-0000-000000000003', '<PERSON><PERSON><PERSON>'),
  ('00000000-0000-0000-0000-000000000004', '<PERSON>'),
  ('00000000-0000-0000-0000-000000000005', '<PERSON>');

-- Insert tournaments for Trick Room Store
insert into tournaments (id, title, date, store_id, time_start, time_end, format, max_players, description, prize_pool, price) values
  ('00000000-0000-0000-0000-000000000001', 'Torneo Pauper Standard', '2025-03-13 21:30:00+01', '00000000-0000-0000-0000-000000000001', '21:30', '01:30', 'Swiss (4 round)', 32, 'Torneo settimanale di Pauper presso Trick Room Store. Porta il tuo mazzo e vieni a sfidarci!', 'Premi in buoni negozio', 5.00),
  ('00000000-0000-0000-0000-000000000002', 'Torneo Pauper Standard', '2025-03-20 21:30:00+01', '00000000-0000-0000-0000-000000000001', '21:30', '01:30', 'Swiss (4 round)', 32, 'Torneo settimanale di Pauper presso Trick Room Store. Porta il tuo mazzo e vieni a sfidarci!', 'Premi in buoni negozio', 5.00);

-- Insert tournaments for Top Deck
insert into tournaments (id, title, date, store_id, time_start, time_end, format, max_players, description, prize_pool, price) values
  ('00000000-0000-0000-0000-000000000003', 'Pauper Night', '2025-03-14 21:45:00+01', '00000000-0000-0000-0000-000000000002', '21:45', '01:00', 'Swiss (4 round)', 24, 'Torneo bisettimanale di Pauper presso Top Deck. Ambiente competitivo ma amichevole!', 'Premi in buoni negozio', 5.00),
  ('00000000-0000-0000-0000-000000000004', 'Pauper Night', '2025-03-28 21:45:00+01', '00000000-0000-0000-0000-000000000002', '21:45', '01:00', 'Swiss (4 round)', 24, 'Torneo bisettimanale di Pauper presso Top Deck. Ambiente competitivo ma amichevole!', 'Premi in buoni negozio', 5.00);

-- Insert tournaments for Fantasy World
insert into tournaments (id, title, date, store_id, time_start, time_end, format, max_players, description, prize_pool, price) values
  ('00000000-0000-0000-0000-000000000005', 'Pauper League', '2025-03-17 21:45:00+01', '00000000-0000-0000-0000-000000000003', '21:45', '01:15', 'Swiss (4 round)', 28, 'Tappa della Pauper League presso Fantasy World. I punti accumulati conteranno per la classifica generale.', 'Premi in buoni negozio', 6.00),
  ('00000000-0000-0000-0000-000000000006', 'Pauper League', '2025-03-24 21:45:00+01', '00000000-0000-0000-0000-000000000003', '21:45', '01:15', 'Swiss (4 round)', 28, 'Tappa della Pauper League presso Fantasy World. I punti accumulati conteranno per la classifica generale.', 'Premi in buoni negozio', 6.00);

-- Insert tournaments for Mega Comics
insert into tournaments (id, title, date, store_id, time_start, time_end, format, max_players, description, prize_pool, price) values
  ('00000000-0000-0000-0000-000000000007', 'Pauper Challenge', '2025-03-22 21:45:00+01', '00000000-0000-0000-0000-000000000004', '21:45', '01:30', 'Swiss (5 round)', 36, 'Torneo speciale con meta particolare presso Mega Comics. Premi maggiorati e competizione di alto livello!', 'Premi in buoni negozio', 5.00),
  ('00000000-0000-0000-0000-000000000008', 'Pauper Challenge', '2025-04-05 21:45:00+02', '00000000-0000-0000-0000-000000000004', '21:45', '01:30', 'Swiss (5 round)', 36, 'Torneo speciale con meta particolare presso Mega Comics. Premi maggiorati e competizione di alto livello!', 'Premi in buoni negozio', 5.00);

-- Insert tournament results
insert into tournament_results (tournament_id, player_id, position, points) values
  -- Torneo Pauper Standard (13/03/2025)
  ('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', 1, 10),
  ('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000002', 2, 8),
  ('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000005', 3, 7),
  
  -- Pauper Night (14/03/2025)
  ('00000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000002', 1, 10),
  ('00000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000004', 2, 8),
  ('00000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000001', 3, 7),
  
  -- Pauper League (17/03/2025)
  ('00000000-0000-0000-0000-000000000005', '00000000-0000-0000-0000-000000000003', 1, 10),
  ('00000000-0000-0000-0000-000000000005', '00000000-0000-0000-0000-000000000001', 2, 8),
  ('00000000-0000-0000-0000-000000000005', '00000000-0000-0000-0000-000000000004', 3, 7);

-- Insert tournament registrations (current players)
insert into tournament_registrations (tournament_id, player_id) values
  -- Torneo Pauper Standard (13/03/2025)
  ('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001'),
  ('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000002'),
  ('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000005'),
  
  -- Pauper Night (14/03/2025)
  ('00000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000002'),
  ('00000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000004'),
  ('00000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000001'),
  
  -- Pauper League (17/03/2025)
  ('00000000-0000-0000-0000-000000000005', '00000000-0000-0000-0000-000000000003'),
  ('00000000-0000-0000-0000-000000000005', '00000000-0000-0000-0000-000000000001'),
  ('00000000-0000-0000-0000-000000000005', '00000000-0000-0000-0000-000000000004'); 