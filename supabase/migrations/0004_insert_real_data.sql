-- Elimina prima i risultati dei tornei e le registrazioni
DELETE FROM tournament_results;
DELETE FROM tournament_registrations;

-- Elimina i giocatori esistenti
DELETE FROM players;

-- Inserisci i nuovi giocatori
INSERT INTO players (name) VALUES
  ('<PERSON><PERSON><PERSON><PERSON>'),
  ('<PERSON><PERSON>'),
  ('<PERSON><PERSON>'),
  ('<PERSON>'),
  ('<PERSON>'),
  ('<PERSON>'),
  ('<PERSON><PERSON><PERSON>'),
  ('<PERSON><PERSON>'),
  ('<PERSON><PERSON><PERSON>'),
  ('<PERSON><PERSON><PERSON><PERSON>'),
  ('<PERSON>'),
  ('<PERSON><PERSON><PERSON>'),
  ('<PERSON>'),
  ('<PERSON><PERSON><PERSON>'),
  ('<PERSON>'),
  ('<PERSON><PERSON><PERSON>nte'),
  ('<PERSON>');

-- O<PERSON><PERSON> gli <PERSON> dei tornei per Trick Room (13/03/2025) e Top Deck (14/03/2025)
WITH tournament_ids AS (
  SELECT id, title, date, store_id
  FROM tournaments
  WHERE (date = '2025-03-13 21:30:00+01' AND store_id = '00000000-0000-0000-0000-000000000001')
     OR (date = '2025-03-14 21:45:00+01' AND store_id = '00000000-0000-0000-0000-000000000002')
)

-- Inserisci i risultati dei tornei
INSERT INTO tournament_results (tournament_id, player_id, position, points)
SELECT 
  t.id AS tournament_id,
  p.id AS player_id,
  ROW_NUMBER() OVER (PARTITION BY t.id ORDER BY 
    CASE 
      WHEN t.store_id = '00000000-0000-0000-0000-000000000001' AND t.date = '2025-03-13 21:30:00+01' THEN
        CASE p.name
          WHEN 'Mattia Cavaliere' THEN 1
          WHEN 'Giuliano Tambone' THEN 2
          WHEN 'Simone Monaldi' THEN 3
          WHEN 'Federico Fronzi' THEN 4
          WHEN 'Federico Fabbrini' THEN 5
          WHEN 'Ennio Ciuti' THEN 6
          WHEN 'Edoardo Ballerini' THEN 7
          WHEN 'Mattia Pettinari' THEN 8
          WHEN 'Filippo Zezza' THEN 9
          WHEN 'Sabastiano Mancini' THEN 10
          WHEN 'Luigi Pirrera' THEN 11
          WHEN 'Alessio Turchi' THEN 12
          WHEN 'Francesco Caputo' THEN 13
          ELSE 99
        END
      WHEN t.store_id = '00000000-0000-0000-0000-000000000002' AND t.date = '2025-03-14 21:45:00+01' THEN
        CASE p.name
          WHEN 'Tommaso Riva' THEN 1
          WHEN 'Giuliano Tambone' THEN 2
          WHEN 'Andrea Animobono' THEN 3
          WHEN 'Ennio Ciuti' THEN 4
          WHEN 'Fabrio Bracalente' THEN 5
          WHEN 'Alessio Turchi' THEN 6
          WHEN 'Alessandro Doria' THEN 7
          WHEN 'Simone Monaldi' THEN 8
          ELSE 99
        END
      ELSE 99
    END
  ) AS position,
  CASE 
    WHEN t.store_id = '00000000-0000-0000-0000-000000000001' AND t.date = '2025-03-13 21:30:00+01' THEN
      CASE p.name
        WHEN 'Mattia Cavaliere' THEN 12
        WHEN 'Giuliano Tambone' THEN 9
        WHEN 'Simone Monaldi' THEN 9
        WHEN 'Federico Fronzi' THEN 7
        WHEN 'Federico Fabbrini' THEN 7
        WHEN 'Ennio Ciuti' THEN 6
        WHEN 'Edoardo Ballerini' THEN 6
        WHEN 'Mattia Pettinari' THEN 6
        WHEN 'Filippo Zezza' THEN 6
        WHEN 'Sabastiano Mancini' THEN 6
        WHEN 'Luigi Pirrera' THEN 3
        WHEN 'Alessio Turchi' THEN 3
        WHEN 'Francesco Caputo' THEN 3
        ELSE 0
      END
    WHEN t.store_id = '00000000-0000-0000-0000-000000000002' AND t.date = '2025-03-14 21:45:00+01' THEN
      CASE p.name
        WHEN 'Tommaso Riva' THEN 9
        WHEN 'Giuliano Tambone' THEN 6
        WHEN 'Andrea Animobono' THEN 6
        WHEN 'Ennio Ciuti' THEN 3
        WHEN 'Fabrio Bracalente' THEN 3
        WHEN 'Alessio Turchi' THEN 3
        WHEN 'Alessandro Doria' THEN 3
        WHEN 'Simone Monaldi' THEN 0
        ELSE 0
      END
    ELSE 0
  END AS points
FROM tournament_ids t
CROSS JOIN players p
WHERE 
  (t.store_id = '00000000-0000-0000-0000-000000000001' AND t.date = '2025-03-13 21:30:00+01' AND p.name IN (
    'Giuliano Tambone', 'Mattia Cavaliere', 'Ennio Ciuti', 'Simone Monaldi', 
    'Federico Fronzi', 'Federico Fabbrini', 'Edoardo Ballerini', 'Mattia Pettinari', 
    'Filippo Zezza', 'Sabastiano Mancini', 'Luigi Pirrera', 'Alessio Turchi', 'Francesco Caputo'
  ))
  OR
  (t.store_id = '00000000-0000-0000-0000-000000000002' AND t.date = '2025-03-14 21:45:00+01' AND p.name IN (
    'Giuliano Tambone', 'Ennio Ciuti', 'Simone Monaldi', 'Tommaso Riva', 
    'Andrea Animobono', 'Fabrio Bracalente', 'Alessio Turchi', 'Alessandro Doria'
  ));

-- Inserisci le registrazioni dei tornei
INSERT INTO tournament_registrations (tournament_id, player_id)
SELECT tournament_id, player_id FROM tournament_results; 