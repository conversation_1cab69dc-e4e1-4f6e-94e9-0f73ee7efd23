-- Assicurati che RLS sia abilitato sulla tabella tournaments
ALTER TABLE public.tournaments ENABLE ROW LEVEL SECURITY;

-- Policy per consentire a tutti di leggere i tornei
CREATE POLICY "Anyone can read tournaments" ON public.tournaments
  FOR SELECT
  USING (true);

-- Policy per consentire agli amministratori di inserire nuovi tornei
CREATE POLICY "Admins can insert tournaments" ON public.tournaments
  FOR INSERT
  WITH CHECK (public.is_admin());

-- Policy per consentire agli amministratori di aggiornare i tornei
CREATE POLICY "Admins can update tournaments" ON public.tournaments
  FOR UPDATE
  USING (public.is_admin());

-- Policy per consentire agli amministratori di eliminare i tornei
CREATE POLICY "Admins can delete tournaments" ON public.tournaments
  FOR DELETE
  USING (public.is_admin()); 