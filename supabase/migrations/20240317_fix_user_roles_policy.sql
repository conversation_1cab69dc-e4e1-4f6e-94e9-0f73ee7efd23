-- Rimuovi le policy esistenti sulla tabella user_roles
DROP POLICY IF EXISTS "Users can read their own roles" ON public.user_roles;
DROP POLICY IF EXISTS "Admins can manage all roles" ON public.user_roles;

-- Crea una policy che consenta a tutti gli utenti autenticati di leggere la tabella user_roles
CREATE POLICY "Authenticated users can read all roles" ON public.user_roles
  FOR SELECT
  USING (auth.role() = 'authenticated');

-- Crea una policy che consenta agli amministratori di gestire tutti i ruoli
CREATE POLICY "Admins can manage all roles" ON public.user_roles
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

-- Assicurati che RLS sia abilitato
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY; 