-- Enable UUID extension
create extension if not exists "uuid-ossp";

-- Create stores table
create table if not exists stores (
  id uuid default uuid_generate_v4() primary key,
  name text not null,
  address text not null,
  city text not null,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create players table
create table if not exists players (
  id uuid default uuid_generate_v4() primary key,
  name text not null,
  email text unique,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create tournaments table
create table if not exists tournaments (
  id uuid default uuid_generate_v4() primary key,
  title text not null,
  date timestamp with time zone not null,
  store_id uuid references stores(id) not null,
  time_start time not null,
  time_end time not null,
  format text not null,
  max_players integer not null,
  description text,
  prize_pool text,
  price numeric(5,2) not null,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create tournament_results table
create table if not exists tournament_results (
  id uuid default uuid_generate_v4() primary key,
  tournament_id uuid references tournaments(id) not null,
  player_id uuid references players(id) not null,
  position integer not null,
  points integer not null,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  unique(tournament_id, player_id)
);

-- Create tournament_registrations table
create table if not exists tournament_registrations (
  id uuid default uuid_generate_v4() primary key,
  tournament_id uuid references tournaments(id) not null,
  player_id uuid references players(id) not null,
  registration_date timestamp with time zone default timezone('utc'::text, now()) not null,
  unique(tournament_id, player_id)
);

-- Create RLS policies
alter table stores enable row level security;
alter table players enable row level security;
alter table tournaments enable row level security;
alter table tournament_results enable row level security;
alter table tournament_registrations enable row level security;

-- Stores policies
create policy "Stores are viewable by everyone" 
on stores for select 
using (true);

-- Players policies
create policy "Players are viewable by everyone" 
on players for select 
using (true);

-- Tournaments policies
create policy "Tournaments are viewable by everyone" 
on tournaments for select 
using (true);

-- Tournament results policies
create policy "Tournament results are viewable by everyone" 
on tournament_results for select 
using (true);

-- Tournament registrations policies
create policy "Tournament registrations are viewable by everyone" 
on tournament_registrations for select 
using (true);

-- Create indexes
create index if not exists idx_tournaments_date on tournaments(date);
create index if not exists idx_tournament_results_tournament_id on tournament_results(tournament_id);
create index if not exists idx_tournament_results_player_id on tournament_results(player_id);
create index if not exists idx_tournament_registrations_tournament_id on tournament_registrations(tournament_id);
create index if not exists idx_tournament_registrations_player_id on tournament_registrations(player_id); 