-- Creazione della tabella user_roles
CREATE TABLE IF NOT EXISTS public.user_roles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('admin', 'user')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  UNIQUE (user_id, role)
);

-- Creazione di una policy RLS per la tabella user_roles
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;

-- Policy per consentire agli utenti di leggere i propri ruoli
CREATE POLICY "Users can read their own roles" ON public.user_roles
  FOR SELECT
  USING (auth.uid() = user_id);

-- Policy per consentire agli amministratori di gestire tutti i ruoli
CREATE POLICY "Admins can manage all roles" ON public.user_roles
  USING (
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

-- Funzione per verificare se un utente è amministratore
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Funzione per aggiungere un ruolo amministratore a un utente
CREATE OR REPLACE FUNCTION public.add_admin_role(user_id_param UUID)
RETURNS VOID AS $$
BEGIN
  -- Verifica se l'utente corrente è un amministratore
  IF NOT public.is_admin() THEN
    RAISE EXCEPTION 'Solo gli amministratori possono assegnare ruoli di amministratore';
  END IF;

  -- Aggiungi il ruolo di amministratore all'utente specificato
  INSERT INTO public.user_roles (user_id, role)
  VALUES (user_id_param, 'admin')
  ON CONFLICT (user_id, role) DO NOTHING;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 